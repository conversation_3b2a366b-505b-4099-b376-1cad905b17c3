﻿using Puppeteer.EventSourcing.Libraries;
using System;

namespace GamesEngine.Business.Liquidity.Transactions
{
    public abstract class Deposit:Objeto
    {
        public decimal Amount { get; private set; } 
        public string Address { get; private set; }

        public int Id { get; private set; } //Rubicon: Preguntar si el id debe ser un string

        public Deposit(decimal amount, string address,int id)
        {
            Amount = amount;
            Address = address ?? throw new ArgumentNullException(nameof(address));
            Id = id;
        }

        public string DepositType => this.GetType().Name;

        public void Cancel()
        {
            //Rubicon: Todo hacer logica para borrar del objeto deposito que lo contiene 
        }
    }

    public class PendingDeposit : Deposit
    {
        public PendingDeposit(decimal amount, string address, int id) : base(amount, address, id)
        {
        }
    }

    public class FailedDeposit : Deposit
    {
        public FailedDeposit(decimal amount, string address, int id) : base(amount, address, id)
        {
        }
    }

    public class ConfirmedDeposit : Deposit
    {
        private PendingDeposit pendingDeposit;
        private string transactionId;

        public ConfirmedDeposit(PendingDeposit pendingDeposit, string transactionId, decimal amount, DateTime createdAt) : base(amount, pendingDeposit.Address, pendingDeposit.Id)
        {
            this.pendingDeposit = pendingDeposit;
            this.transactionId = transactionId;

            CreatedAt = createdAt;
        }

        public DateTime CreatedAt { get; private set; }
    }

    public class ProposedDeposit : Deposit
    {
        private ConfirmedDeposit confirmedDeposit;

        public ProposedDeposit(ConfirmedDeposit confirmedDeposit) : base(confirmedDeposit.Amount, confirmedDeposit.Address, confirmedDeposit.Id)
        {
            this.confirmedDeposit = confirmedDeposit;
        }
    }


}
