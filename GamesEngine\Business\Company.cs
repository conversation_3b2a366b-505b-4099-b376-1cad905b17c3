﻿using Confluent.Kafka;
using GamesEngine.Bets;
using GamesEngine.Business.Marketing;
using GamesEngine.Custodian;
using GamesEngine.Customers;
using GamesEngine.Domains;
using GamesEngine.Finance;
using GamesEngine.Gameboards;
using GamesEngine.Gameboards.Lotto;
using GamesEngine.Gameboards.MarchMadness;
using GamesEngine.Games.Lines;
using GamesEngine.Games.Lotto;
using GamesEngine.Games.Tournaments;
using GamesEngine.Location;
using GamesEngine.Marketing.Billboards;
using GamesEngine.Marketing.Campaigns;
using GamesEngine.Marketing.Segments;
using GamesEngine.Messaging;
using GamesEngine.Preferences.Lotto;
using GamesEngine.PurchaseOrders;
using GamesEngine.PurchaseOrders.Activators.Lotto;
using GamesEngine.Resources;
using GamesEngine.Settings;
using GamesEngine.Time;
using GamesEngine.Tools;
using Newtonsoft.Json.Linq;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text;
using System.Threading.Tasks;
using town.connectors.drivers.artemis;
using static GamesEngine.Custodian.RiskRatingTypes;
using static GamesEngine.Exchange.PaymentProcessorsAndActionsByDomains;
using static GamesEngine.Finance.FragmentsCreationBody;
using static GamesEngine.Finance.PaymentChannels;
using static GamesEngine.Games.Lotto.ExcludeSubtickets;
using static GamesEngine.Games.Lotto.NextDatesAccumulator;
using MarchMadnessTournament = GamesEngine.Games.Tournaments.MarchMadnessTournament;
using Player = GamesEngine.Bets.Player;
using Round = GamesEngine.Games.Tournaments.Round;
using TicketType = GamesEngine.Gameboards.Lotto.TicketType;
using Tournaments = GamesEngine.Games.Tournaments.Tournaments;
using ToWinByDrawAndNumber = GamesEngine.Finance.ToWinByDrawAndNumber;

[assembly: InternalsVisibleTo("LinesAPI")]
[assembly: InternalsVisibleTo("LinesBIAPI")]
namespace GamesEngine.Business
{
	[Puppet]
	public sealed class Company : Objeto
	{
		private readonly Products products;
		private readonly PurchaseOrders.Customers customers;
		private readonly Players players = new Players();
		private readonly Orders orders = new Orders();
		private readonly Book book;
		private readonly List<MarchMadnessEdition> rules = new List<MarchMadnessEdition>();

		private readonly Tournaments tournaments;
		private readonly Gameboards.Gameboards gameboards = new Gameboards.Gameboards();
		private Accounting accounting;
		private readonly MarketingKPIs marketingKPIs;
		private Campaigns campaigns;
        private Billboards billboards;
        private RuntimeSettings settings = new RuntimeSettings();
		private CustomerTypes customerTypes;
		private RiskRatingTypes riskRatingTypes;
		private readonly Settlers settlers;
		private readonly Sales sales;
		private readonly int id;

        internal Exchange.Marketplace Marketplace { get; set; }

        internal Accounting Accounting
		{
			get { return accounting; }
			set
			{
				if (accounting != null) throw new GameEngineException("Accounting gateway can only be set once. It is supposed to be define at the begining of the dairy.");
				this.accounting = value;
			}
		}

        internal GrafanaExternalAPI GrafanaExternalAPI
        {
            get
            {
                return GrafanaExternalAPI.Instance;
            }
        }

        internal void IndexByIdentifier(Customer customer, string newIdentifier)
		{
			if (String.IsNullOrWhiteSpace(newIdentifier)) throw new ArgumentNullException(nameof(newIdentifier));
			if (customer == null) throw new ArgumentNullException(nameof(customer));
			var normalizedNewId = newIdentifier.ToUpper();

			customers.IndexByIdentifier(customer, normalizedNewId);
		}

		internal void IndexByIdentifier(Customer customer, string oldIdentifier, string newIdentifier)
		{
			if (String.IsNullOrWhiteSpace(newIdentifier)) throw new ArgumentNullException(nameof(newIdentifier));
			if (String.IsNullOrWhiteSpace(oldIdentifier)) throw new ArgumentNullException(nameof(oldIdentifier));
			if (customer == null) throw new ArgumentNullException(nameof(customer));
			var normalizedOldId = oldIdentifier.ToUpper();
			var normalizedNewId = newIdentifier.ToUpper();
			if (normalizedOldId == normalizedNewId) throw new GameEngineException("Normalized identifiers must be different");

			customers.IndexByIdentifier(customer, normalizedOldId, normalizedNewId);
		}


		internal Company()
		{
			tournaments = new Tournaments(this);
			customers = new PurchaseOrders.Customers(this);
			products = new Products(this);
			marketingKPIs = new MarketingKPIs(this, this.players);
			this.book = new Book(this);
			this.sales = new Sales(this);
			this.settlers = new Settlers(this);
			System = new System(this);
			this.id = 1;//TODO Ahorita todos los actores asumen solo una Company
		}

		internal int Id
		{
			get
			{
				return this.id;
			}
		}

		internal Book Book
		{
			get
			{
				return this.book;
			}
		}

		internal Sales Sales
		{
			get
			{
				return this.sales;
			}
		}

		internal System System { get; }

		internal Campaigns Campaigns
		{
			get
			{
				if (this.campaigns == null) this.campaigns = new Campaigns(this);
				return this.campaigns;
			}
		}

        internal Billboards Billboards
        {
            get
            {
                if (this.billboards == null) this.billboards = new Billboards(this);
                return this.billboards;
            }
        }

        public RuntimeSettings Settings
		{
			get
			{
				return this.settings;
			}
		}

		internal Customer CreateCustomer(string accountNumber, Agents agent)
		{
			if (String.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException("The accountNumber is required.");
			//TODO Arreglar> como se va a definir el ID a partir de la cuenta y la cuenta a partir del ID, mejorar la politica
			Customer result = customers.CreateCustomer(accountNumber, agent);
			return result;
		}

		internal Customer GetOrCreateCustomerById(string accountNumber)
		{
			if (String.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException("The accountNumber is required.");
			//TODO Arreglar> como se va a definir el ID a partir de la cuenta y la cuenta a partir del ID, mejorar la politica
			Customer result = customers.GetOrCreateCustomerByAccountNumber(accountNumber);
			return result;
		}

		[Obsolete("Backward compatibility for V1.0, Use CustomerByAccountNumber(string accountNumber) instead")]
		internal Customer GetOrCreateCustomerById(string accountNumber, string token)
		{
			if (String.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException("The accountNumber is required.");
			if (String.IsNullOrWhiteSpace(token)) throw new ArgumentNullException("The token is required.");

			return GetOrCreateCustomerById(accountNumber);
		}

		internal Customer CustomerByPlayerId(string playerId)
		{
			if (String.IsNullOrWhiteSpace(playerId)) throw new ArgumentNullException("The player id is required.");
			var accountNumber = (string)this.EncryptionHelper.Decrypt(playerId);
			return CustomerByAccountNumber(accountNumber);
		}

		internal Customer CustomerByAccountNumber(string accountNumber)
		{
			if (String.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException("The accountNumber is required.");
			accountNumber = accountNumber.Trim().ToLower();

			return customers.CustomerByAccountNumber(accountNumber);
		}

		internal Customer CustomerByIdentifier(string identificationDocumentNumber)
		{
			if (String.IsNullOrWhiteSpace(identificationDocumentNumber)) throw new ArgumentNullException("The accountNumber is required.");
			identificationDocumentNumber = identificationDocumentNumber.Trim().ToLower();

			return customers.SearchCustomerByIdentifier(identificationDocumentNumber);
		}

		internal bool ExistsCustomer(string accountNumber)
		{
			if (string.IsNullOrWhiteSpace(accountNumber)) return false;

			var result = customers.Contains(accountNumber);
			return result;
		}

		internal bool ExistsCustomerByIdentifier(string identifier)
		{
			if (string.IsNullOrWhiteSpace(identifier)) return false;

			var result = customers.ContainsByIdentifier(identifier);
			return result;
		}

		internal void AddPlayer(Bets.Player player)
		{
			if (player == null) throw new ArgumentNullException(nameof(player));
			this.players.Add(player);
		}

		internal Customer CustomerByPlayer(Bets.Player player)
		{
			if (player == null) throw new ArgumentNullException(nameof(player));
			var result = customers.CustomerByPlayer(player);
			return result;
		}

		public Product GetOrCreateProductById(int id)
		{
			Product product;
			if (products.HasProductWithId(id))
			{
				product = products.ProductById(id);
			}
			else
			{
				product = new Product(this, id);
				products.Add(product);
			}
			return product;
		}

		internal void PayBetsOf(MarchMadnessTournament tournament)
		{
			if (!tournament.IsEnded()) throw new GameEngineException("The tournament has not ended");
			book.CalculatePrize(tournament);
		}

		internal Products Products()
		{
			return products;
		}

		internal Gameboards.Gameboards Gameboards
		{
			get
			{
				return gameboards;
			}
		}

		[Obsolete("Do not use, it is for legacy purposes")]
		internal int NextOrderNumber
		{
			get
			{
				return orders.NextOrderNumber();
			}
		}

		internal int IdentityOrderNumber
		{
			get
			{
				return orders.IdentityOrderNumber;
			}
		}

		[Obsolete("DO NOT USE BECAUSE IT CREATES A WRONG ORDER NUMBER")]
		internal Order GetNewOrder(Customer customer)
		{
			if (this.sales.CurrentStore == Store.EMPTY) throw new GameEngineException("Store is still empty, it needs to be configured to sale");

			return GetNewOrder(customer, orders.NextOrderNumber());
		}

		private bool irNormal = true;
		private int orders210349Counter = 0;
		private int newNumberOrder = 210556;

		[Obsolete("Do not use this, it is for legacy purposes")]
		internal Order GetNewOrder(Customer customer, int orderNumber)
		{
			if (customer == null) throw new ArgumentNullException(nameof(customer));
			if (!customers.Contains(customer)) throw new GameEngineException("Customer is unknown. Order only can be delivered to know customers.");
			if (this.sales.CurrentStore == Store.EMPTY) throw new GameEngineException("Store is still empty, it needs to be configured to sale");

			if (irNormal && orderNumber == 210349)
			{
				orders210349Counter++;
				if (orders210349Counter >= 2) irNormal = false;
			}

			if (irNormal)
			{
				Order order = new OrderCart(customer, orderNumber, Coinage.Coin(Currencies.CODES.USD));
				return order;
			}
			else
			{
				newNumberOrder++;
				Order order = new OrderCart(customer, newNumberOrder, Coinage.Coin(Currencies.CODES.USD));
				return order;
			}
		}

		[Obsolete("Do not use this, it is for legacy purposes")]
		internal Order NewOrder(Customer customer, int orderNumber)
		{
			if (customer == null) throw new ArgumentNullException(nameof(customer));
			if (!customers.Contains(customer)) throw new GameEngineException("Customer is unknown. Order only can be delivered to know customers.");
			if (this.sales.CurrentStore == Store.EMPTY) throw new GameEngineException("Store is still empty, it needs to be configured to sale");

			Order order = new OrderCart(customer, orderNumber, Coinage.Coin(Currencies.CODES.USD));
			return order;
		}

		[Obsolete("Do not use this, it is for legacy purposes.")]
		internal Order NewOrder(Customer customer, int orderNumber, string currencyCode)
		{
			if (customer == null) throw new ArgumentNullException(nameof(customer));
			if (customer.Company != this) throw new GameEngineException($"Customer {customer.AccountNumber} belongs to a different Company");
			if (!customers.Contains(customer)) throw new GameEngineException("Customer is unknown. Order only can be delivered to know customers.");
			if (this.sales.CurrentStore == Store.EMPTY) throw new GameEngineException("Store is still empty, it needs to be configured to sale");

			Order order = new OrderCart(customer, orderNumber, Coinage.Coin(currencyCode));
			return order;
		}

		[Obsolete("Do not use this, it is for legacy purposes.")]
		internal Order NewOrder(Customer customer, int orderNumber, Currencies.CODES currencyCode)
		{
			if (customer == null) throw new ArgumentNullException(nameof(customer));
			if (customer.Company != this) throw new GameEngineException($"Customer {customer.AccountNumber} belongs to a different Company");
			if (!customers.Contains(customer)) throw new GameEngineException("Customer is unknown. Order only can be delivered to know customers.");
			if (this.sales.CurrentStore == Store.EMPTY) throw new GameEngineException("Store is still empty, it needs to be configured to sale");

			Order order = new OrderCart(customer, orderNumber, Coinage.Coin(currencyCode));
			return order;
		}

		internal OrderCart NewOrder(Customer customer, int orderNumber, Coin coin, int authorizationId)
		{
			if (customer == null) throw new ArgumentNullException(nameof(customer));
			if (customer.Company != this) throw new GameEngineException($"Customer {customer.AccountNumber} belongs to a different Company");
			if (!customers.Contains(customer)) throw new GameEngineException("Customer is unknown. Order only can be delivered to know customers.");
			if (this.sales.CurrentStore == Store.EMPTY) throw new GameEngineException("Store is still empty, it needs to be configured to sale");

			OrderCart order = new OrderCart(customer, orderNumber, coin);
			order.AuthorizationId = authorizationId;
			//TODO pasarle la tienda que es, porque en PurchaseOrder esta quemado.

			return order;
		}

		[Obsolete("Do not use it is for legacy purposes")]
		internal int NextBetNumber()
		{
			var result = book.NextBetNumber();
			return result;
		}

		internal int IdentitytBetNumber
		{
			get
			{
				var result = book.IdentitytBetNumber;
				return result;
			}
		}

		internal void IncreaseBetNumber()
		{
			book.IncreaseBetNumbersToSkip();
		}

		internal bool CustomerHasFoundsFor(Order order, decimal founds)
		{
			if (order == null) throw new ArgumentNullException(nameof(order));
			if (order.State != Order.OrderState.CART && order.State != Order.OrderState.PLACED) throw new GameEngineException("Founds confirmation only can be done for non placed orders.");

			var hasFounds = order.Customer.HasFoundsFor(order, founds);
			return hasFounds;
		}

		[Obsolete("This method is only for backward compatibility, DO NOT use it.")]
		internal OrderCompleted Sale(bool itIsThePresent, OrderCart order, DateTime now, decimal founds)
		{
			return Sale(itIsThePresent, order, now, founds, "Brackets: Bracket Purchase", Domains.Domains.DefaultDomain, "");
		}

		internal OrderCompleted Sale(bool itIsThePresent, OrderCart order, DateTime now, decimal founds, string descriptionUnused, Domain domain, string additionalInfoUnused)
		{
			if (domain == null) throw new ArgumentNullException(nameof(domain));
			if (!this.Sales.ContainsDomain(domain)) throw new GameEngineException($"Domain {domain.Url} is not available to perform sales.");
			if (!domain.Visible) throw new GameEngineException($"Domain {domain.Url} is not visible then sale cannot proceed");
			if (order == null) throw new ArgumentNullException(nameof(order));
			if (!order.HasItems) throw new GameEngineException($"Order {order.Number} has not added any items yet.");
			if (!order.CanBeModified) throw new GameEngineException("This order was already processed.");
			if (!customers.Contains(order.Customer)) throw new GameEngineException("Customer is unknown. Order only can be delivered to know customers.");
			var productsOnOrder = order.ProductsOnOrder();
			if (!products.ContainsAll(productsOnOrder)) throw new GameEngineException("Order contains products that are unknown. Only products on sale can be bought.");
			//TODO: Falta chequear o ponerle la fecha hora

			var hasFounds = order.Customer.HasFoundsFor(order, founds);
			if (!hasFounds) throw new GameEngineException("Customer has insufficient founds to buy this order.");

			orders.Add(order);
			OrderPlaced orderPlaced = (OrderPlaced)order.Purchase();
			OrderPayed orderReadyToDispatch = (OrderPayed)orderPlaced.ConfirmSale();
			orderReadyToDispatch.Domain = domain;
			OrderCompleted orderCompleted = (OrderCompleted)orderReadyToDispatch.Dispatch(itIsThePresent, now);
			accounting.Purchase(itIsThePresent, orderCompleted, now);

			orders.Replace(order, orderCompleted);
			var customer = orderCompleted.Customer;
			Integration.Kafka.Send(itIsThePresent, Integration.Kafka.TopicForCustomers,
				new CustomerSaleMessage(customer.Identifier,
				customer.AccountNumber, 
				this.sales.CurrentStore.Id,
				domain,
				now, 
				Currency.Factory(orderCompleted.CurrencyCode, orderCompleted.Total()),
				(int)customer.Player.Agent));

			return orderCompleted;
		}


		internal OrderCompleted SaleBracket(bool itIsThePresent, OrderCart order, DateTime now, decimal founds, Domain domain)
		{
			if (domain == null) throw new ArgumentNullException(nameof(domain));
			if (order == null) throw new ArgumentNullException(nameof(order));
			if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (founds <= 0) throw new GameEngineException($"{nameof(founds)} {founds} must be greater than zero");

			var nextBracketId = gameboards.NextSequence();
			var description = $"Brackets: Bracket #{nextBracketId} Purchase";
			var additionalInfo = $"Bracket #{nextBracketId} price {order.Total()}";
			return Sale(itIsThePresent, order, now, founds, description, domain, additionalInfo);
		}

		internal OrderCompleted SaleJoin(bool itIsThePresent, OrderCart order, DateTime now, decimal founds, MarchMadnessBracket bracket, int roomId, Domain domain)
		{
			if (domain == null) throw new ArgumentNullException(nameof(domain));
			if (order == null) throw new ArgumentNullException(nameof(order));
			if (bracket == null) throw new ArgumentNullException(nameof(bracket));
			if (founds <= 0) throw new GameEngineException($"{nameof(founds)} {founds} must be greater than zero");

			var description = $"Brackets: Bracket #{bracket.Id} Group #{roomId} Joined";
			const int MAXIMUM_NAME_LENGTH_TO_SHOW = 10;
			var firstLettersOfName = bracket.Name.Length > MAXIMUM_NAME_LENGTH_TO_SHOW ? $"{bracket.Name.Substring(0, MAXIMUM_NAME_LENGTH_TO_SHOW)}..." : bracket.Name;
			var additionalInfo = $"Bracket #{bracket.Id} name {firstLettersOfName} price {order.Total()}";
			return Sale(itIsThePresent, order, now, founds, description, domain, additionalInfo);
		}

		internal OrderCompleted SaleGroup(bool itIsThePresent, OrderCart order, DateTime now, decimal founds, MarchMadnessBracket bracket, Domain domain)
		{
			if (domain == null) throw new ArgumentNullException(nameof(domain));
			if (order == null) throw new ArgumentNullException(nameof(order));
			if (bracket == null) throw new ArgumentNullException(nameof(bracket));
			if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (founds <= 0) throw new GameEngineException($"{nameof(founds)} {founds} must be greater than zero");

			var tournament = tournaments.GetMarchMadnessTournamentOf(now.Year);
			if (order.Customer.Player.HasReachedLimitOfGroupCreation(now.Year, tournament.CurrentRound())) throw new GameEngineException($"Player has reached limit of group creation per round of {tournament.MaxAmountOfGroupsToBuyPerRound}");
			var rules = GetMarchMadnessEdition(bracket.Year);
			var nextRoomId = rules.NextRoomConsecutive();
			var description = $"Brackets: Bracket #{bracket.Id} Group #{nextRoomId} Joined";
			const int MAXIMUM_NAME_LENGTH_TO_SHOW = 10;
			var firstLettersOfName = bracket.Name.Length > MAXIMUM_NAME_LENGTH_TO_SHOW ? $"{bracket.Name.Substring(0, MAXIMUM_NAME_LENGTH_TO_SHOW)}..." : bracket.Name;
			var additionalInfo = $"Bracket #{bracket.Id} name {firstLettersOfName} price {order.Total()}";
			return Sale(itIsThePresent, order, now, founds, description, domain, additionalInfo);
		}

		private decimal TicketCost(decimal ticketAmount, int includeSubTickets, ExcludeSubtickets excludedSubtickets, DateTime dateSelected, State state)
		{
			return TicketCost(ticketAmount, includeSubTickets - excludedSubtickets.Count(state, dateSelected));
		}

		private decimal TicketCost(decimal ticketAmount, int includeSubTickets)
		{
			var ticketCost = ticketAmount * includeSubTickets;
			Commons.ValidateAmount(ticketCost);
			return ticketCost;
		}

		private decimal TicketCost(decimal ticketAmount, bool powerPlay)
		{
			if (HasWrongDecimals(ticketAmount)) throw new GameEngineException($"Invalid ticket cost of Power play ticket");

			var ticketCost = 0m;
			if (powerPlay)
			{
				const decimal FACTOR_OF_A_POWER_PLAY_TICKET = 2m;
				ticketCost = TicketCost(ticketAmount * FACTOR_OF_A_POWER_PLAY_TICKET, 1);
			}
			else
			{
				ticketCost = TicketCost(ticketAmount, 1);
			}
			return ticketCost;
		}

		private bool HasWrongDecimals(decimal amount)
		{
			var intPart = (int)(amount * 100m);
			var hasWrongDecimals = amount * 100m - (decimal)intPart > 0;
			return hasWrongDecimals;
		}

		internal static TicketType ToTicketType(int pickType, char gameType)
		{
			switch (pickType)
			{
				case 2:
					switch (gameType)
					{
						case 'S':
							return TicketType.P2S;
						case 'B':
							return TicketType.P2B;
					}
					break;
				case 3:
					switch (gameType)
					{
						case 'S':
							return TicketType.P3S;
						case 'B':
							return TicketType.P3B;
					}
					break;
				case 4:
					switch (gameType)
					{
						case 'S':
							return TicketType.P4S;
						case 'B':
							return TicketType.P4B;
					}
					break;
				case 5:
					switch (gameType)
					{
						case 'S':
							return TicketType.P5S;
						case 'B':
							return TicketType.P5B;
					}
					break;
			}
			throw new GameEngineException($"Pick type {pickType} and game type {gameType} is unknown.");
		}

		internal readonly static ExcludeSubtickets DEFAULT_WITHOUT_EXCLUDES = new ExcludeSubtickets();

        [Obsolete("This method is only for backward compatibility with no withFireBalls, DO NOT use it.")]
        internal Order CreateTicketFullOrder(Player player, string states, string dates, string numbers, string selectionMode, decimal ticketAmount, string includedNumbersForInput, string gameType, OrderCart order, Product product, NextDatesAccumulator nextDatesAccumulator, Domain domain)
		{
            string[] statesArr = string.IsNullOrWhiteSpace(states) ? new string[] { } : states.Split(',');
            string withFireBalls = string.Join(",", statesArr.Select(x => "false"));

            return CreateTicketOrder(this.lotto900, player, states, dates, withFireBalls, numbers, selectionMode, ticketAmount, includedNumbersForInput, DEFAULT_WITHOUT_EXCLUDES, gameType, order, product, nextDatesAccumulator, domain);
        }

		internal Order CreateTicketFullOrder(Player player, string states, string dates, string withFireBalls, string numbers, string selectionMode, decimal ticketAmount, string includedNumbersForInput, string gameType, OrderCart order, Product product, NextDatesAccumulator nextDatesAccumulator, Domain domain)
		{
            return CreateTicketOrder(this.lotto900, player, states, dates, withFireBalls, numbers, selectionMode, ticketAmount, includedNumbersForInput, DEFAULT_WITHOUT_EXCLUDES, gameType, order, product, nextDatesAccumulator, domain);
		}

		[Obsolete("This method is only for backward compatibility with no withFireBalls, DO NOT use it.")]
		internal Order CreateTicketOrder(PicksLotteryGame lotto900, Player player, string states, string dates, string numbers, string selectionMode, decimal ticketAmount, string includedNumbersForInput, ExcludeSubtickets excludedSubtickets, string gameType, OrderCart order, Product product, NextDatesAccumulator nextDatesAccumulator, Domain domain)
		{
            string[] statesArr = string.IsNullOrWhiteSpace(states) ? new string[] { } : states.Split(',');
            string withFireBalls = string.Join(",", statesArr.Select(x => "false"));

			return CreateTicketOrder(lotto900, player, states, dates, withFireBalls, numbers, selectionMode, ticketAmount, includedNumbersForInput, excludedSubtickets, gameType, order, product, nextDatesAccumulator, domain);
        }

		internal Order CreateTicketOrder(PicksLotteryGame lotto900, Player player, string states, string dates, string withFireBalls, string numbers, string selectionMode, decimal ticketAmount, string includedNumbersForInput, ExcludeSubtickets excludedSubtickets, string gameType, OrderCart order, Product product, NextDatesAccumulator nextDatesAccumulator, Domain domain)
		{
			if (player == null) throw new ArgumentNullException(nameof(player));
			if (string.IsNullOrWhiteSpace(states)) throw new ArgumentNullException(nameof(states));
			if (string.IsNullOrWhiteSpace(dates)) throw new ArgumentNullException(nameof(dates));
			if (string.IsNullOrWhiteSpace(withFireBalls)) throw new ArgumentNullException(nameof(withFireBalls));

			if (ticketAmount <= 0) throw new GameEngineException("Ticket amount must be greater than zero");
			if (product.Id < 1 && product.Id > 8) throw new GameEngineException("This is not a valid product for picks tickets");

			string[] numbersArr = string.IsNullOrWhiteSpace(numbers) ? new string[] { } : numbers.Split(',');
			string[] datesArr = string.IsNullOrWhiteSpace(dates) ? new string[] { } : dates.Split(',');
			string[] statesArr = string.IsNullOrWhiteSpace(states) ? new string[] { } : states.Split(',');
			string[] withFireBallsArr = string.IsNullOrWhiteSpace(withFireBalls) ? new string[] { } : withFireBalls.Split(',');

			if (withFireBallsArr.Length != statesArr.Length) throw new GameEngineException($"Invalid withFireBalls length {withFireBallsArr.Length} for states length {statesArr.Length}");

			string[] includedNumbersForInputArr = string.IsNullOrWhiteSpace(includedNumbersForInput) ? new string[] { } : includedNumbersForInput.Split(',');
			int includeSubTickets;
			int pickType = 0;

			switch (selectionMode)
			{
				case "balls":
					if (numbersArr.All(x => x == "*")) throw new GameEngineException($"Pattern selected with {numbersArr.Length} * is not valid");
					includeSubTickets = 1;
					foreach (string num in numbersArr)
					{
						includeSubTickets *= new Pattern(num).Count;
						pickType++;
					}
					break;
				case "SingleInputMultipleAmount":
				case "MultipleInputSingleAmount":
					excludedSubtickets.ExcludeNumbersNoIncluded(includedNumbersForInputArr);
					includeSubTickets = includedNumbersForInputArr.Length;
					pickType = includedNumbersForInputArr[0].Length;
					break;
				default:
					throw new GameEngineException($"Invalid selection mode {selectionMode}");
			}
            var keyName = LotteryGamesPool.GameTypes.KeyName(selectionMode, pickType);
            if (!LotteryGamesPool.GameTypes.IsEnabled(keyName, domain)) throw new GameEngineException($"Game type {keyName} is not enabled for domain {domain.Url}");

            TicketPickActivator activator;
			OrderCart newOrder = order;
			var ticketCreator = lotto900.GetTicketCreator(ToTicketType(pickType, gameType[0]));
            for (int indexStateAbb = 0; indexStateAbb < statesArr.Length; indexStateAbb++)
            {
				string stateAbb = statesArr[indexStateAbb];

                bool withFireBall;
                if (!bool.TryParse(withFireBallsArr[indexStateAbb], out withFireBall)) throw new GameEngineException($"Invalid withFireBall value {withFireBall}");

                var state = lotto900.GetState(stateAbb);
				var lottery = lotto900.GetLottery(pickType, state);

				if (withFireBall)
				{
					if (lottery is LotteryForPicks lotteryPicksBase)
					{
                        if (!lotteryPicksBase.IsFireBallTurnedOn) throw new GameEngineException($"Lottery in state {lottery.State.Name} and pick {lottery.IdOfLottery} does not have fireball");
                    }
                    else
					{
                        throw new GameEngineException($"Lottery in state {lottery.State.Name} and pick {lottery.IdOfLottery} is not a Pick type.");
                    }
				}

                var nextDates = nextDatesAccumulator.TakeDates(state, withFireBall, datesArr.Length, lottery.IdOfLottery);
                foreach (var nextDate in nextDates)
				{
                    if (lottery.IsGradedAt(nextDate.Date)) throw new GameEngineException($"Lottery was graded at {nextDate.Date}");
					if (lottery.IsNoActionAt(nextDate.Date)) throw new GameEngineException($"Lottery was as no action at {nextDate.Date}");

                    var ticketCost = 0m;
                    if (!excludedSubtickets.IsExclusionAllFor(state, nextDate.DesiredDate))
                    {
                        ticketCost = TicketCost(ticketAmount, includeSubTickets, excludedSubtickets, nextDate.DesiredDate, state);
                    }
                    var areAllExcludes = ticketCost == 0;
                    if (areAllExcludes) continue;
					activator = ticketCreator(player, state, nextDate, withFireBall, ticketCost, selectionMode, numbersArr, includedNumbersForInputArr, excludedSubtickets, domain, 0);
					newOrder.Add(product, activator, ticketCost);
				}
            }
            return newOrder;
        }

        internal TicketOrderBuilderMultipleInputMultipleAmount PrepareTicketOrderWithMultipleAuthorization(Player player, string selectionMode, string ruleType, OrderCart order, NextDatesAccumulator nextDatesAccumulator, Domain domain)
        {
            if (player == null) throw new ArgumentNullException(nameof(player));
			if (string.IsNullOrWhiteSpace(selectionMode)) throw new ArgumentNullException(nameof(selectionMode));
			if (string.IsNullOrWhiteSpace(ruleType)) throw new ArgumentNullException(nameof(ruleType));
            if (order == null) throw new ArgumentNullException(nameof(order));
            if (nextDatesAccumulator == null) throw new ArgumentNullException(nameof(nextDatesAccumulator));
            if (domain == null) throw new ArgumentNullException(nameof(domain));

			return new TicketOrderBuilderMultipleInputMultipleAmount(this, player, selectionMode, ruleType[0], order, nextDatesAccumulator, domain);
        }

        internal TicketOrderBuilder PrepareTicketOrderWithMultipleAuthorization(Player player, string selectionMode, decimal ticketAmount, OrderCart order, NextDatesAccumulator nextDatesAccumulator, Domain domain)
        {
            if (player == null) throw new ArgumentNullException(nameof(player));
            if (ticketAmount <= 0) throw new GameEngineException("Ticket amount must be greater than zero");
            if (order == null) throw new ArgumentNullException(nameof(order));
			if (nextDatesAccumulator == null) throw new ArgumentNullException(nameof(nextDatesAccumulator));
			if (domain == null) throw new ArgumentNullException(nameof(domain));
            if (!LotteryGamesPool.GameTypes.IsEnabled("pickify", domain)) throw new GameEngineException($"Game type pickify is not enabled for domain {domain.Url}");

            return new TicketOrderBuilder(this, player, selectionMode, ticketAmount, order, nextDatesAccumulator, domain);
        }

        internal readonly static string[] EmptyNumberForBalls = new string[]{ };

		[Obsolete("Do not use, since Fireball implementation wont be used")]
        internal Order CreateTicketOrderWithMultipleAuthorization(Player player, string states, string dates, string selectionMode, decimal ticketAmount, List<string> includedNumbersForInputArr, string gameType, OrderCart order, Product product, NextDatesAccumulator nextDatesAccumulator, Domain domain, List<string> toWins)
		{
            string[] statesArr = string.IsNullOrWhiteSpace(states) ? new string[] { } : states.Split(',');
            string withFireBalls = string.Join(",", statesArr.Select(x => "false"));

			return CreateTicketOrderWithMultipleAuthorization(player, states, dates, withFireBalls, selectionMode, ticketAmount, includedNumbersForInputArr, gameType, order, product, nextDatesAccumulator, domain);
        }

		internal Order CreateTicketOrderWithMultipleAuthorization(Player player, string states, string dates, string withFireBalls, string selectionMode, decimal ticketAmount, List<string> includedNumbersForInputArr, 
			string gameType, OrderCart order, Product product, NextDatesAccumulator nextDatesAccumulator, Domain domain)
		{
			if (string.IsNullOrWhiteSpace(states)) throw new ArgumentNullException(nameof(states));
			if (string.IsNullOrWhiteSpace(dates)) throw new ArgumentNullException(nameof(dates));
			if (string.IsNullOrWhiteSpace(withFireBalls)) throw new ArgumentNullException(nameof(withFireBalls));

			if (player == null) throw new ArgumentNullException(nameof(player));
			if (ticketAmount <= 0) throw new GameEngineException("Ticket amount must be greater than zero");
			if (product.Id < 1 && product.Id > 8) throw new GameEngineException("This is not a valid product for picks tickets");

			string[] datesArr = string.IsNullOrWhiteSpace(dates) ? new string[] { } : dates.Split(',');
			string[] statesArr = string.IsNullOrWhiteSpace(states) ? new string[] { } : states.Split(',');
            string[] withFireBallsArr = string.IsNullOrWhiteSpace(withFireBalls) ? new string[] { } : withFireBalls.Split(',');

            if (withFireBallsArr.Length != statesArr.Length) throw new GameEngineException($"Invalid withFireBalls length {withFireBallsArr.Length} for states length {statesArr.Length}");

            int includeSubTickets;
			int pickType;

			switch (selectionMode)
			{
				case "SingleInputMultipleAmount":
				case "MultipleInputSingleAmount":
					includeSubTickets = includedNumbersForInputArr.Count;
					pickType = includedNumbersForInputArr[0].Length;
					break;
				default:
					throw new GameEngineException($"Invalid selection mode {selectionMode}");
			}
            var keyName = LotteryGamesPool.GameTypes.KeyName(selectionMode, pickType);
            if (!LotteryGamesPool.GameTypes.IsEnabled(keyName, domain)) throw new GameEngineException($"Game type {keyName} is not enabled for domain {domain.Url}");

            TicketPickActivator activator;
			OrderCart newOrder = order;
			var ticketCreator = lotto900.GetTicketCreator(ToTicketType(pickType, gameType[0]));
			int index = 0;
            for (int stateIndex = 0; stateIndex < statesArr.Length; stateIndex++)
            {
                string stateAbb = statesArr[stateIndex];

                bool withFireBall;
                if (!bool.TryParse(withFireBallsArr[stateIndex], out withFireBall)) throw new GameEngineException($"Invalid withFireBall value {withFireBall}");

                var state = lotto900.GetState(stateAbb);
				var lottery = lotto900.GetLottery(pickType, state);
                var nextDates = nextDatesAccumulator.TakeDates(state, withFireBall, datesArr.Length, lottery.IdOfLottery);

                foreach (var nextDate in nextDates)
                {
                    if (lottery.IsGradedAt(nextDate.Date)) throw new GameEngineException($"Lottery was graded at {nextDate.Date}");
					if (lottery.IsNoActionAt(nextDate.Date)) throw new GameEngineException($"Lottery was as no action at {nextDate.Date}");

					foreach (var singleNumber in includedNumbersForInputArr)
					{
						activator = ticketCreator(player, state, nextDate, withFireBall, ticketAmount, selectionMode, EmptyNumberForBalls, new[] { singleNumber }, DEFAULT_WITHOUT_EXCLUDES, domain, index);
						newOrder.Add(product, activator, ticketAmount);
						index++;
					}
				}
			}
            
            return newOrder;
		}

        [Obsolete("Do not use, since Fireball implementation wont be used")]
        internal Order CreateTicketOrderWithMultipleAuthorization(Player player, string states, string dates, string selectionMode, decimal ticketAmount, List<string> includedNumbersForInputArr,
			ExcludeSubtickets exclusionsByPurchase, string gameType, OrderCart order, Product product, NextDatesAccumulator nextDatesAccumulator, Domain domain, List<string> toWins)
		{
            string[] statesArr = string.IsNullOrWhiteSpace(states) ? new string[] { } : states.Split(',');
            string withFireBalls = string.Join(',', statesArr.Select(x => "false"));

            return CreateTicketOrderWithMultipleAuthorization(player, states, dates, withFireBalls, selectionMode, ticketAmount, includedNumbersForInputArr, exclusionsByPurchase, gameType, order, product, nextDatesAccumulator, domain);
        }

        internal Order CreateTicketOrderWithMultipleAuthorization(Player player, string states, string dates, string withFireBalls, string selectionMode, decimal ticketAmount, List<string> includedNumbersForInputArr, 
			ExcludeSubtickets exclusionsByPurchase, string gameType, OrderCart order, Product product, NextDatesAccumulator nextDatesAccumulator, Domain domain)
        {
			if (string.IsNullOrWhiteSpace(states)) throw new ArgumentNullException(nameof(states));
			if (string.IsNullOrWhiteSpace(dates)) throw new ArgumentNullException(nameof(dates));
			if (string.IsNullOrWhiteSpace(withFireBalls)) throw new ArgumentNullException(nameof(withFireBalls));

            if (player == null) throw new ArgumentNullException(nameof(player));
            if (ticketAmount <= 0) throw new GameEngineException("Ticket amount must be greater than zero");
            if (product.Id < 1 && product.Id > 8) throw new GameEngineException("This is not a valid product for picks tickets");

            string[] datesArr = string.IsNullOrWhiteSpace(dates) ? new string[] { } : dates.Split(',');
            string[] statesArr = string.IsNullOrWhiteSpace(states) ? new string[] { } : states.Split(',');
			string[] withFireBallsArr = string.IsNullOrWhiteSpace(withFireBalls) ? new string[] { } : withFireBalls.Split(',');

            if (withFireBallsArr.Length != statesArr.Length) throw new GameEngineException($"Invalid withFireBalls length {withFireBallsArr.Length} for states {statesArr.Length}");

            int includeSubTickets;
            int pickType;
            switch (selectionMode)
            {
                case "SingleInputMultipleAmount":
                case "MultipleInputSingleAmount":
                    exclusionsByPurchase.ExcludeNumbersNoIncluded(includedNumbersForInputArr);
                    includeSubTickets = includedNumbersForInputArr.Count;
                    pickType = includedNumbersForInputArr[0].Length;
                    break;
                default:
                    throw new GameEngineException($"Invalid selection mode {selectionMode}");
            }
            var keyName = LotteryGamesPool.GameTypes.KeyName(selectionMode, pickType);
            if (!LotteryGamesPool.GameTypes.IsEnabled(keyName, domain)) throw new GameEngineException($"Game type {keyName} is not enabled for domain {domain.Url}");

            TicketPickActivator activator;
            OrderCart newOrder = order;
            var ticketCreator = lotto900.GetTicketCreator(ToTicketType(pickType, gameType[0]));
			int index = 0;
			for (int indexStateAbb = 0; indexStateAbb < statesArr.Length; indexStateAbb++)
            {
                string stateAbb = statesArr[indexStateAbb];

				bool withFireBall;
                if (!bool.TryParse(withFireBallsArr[indexStateAbb], out withFireBall)) throw new GameEngineException($"Invalid withFireBall value {withFireBall}");

                var state = lotto900.GetState(stateAbb);
                var lottery = lotto900.GetLottery(pickType, state);
                var nextDates = nextDatesAccumulator.TakeDates(state, withFireBall, datesArr.Length, lottery.IdOfLottery);                

                foreach (var nextDate in nextDates)
                {
                    if (lottery.IsGradedAt(nextDate.Date)) throw new GameEngineException($"Lottery was graded at {nextDate.Date}");
                    if (lottery.IsNoActionAt(nextDate.Date)) throw new GameEngineException($"Lottery was as no action at {nextDate.Date}");

                    if (!exclusionsByPurchase.IsExclusionAllFor(state, nextDate.Date))
					{
                        foreach (var singleNumber in includedNumbersForInputArr)
						{
							if (!exclusionsByPurchase.IsNumberExcluded(state, nextDate.Date, singleNumber))
                            {
                                activator = ticketCreator(player, state, nextDate, withFireBall, ticketAmount, selectionMode, EmptyNumberForBalls, new[] { singleNumber }, DEFAULT_WITHOUT_EXCLUDES, domain, index);
                                newOrder.Add(product, activator, ticketAmount);
                                index++;
                            }
						}
					}
                }
            }
            return newOrder;
        }

        [Obsolete("Do not use, since Fireball implementation wont be used")]
        internal Order CreateTicketOrderWithMultipleAuthorization(Player player, string states, string dates, string selectionMode, List<string> betAmounts, List<string> includedNumbersForInputArr, string gameType,
			OrderCart order, Product product, NextDatesAccumulator nextDatesAccumulator, Domain domain, List<string> toWins)
		{
            string[] statesArr = string.IsNullOrWhiteSpace(states) ? new string[] { } : states.Split(',');
            string withFireBalls = string.Join(',', statesArr.Select(x => "false"));

			return CreateTicketOrderWithMultipleAuthorization(player, states, dates, withFireBalls, selectionMode, betAmounts, includedNumbersForInputArr, gameType, order, product, nextDatesAccumulator, domain);
        }
            
		internal Order CreateTicketOrderWithMultipleAuthorization(Player player, string states, string dates, string withFireBalls, string selectionMode, List<string> betAmounts, List<string> includedNumbersForInputArr, 
			string gameType, OrderCart order, Product product, NextDatesAccumulator nextDatesAccumulator, Domain domain)
        {
			if (string.IsNullOrWhiteSpace(states)) throw new ArgumentNullException(nameof(states));
			if (string.IsNullOrWhiteSpace(dates)) throw new ArgumentNullException(nameof(dates));
			if (string.IsNullOrWhiteSpace(withFireBalls)) throw new ArgumentNullException(nameof(withFireBalls));

            if (player == null) throw new ArgumentNullException(nameof(player));
            if (product.Id < 1 && product.Id > 8) throw new GameEngineException("This is not a valid product for picks tickets");

            string[] datesArr = string.IsNullOrWhiteSpace(dates) ? new string[] { } : dates.Split(',');
            string[] statesArr = string.IsNullOrWhiteSpace(states) ? new string[] { } : states.Split(',');
			string[] withFireBallsArr = string.IsNullOrWhiteSpace(withFireBalls) ? new string[] { } : withFireBalls.Split(',');

            if (withFireBallsArr.Length != statesArr.Length) throw new GameEngineException($"Invalid withFireBalls length {withFireBallsArr.Length} for states length {statesArr.Length}");

            int includeSubTickets;
            int pickType;

            switch (selectionMode)
            {
                case "SingleInputMultipleAmount":
                case "MultipleInputSingleAmount":
                    includeSubTickets = includedNumbersForInputArr.Count;
                    pickType = includedNumbersForInputArr[0].Length;
                    break;
                default:
                    throw new GameEngineException($"Invalid selection mode {selectionMode}");
            }
            var keyName = LotteryGamesPool.GameTypes.KeyName(selectionMode, pickType);
            if (!LotteryGamesPool.GameTypes.IsEnabled(keyName, domain)) throw new GameEngineException($"Game type {keyName} is not enabled for domain {domain.Url}");

            TicketPickActivator activator;
            OrderCart newOrder = order;
            var ticketCreator = lotto900.GetTicketCreator(ToTicketType(pickType, gameType[0]));
			int index = 0;
            for (int indexStateAbb = 0; indexStateAbb < statesArr.Length; indexStateAbb++)
            {
                string stateAbb = statesArr[indexStateAbb];

                bool withFireBall;
                if (!bool.TryParse(withFireBallsArr[indexStateAbb], out withFireBall)) throw new GameEngineException($"Invalid withFireBall value {withFireBall}");

                var state = lotto900.GetState(stateAbb);
                var lottery = lotto900.GetLottery(pickType, state);
                var nextDates = nextDatesAccumulator.TakeDates(state, withFireBall, datesArr.Length, lottery.IdOfLottery);
                foreach (var nextDate in nextDates)
                {
                    if (lottery.IsGradedAt(nextDate.Date)) throw new GameEngineException($"Lottery was graded at {nextDate.Date}");
                    if (lottery.IsNoActionAt(nextDate.Date)) throw new GameEngineException($"Lottery was as no action at {nextDate.Date}");

                    foreach (var singleNumber in includedNumbersForInputArr)
                    {
						var betAmount = decimal.Parse(betAmounts[index]);
                        activator = ticketCreator(player, state, nextDate, withFireBall, betAmount, selectionMode, EmptyNumberForBalls, new[] { singleNumber }, DEFAULT_WITHOUT_EXCLUDES, domain, index);
                        newOrder.Add(product, activator, betAmount);
                        index++;
                    }
                }
            }
            return newOrder;
        }

		[Puppet]
		public class TotalOrderAndExclusionsByToWin : Objeto
		{
			internal decimal TotalOrder { get; private set; }
			internal bool IsValidToRetryPurchase { get; private set; }
			internal string ErrorMessage
			{
				get
				{
					var errorMessage = string.Empty;
					if (TotalOrder == 0 ||
						(exclusionsForSingleSelection != null && !exclusionsForSingleSelection.IsEmpty()) ||
						(exclusionsForMultiSelectionBalls != null && !exclusionsForMultiSelectionBalls.IsEmpty()) ||
						(exclusionsForMultiSelectionInputs != null && !exclusionsForMultiSelectionInputs.IsEmpty())
						)
					{
						errorMessage = "Maximum amount per combination exceeded";
					}
					return errorMessage;
				}
			}

			private ExcludeSubtickets exclusionsForSingleSelection;
			internal IEnumerable<Exclude> ExclusionsForSingleSelection
			{
				get
				{
					var result = exclusionsForSingleSelection.SubticketsToExclude().ToList();
					return result;
				}
			}
			private ExcludeSubtickets exclusionsForMultiSelectionBalls;
			internal IEnumerable<Exclude> ExclusionsForMultiSelectionBalls
			{
				get
				{
					var result = exclusionsForMultiSelectionBalls.SubticketsToExclude().ToList();
					return result;
				}
			}
			private ExcludeSubtickets exclusionsForMultiSelectionInputs;
			internal IEnumerable<Exclude> ExclusionsForMultiSelectionInputs
			{
				get
				{
					var result = exclusionsForMultiSelectionInputs.SubticketsToExclude().ToList();
					return result;
				}
			}

			private int RemainingTickets { get; set; }
			internal bool IsMultiSelection { get; }
			internal List<ToWinByDrawAndNumber> Tickets { get; set; } = new List<ToWinByDrawAndNumber>();

			internal TotalOrderAndExclusionsByToWin(int expectedTickets)
			{
				if (expectedTickets < 1 && expectedTickets > 2) throw new GameEngineException($"Only 1 or 2 group of tickets can be bought");

				RemainingTickets = expectedTickets;
				IsMultiSelection = expectedTickets == 2;
			}

			internal void AddExclusionsForSingleSelection(decimal totalOrder, ExcludeSubtickets exclusionsByToWin, int numberOfDraws)
			{
				if (exclusionsByToWin == null) throw new ArgumentNullException(nameof(exclusionsByToWin));
				if (totalOrder < 0) throw new GameEngineException($"{nameof(totalOrder)} must be greater or equal than 0");
				if (numberOfDraws < 0) throw new GameEngineException($"{nameof(numberOfDraws)} must be greater or equal than 0");
				if (RemainingTickets <= 0) throw new GameEngineException($"All tickets expected were added");

				TotalOrder += totalOrder;
				RemainingTickets--;
				if (exclusionsForSingleSelection == null) exclusionsForSingleSelection = new ExcludeSubtickets();
				if (!exclusionsByToWin.IsEmpty())
				{
					exclusionsForSingleSelection.Add(exclusionsByToWin);
				}

				var isThereAnyExclusion = exclusionsForSingleSelection.CountExclusions > 0;
				var canBePurchasedSomeDraw = numberOfDraws != exclusionsForSingleSelection.CountExclusions;
				var isValidToRetryPurchase = isThereAnyExclusion && (canBePurchasedSomeDraw || !exclusionsForSingleSelection.AreAllExclusionsAll());
				IsValidToRetryPurchase = isValidToRetryPurchase;
			}

			internal void AddExclusionsForMultiSelection(decimal totalTicket, ExcludeSubtickets exclusionsByToWin, int numberOfDraws, Ticket.Selection selection)
			{
				switch (selection)
				{
					case Ticket.Selection.BALLS:
						AddExclusionsForBalls(totalTicket, exclusionsByToWin, numberOfDraws);
						break;
					case Ticket.Selection.MultipleInputSingleAmount:
						AddExclusionsForInputs(totalTicket, exclusionsByToWin, numberOfDraws);
						break;
					default:
						throw new GameEngineException($"{nameof(selection)} {selection} is not valid");
				}
			}

			private void AddExclusionsForBalls(decimal totalTicket, ExcludeSubtickets exclusionsByToWin, int numberOfDraws)
			{
				if (exclusionsByToWin == null) throw new ArgumentNullException(nameof(exclusionsByToWin));
				if (totalTicket < 0) throw new GameEngineException($"{nameof(totalTicket)} must be greater or equal than 0");
				if (numberOfDraws < 0) throw new GameEngineException($"{nameof(numberOfDraws)} must be greater or equal than 0");
				if (RemainingTickets <= 0) throw new GameEngineException($"All tickets expected were added");

				TotalOrder += totalTicket;
				RemainingTickets--;

				if (exclusionsForMultiSelectionBalls == null) exclusionsForMultiSelectionBalls = new ExcludeSubtickets();
				if (!exclusionsByToWin.IsEmpty())
				{
					exclusionsForMultiSelectionBalls.Add(exclusionsByToWin);
				}

				var isTheLastRemainingTickets = RemainingTickets == 0;
				var isThereAnyExclusion = (exclusionsForMultiSelectionBalls != null && exclusionsForMultiSelectionBalls.CountExclusions > 0) ||
					(exclusionsForMultiSelectionInputs != null && exclusionsForMultiSelectionInputs.CountExclusions > 0);
				var canBePurchasedSomeDraw = (exclusionsForMultiSelectionBalls != null && numberOfDraws != exclusionsForMultiSelectionBalls.CountExclusions) ||
					(exclusionsForMultiSelectionInputs != null && numberOfDraws != exclusionsForMultiSelectionInputs.CountExclusions);
				var isValidToRetryPurchase = isTheLastRemainingTickets && isThereAnyExclusion &&
					(canBePurchasedSomeDraw || !exclusionsForMultiSelectionBalls.AreAllExclusionsAll() || !exclusionsForMultiSelectionInputs.AreAllExclusionsAll());
				IsValidToRetryPurchase = isValidToRetryPurchase;
			}

			private void AddExclusionsForInputs(decimal totalTicket, ExcludeSubtickets exclusionsByToWin, int numberOfDraws)
			{
				if (exclusionsByToWin == null) throw new ArgumentNullException(nameof(exclusionsByToWin));
				if (totalTicket < 0) throw new GameEngineException($"{nameof(totalTicket)} must be greater or equal than 0");
				if (numberOfDraws < 0) throw new GameEngineException($"{nameof(numberOfDraws)} must be greater or equal than 0");
				if (RemainingTickets <= 0) throw new GameEngineException($"All tickets expected were added");

				TotalOrder += totalTicket;
				RemainingTickets--;

				if (exclusionsForMultiSelectionInputs == null) exclusionsForMultiSelectionInputs = new ExcludeSubtickets();
				if (!exclusionsByToWin.IsEmpty())
				{
					exclusionsForMultiSelectionInputs.Add(exclusionsByToWin);
				}

				var isTheLastRemainingTickets = RemainingTickets == 0;
				var isThereAnyExclusion = (exclusionsForMultiSelectionBalls != null && exclusionsForMultiSelectionBalls.CountExclusions > 0) ||
					(exclusionsForMultiSelectionInputs != null && exclusionsForMultiSelectionInputs.CountExclusions > 0);
				var canBePurchasedSomeDraw = (exclusionsForMultiSelectionBalls != null && numberOfDraws != exclusionsForMultiSelectionBalls.CountExclusions) ||
					(exclusionsForMultiSelectionInputs != null && numberOfDraws != exclusionsForMultiSelectionInputs.CountExclusions);
				var isValidToRetryPurchase = isTheLastRemainingTickets && isThereAnyExclusion &&
					(canBePurchasedSomeDraw || !exclusionsForMultiSelectionBalls.AreAllExclusionsAll() || !exclusionsForMultiSelectionInputs.AreAllExclusionsAll());
				IsValidToRetryPurchase = isValidToRetryPurchase;
			}

			HashSet<DateTime> validDatesToPurchase = new HashSet<DateTime>();
			internal IEnumerable<string> ValidDatesToPurchase
			{
				get
				{
					return validDatesToPurchase.Select(date => date.ToString("MM/dd/yyyy")).ToList();
				}
			}
			internal DateTime MaxValidDateToPurchase
			{
				get
				{
					return validDatesToPurchase.Max(date => date);
				}
			}
			internal void AddValidDateToPurchase(DateTime date)
			{
				validDatesToPurchase.Add(date);
			}
		}

        [Obsolete("Do not use it is for legacy purposes, for QA TC")]
        internal decimal TotalTicketFullOrder(string states, string dates, string numbers, string selectionMode, decimal ticketAmount, string includedNumbersForInput, Product product, NextDatesAccumulator nextDatesAccumulator)
		{
			return TotalTicketOrder(this.lotto900, states, dates, numbers, selectionMode, ticketAmount, includedNumbersForInput, DEFAULT_WITHOUT_EXCLUDES, product, nextDatesAccumulator);
		}

		[Obsolete("Do not use it is for legacy purposes, for QA TC")]
		internal decimal TotalTicketOrder(PicksLotteryGame lotto900, string states, string dates, string numbers, string selectionMode, decimal ticketAmount, string includedNumbersForInput, ExcludeSubtickets excludedSubtickets, Product product, NextDatesAccumulator nextDatesAccumulator)
		{
            string[] statesArr = string.IsNullOrWhiteSpace(states) ? new string[] { } : states.Split(',');
            string withFireBalls = string.Join(",", statesArr.Select(x => "false"));
			return TotalTicketOrder(lotto900, states, dates, withFireBalls, numbers, selectionMode, ticketAmount, includedNumbersForInput, excludedSubtickets, product, nextDatesAccumulator);
        }

		private decimal TotalTicketOrder(PicksLotteryGame lotto900, string states, string dates, string withFireBalls, string numbers, string selectionMode, decimal ticketAmount, string includedNumbersForInput, ExcludeSubtickets excludedSubtickets, Product product, NextDatesAccumulator nextDatesAccumulator)
		{
			string[] numbersArr = string.IsNullOrWhiteSpace(numbers) ? new string[] { } : numbers.Split(',');
			string[] datesArr = string.IsNullOrWhiteSpace(dates) ? new string[] { } : dates.Split(',');
			string[] statesArr = string.IsNullOrWhiteSpace(states) ? new string[] { } : states.Split(',');
			string[] withFireBallsArr = string.IsNullOrWhiteSpace(withFireBalls) ? new string[] { } : withFireBalls.Split(',');

			string[] includedNumbersForInputArr = string.IsNullOrWhiteSpace(includedNumbersForInput) ? new string[] { } : includedNumbersForInput.Split(',');
			int includeSubTickets;
			int pickType = 0;
			switch (selectionMode)
			{
				case "balls":
					if (numbersArr.All(x => x == "*")) throw new GameEngineException($"Pattern selected with {numbersArr.Length} * is not valid");
					includeSubTickets = 1;
					foreach (string num in numbersArr)
					{
						includeSubTickets *= new Pattern(num).Count;
						pickType++;
					}
					break;
				case "SingleInputMultipleAmount":
				case "MultipleInputSingleAmount":
					excludedSubtickets.ExcludeNumbersNoIncluded(includedNumbersForInputArr);
					includeSubTickets = includedNumbersForInputArr.Length;

					int length = 0;
					foreach (string inputNumber in includedNumbersForInputArr)
					{
						if (length == 0) length = inputNumber.Length;

						if (length != inputNumber.Length)
						{
							throw new GameEngineException($"All numbers needs should be in the same pick. {inputNumber} it is not valid.");
						}
					}

					if (length < 2 || length > 5) throw new GameEngineException("Invalid pick");
					if (includedNumbersForInputArr.Length == 0) throw new GameEngineException("At least should be one input number");
					pickType = includedNumbersForInputArr[0].Length;

					break;
				default:
					throw new GameEngineException($"Invalid selection mode {selectionMode}");
			}

			decimal total = 0m;

            for (int indexStateAbb = 0; indexStateAbb < statesArr.Length; indexStateAbb++)
            {
                string stateAbb = statesArr[indexStateAbb];

                var state = lotto900.GetState(stateAbb);
				var lottery = lotto900.GetLottery(pickType, state);

				bool withFireBall;
				if (!bool.TryParse(withFireBallsArr[indexStateAbb], out withFireBall)) throw new GameEngineException($"Invalid withFireBall value {withFireBall}");

                var nextDates = nextDatesAccumulator.TakeDates(state, withFireBall, datesArr.Length, lottery.IdOfLottery);
				foreach (var nextDate in nextDates)
				{
					if (lottery.IsGradedAt(nextDate.Date)) throw new GameEngineException($"Lottery was graded at {nextDate.Date}");
					if (lottery.IsNoActionAt(nextDate.Date)) throw new GameEngineException($"Lottery was as no action at {nextDate.Date}");

					var ticketCost = TicketCost(ticketAmount, includeSubTickets, excludedSubtickets, nextDate.DesiredDate, state);
					var areAllExcludes = ticketCost == 0;
					if (areAllExcludes) continue;
					total += product.Price * ticketCost;
				}
			}
			return total;
		}

        [Obsolete("Do not use it is for legacy purposes")]
        internal TotalOrderAndExclusionsByToWin TotalTicketOrderAndExclusionsByToWin(Domain domain, string states, string dates, string numbers, string selectionMode, decimal ticketAmount, string includedNumbersForInput,
			string gameType, Product product, NextDatesAccumulator nextDatesAccumulator, ToWinAccumulator toWinAccumulator, TotalOrderAndExclusionsByToWin totalOrder, string currencyCode, string gameTypeKey)
		{
            var excludedSubtickets = DEFAULT_WITHOUT_EXCLUDES;
            string[] statesArr = string.IsNullOrWhiteSpace(states) ? new string[] { } : states.Split(',');
            string withFireBalls = string.Join(",", statesArr.Select(x => "false"));
            return TotalTicketOrderAndExclusionsByToWin(domain, states, dates, withFireBalls, numbers, selectionMode, ticketAmount, includedNumbersForInput, gameType, excludedSubtickets, product, nextDatesAccumulator, toWinAccumulator, totalOrder, currencyCode, gameTypeKey, applyToleranceFactor: false, issued: string.Empty, now: DateTime.Now);
        }

        internal TotalOrderAndExclusionsByToWin TotalTicketOrderAndExclusionsByToWin(Domain domain, string states, string dates, string withFireBalls, string numbers, string selectionMode, decimal ticketAmount, string includedNumbersForInput,
			string gameType, Product product, NextDatesAccumulator nextDatesAccumulator, ToWinAccumulator toWinAccumulator, TotalOrderAndExclusionsByToWin totalOrder, string currencyCode, string gameTypeKey)
		{
			var excludedSubtickets = DEFAULT_WITHOUT_EXCLUDES;
			return TotalTicketOrderAndExclusionsByToWin(domain, states, dates, withFireBalls, numbers, selectionMode, ticketAmount, includedNumbersForInput, gameType, excludedSubtickets, product, nextDatesAccumulator, toWinAccumulator, totalOrder, currencyCode, gameTypeKey, applyToleranceFactor: false, issued: string.Empty, now: DateTime.Now);
		}

        [Obsolete("Do not use it is for legacy purposes")]
        internal TotalOrderAndExclusionsByToWin TotalTicketOrderAndExclusionsByToWin(Domain domain, string states, string dates, string numbers, string selectionMode, decimal ticketAmount, string includedNumbersForInput,
			string gameType, ExcludeSubtickets exclusionsByPurchase, Product product, NextDatesAccumulator nextDatesAccumulator, ToWinAccumulator toWinAccumulator, TotalOrderAndExclusionsByToWin totalOrder, string currencyCode, string gameTypeKey)
		{
            string[] statesArr = string.IsNullOrWhiteSpace(states) ? new string[] { } : states.Split(',');
            string withFireBalls = string.Join(",", statesArr.Select(x => "false"));
			return TotalTicketOrderAndExclusionsByToWin(domain, states, dates, withFireBalls, numbers, selectionMode, ticketAmount, includedNumbersForInput, gameType, exclusionsByPurchase, product, nextDatesAccumulator, toWinAccumulator, totalOrder, currencyCode, gameTypeKey, applyToleranceFactor: false, issued: string.Empty, now: DateTime.Now);
        }

        internal TotalOrderAndExclusionsByToWin TotalTicketOrderAndExclusionsByToWin(Domain domain, string states, string dates, string withFireBalls, string numbers, string selectionMode, decimal ticketAmount, string includedNumbersForInput,
			string gameType, ExcludeSubtickets exclusionsByPurchase, Product product, NextDatesAccumulator nextDatesAccumulator, ToWinAccumulator toWinAccumulator, TotalOrderAndExclusionsByToWin totalOrder, string currencyCode, string gameTypeKey, bool applyToleranceFactor, string issued, DateTime now)
		{
			if (domain == null) throw new ArgumentNullException(nameof(domain));
			if (!domain.Visible) throw new GameEngineException($"Domain {domain.Url} is not visible then sale cannot proceed");
			if (toWinAccumulator == null) throw new ArgumentNullException(nameof(toWinAccumulator));
            if (!lotto900.StandardCurrency.Equals(currencyCode, StringComparison.OrdinalIgnoreCase) && !lotto900.RewardCurrency.Equals(currencyCode, StringComparison.OrdinalIgnoreCase)) throw new GameEngineException($"Currency code {currencyCode} is not valid for purchase");

            string[] numbersArr = string.IsNullOrWhiteSpace(numbers) ? new string[] { } : numbers.Split(',');
			string[] datesArr = string.IsNullOrWhiteSpace(dates) ? new string[] { } : dates.Split(',');
			string[] statesArr = string.IsNullOrWhiteSpace(states) ? new string[] { } : states.Split(',');
			string[] withFireBallsArr = string.IsNullOrWhiteSpace(withFireBalls) ? new string[] { } : withFireBalls.Split(',');

			string[] includedNumbersForInputArr = string.IsNullOrWhiteSpace(includedNumbersForInput) ? new string[] { } : includedNumbersForInput.Split(',');
			int includeSubTickets;
			int pickType = 0;
			IPick[] picks;
			switch (selectionMode)
			{
				case "balls":
					if (numbersArr.All(x => x == "*")) throw new GameEngineException($"Pattern selected with {numbersArr.Length} * is not valid");
					includeSubTickets = 1;
					foreach (string num in numbersArr)
					{
						includeSubTickets *= new Pattern(num).Count;
						pickType++;
					}
					picks = PicksLotteryGame.PicksOfBalls(pickType, numbersArr);
					break;
				case "SingleInputMultipleAmount":
				case "MultipleInputSingleAmount":
					exclusionsByPurchase.ExcludeNumbersNoIncluded(includedNumbersForInputArr);
					includeSubTickets = includedNumbersForInputArr.Length;

					int length = 0;
					foreach (string inputNumber in includedNumbersForInputArr)
					{
						if (length == 0) length = inputNumber.Length;

						if (length != inputNumber.Length)
						{
							throw new GameEngineException($"All numbers needs should be in the same pick. {inputNumber} it is not valid.");
						}
					}

					if (length < 2 || length > 5) throw new GameEngineException("Invalid pick");
					if (includedNumbersForInputArr.Length == 0) throw new GameEngineException("At least should be one input number");
					pickType = includedNumbersForInputArr[0].Length;
					picks = PicksLotteryGame.PicksOfNumbers(pickType, includedNumbersForInputArr, includedNumbersForInputArr.Length);
					break;
				default:
					throw new GameEngineException($"Invalid selection mode {selectionMode}");
			}

			var keyName = string.IsNullOrWhiteSpace(gameTypeKey) ? LotteryGamesPool.GameTypes.KeyName(selectionMode, pickType) : gameTypeKey;
            if (!LotteryGamesPool.GameTypes.IsEnabled(keyName, domain)) throw new GameEngineException($"Game type {keyName} is not enabled for domain {domain.Url}");

            var subtickets = new List<SubTicket<IPick>>();
			foreach (var pick in picks)
			{
				subtickets.AddRange(pick.SubTickets());
			}

			decimal total = 0m;
			var exclusionsByToWin = new ExcludeSubtickets();
			var ticketType = ToTicketType(pickType, gameType[0]);
            for (int indexStateAbb = 0; indexStateAbb < statesArr.Length; indexStateAbb++)
            {
                string stateAbb = statesArr[indexStateAbb];

                var state = lotto900.GetState(stateAbb);
				var lottery = lotto900.GetLottery(pickType, state);

				bool withFireBall;
                if (!bool.TryParse(withFireBallsArr[indexStateAbb], out withFireBall)) throw new GameEngineException($"Invalid withFireBall value {withFireBall}");
                var nextDates = nextDatesAccumulator.TakeDates(state, withFireBall, datesArr.Length, lottery.IdOfLottery);

				foreach (var nextDate in nextDates)
				{
					if (lottery.IsGradedAt(nextDate.Date)) throw new GameEngineException($"Lottery was graded at {nextDate.Date}");
					if (lottery.IsNoActionAt(nextDate.Date)) throw new GameEngineException($"Lottery was as no action at {nextDate.Date}");

					var ticketCost = 0m;
					var drawDate = nextDate.Date;
					List<SubTicket<IPick>> subticketsWithoutExclusions = null;
					if (!exclusionsByPurchase.IsExclusionAllFor(state, drawDate))
					{
						subticketsWithoutExclusions = SubticketsWithoutExclusions(exclusionsByPurchase, pickType, state, drawDate, subtickets).ToList();

						AddExclusionsByToWin(domain, pickType, lottery, state, drawDate, subticketsWithoutExclusions, ticketAmount, ticketType, exclusionsByToWin, toWinAccumulator, withFireBall, applyToleranceFactor, issued, now);
						if (subticketsWithoutExclusions.Count > 0 && !exclusionsByToWin.AreAllExcludedIn(pickType, state, drawDate, subticketsWithoutExclusions))
						{
							ticketCost = TicketCost(ticketAmount, includeSubTickets, exclusionsByPurchase, drawDate, state);
						}
					}

					var areAllExcludes = ticketCost == 0;
					if (areAllExcludes) continue;
					else
					{
						totalOrder.AddValidDateToPurchase(nextDate.Date);
						if (totalOrder is TotalOrderByToWin)
							((TotalOrderByToWin)totalOrder).AddToWinByDrawAndNumbers(this, domain, subticketsWithoutExclusions, ticketAmount, ticketType, pickType, state, drawDate, currencyCode, withFireBall);
					}
					total += product.Price * ticketCost;
				}
			}

			int numberOfDraws = statesArr.Length * datesArr.Length;
			if (totalOrder.IsMultiSelection)
			{
				Ticket.Selection selection;
				if (Enum.TryParse(selectionMode.ToUpperInvariant(), true, out selection))
				{
					totalOrder.AddExclusionsForMultiSelection(total, exclusionsByToWin, numberOfDraws, selection);
				}
				else
				{
					throw new GameEngineException($"{nameof(selection)} {selectionMode} is not valid");
				}
			}
			else
			{
				totalOrder.AddExclusionsForSingleSelection(total, exclusionsByToWin, numberOfDraws);
			}

			return totalOrder;
		}

        [Obsolete("Do not use it is for legacy purposes")]
        internal TotalOrderAndExclusionsByToWin TotalTicketOrderAndExclusionsByToWin(Domain domain, string states, string dates, string selectionMode, decimal ticketAmount, List<string> includedNumbersForInputArr,
			string gameType, ExcludeSubtickets exclusionsByPurchase, Product product, NextDatesAccumulator nextDatesAccumulator, ToWinAccumulator toWinAccumulator, TotalOrderAndExclusionsByToWin totalOrder, string currencyCode, string gameTypeKey)
		{
            string[] statesArr = string.IsNullOrWhiteSpace(states) ? new string[] { } : states.Split(',');
            string withFireBalls = string.Join(",", statesArr.Select(x => "false"));
            return TotalTicketOrderAndExclusionsByToWin(domain, states, dates, withFireBalls, selectionMode, ticketAmount, includedNumbersForInputArr, gameType, exclusionsByPurchase, product, nextDatesAccumulator, toWinAccumulator, totalOrder, currencyCode, gameTypeKey, applyToleranceFactor: false, issued: string.Empty, now: DateTime.Now);
        }

        internal TotalOrderAndExclusionsByToWin TotalTicketOrderAndExclusionsByToWin(Domain domain, string states, string dates, string withFireBalls, string selectionMode, decimal ticketAmount, List<string> includedNumbersForInputArr, 
			string gameType, ExcludeSubtickets exclusionsByPurchase, Product product, NextDatesAccumulator nextDatesAccumulator, ToWinAccumulator toWinAccumulator, TotalOrderAndExclusionsByToWin totalOrder, string currencyCode, string gameTypeKey, bool applyToleranceFactor, string issued, DateTime now)
        {
            if (toWinAccumulator == null) throw new ArgumentNullException(nameof(toWinAccumulator));
            LotteryGame lotteryGame = nextDatesAccumulator.LotteryGame;
            if (!lotteryGame.StandardCurrency.Equals(currencyCode, StringComparison.OrdinalIgnoreCase) && !lotteryGame.RewardCurrency.Equals(currencyCode, StringComparison.OrdinalIgnoreCase)) throw new GameEngineException($"Currency code {currencyCode} is not valid for purchase");

            string[] datesArr = string.IsNullOrWhiteSpace(dates) ? new string[] { } : dates.Split(',');
			string[] statesArr = string.IsNullOrWhiteSpace(states) ? new string[] { } : states.Split(',');
            string[] withFireBallsArr = string.IsNullOrWhiteSpace(withFireBalls) ? new string[] { } : withFireBalls.Split(',');

            int includeSubTickets;
            int pickType = 0;
            IPick[] picks;
			switch (selectionMode)
            {
                case "SingleInputMultipleAmount":
				case "MultipleInputSingleAmount":
					exclusionsByPurchase.ExcludeNumbersNoIncluded(includedNumbersForInputArr);
                    includeSubTickets = includedNumbersForInputArr.Count;

					int length = 0;
					foreach (string inputNumber in includedNumbersForInputArr)
					{
                        string fixedInputNumber = inputNumber.Replace(Pick2.SPLIT_BEGINNING, string.Empty);

                        if (length == 0) length = fixedInputNumber.Length;

						if (length != fixedInputNumber.Length)
						{
							throw new GameEngineException($"All numbers needs should be in the same pick. {inputNumber} it is not valid.");
						}
					}

                    if (length < 2 || length > 5) throw new GameEngineException("Invalid pick");
                    if (includedNumbersForInputArr.Count == 0) throw new GameEngineException("At least should be one input number");
                    string firstInputNumber = includedNumbersForInputArr[0];
                    pickType = firstInputNumber.Replace(Pick2.SPLIT_BEGINNING, string.Empty).Length;
                    picks = PicksLotteryGame.PicksOfNumbers(pickType, includedNumbersForInputArr, includedNumbersForInputArr.Count);
                    break;
                default:
                    throw new GameEngineException($"Invalid selection mode {selectionMode}");
            }

            var keyName = string.IsNullOrWhiteSpace(gameTypeKey) ? LotteryGamesPool.GameTypes.KeyName(selectionMode, pickType) : gameTypeKey;
			if (lotteryGame is TrizLotteryGame) keyName = GameTypes.TRIZ_KEY_NAME;
            if (!LotteryGamesPool.GameTypes.IsEnabled(keyName, domain)) throw new GameEngineException($"Game type {keyName} is not enabled for domain {domain.Url}");

            var subtickets = new List<SubTicket<IPick>>();
			foreach (var pick in picks)
			{
				subtickets.AddRange(pick.SubTickets());
			}

			decimal total = 0m;
			var exclusionsByToWin = new ExcludeSubtickets();
			var ticketType = ToTicketType(pickType, gameType[0]);
            for (int indexStateAbb = 0; indexStateAbb < statesArr.Length; indexStateAbb++)
            {
                string stateAbb = statesArr[indexStateAbb];

                var state = lotteryGame.GetState(stateAbb);
				Lottery lottery;
				IdOfLottery idOfLottery;
                if (lotteryGame is PicksLotteryGame picksLotteryGame)
				{
                    lottery = picksLotteryGame.GetLottery(pickType, state);
                    idOfLottery = lottery.IdOfLottery;
                }
				else if (lotteryGame is TrizLotteryGame trizLotteryGame)
				{
					lottery = trizLotteryGame.GetLottery(pickType, state);
					idOfLottery = trizLotteryGame.GetLottery().IdOfLottery;
                }
				else
				{
                    throw new GameEngineException($"Invalid lottery game {lotteryGame}");
                }

				bool withFireBall;
                if (!bool.TryParse(withFireBallsArr[indexStateAbb], out withFireBall)) throw new GameEngineException($"Invalid withFireBall value {withFireBall}");

                var nextDates = nextDatesAccumulator.TakeDates(state, withFireBall, datesArr.Length, idOfLottery);

				foreach (var nextDate in nextDates)
				{
					if (lottery.IsGradedAt(nextDate.Date)) throw new GameEngineException($"Lottery was graded at {nextDate.Date}");
					if (lottery.IsNoActionAt(nextDate.Date)) throw new GameEngineException($"Lottery was as no action at {nextDate.Date}");

                    var ticketCost = 0m;
                    var drawDate = nextDate.Date;
					List<SubTicket<IPick>> subticketsWithoutExclusions = null;
					if (!exclusionsByPurchase.IsExclusionAllFor(state, drawDate))
					{
						subticketsWithoutExclusions = SubticketsWithoutExclusions(exclusionsByPurchase, pickType, state, drawDate, subtickets).ToList();

						AddExclusionsByToWin(domain, pickType, lottery, state, drawDate, subticketsWithoutExclusions, ticketAmount, ticketType, exclusionsByToWin, toWinAccumulator, withFireBall, applyToleranceFactor, issued, now);
						if (subticketsWithoutExclusions.Count > 0 && !exclusionsByToWin.AreAllExcludedIn(pickType, state, drawDate, subticketsWithoutExclusions))
						{
							ticketCost = TicketCost(ticketAmount, includeSubTickets, exclusionsByPurchase, drawDate, state);
						}
					}

					var areAllExcludes = ticketCost == 0;
					if (areAllExcludes) continue;
					else
					{
						totalOrder.AddValidDateToPurchase(nextDate.Date);
						if (totalOrder is TotalOrderByToWin)
							((TotalOrderByToWin)totalOrder).AddToWinByDrawAndNumbers(this, domain, subticketsWithoutExclusions, ticketAmount, ticketType, pickType, state, drawDate, currencyCode, withFireBall);
					}
					total += product.Price * ticketCost;
				}
			}

			int numberOfDraws = statesArr.Length * datesArr.Length;
			if (totalOrder.IsMultiSelection)
			{
				Ticket.Selection selection;
				if (Enum.TryParse(selectionMode.ToUpperInvariant(), true, out selection))
				{
					totalOrder.AddExclusionsForMultiSelection(total, exclusionsByToWin, numberOfDraws, selection);
				}
				else
				{
					throw new GameEngineException($"{nameof(selection)} {selectionMode} is not valid");
				}
			}
			else
			{
				totalOrder.AddExclusionsForSingleSelection(total, exclusionsByToWin, numberOfDraws);
			}

			return totalOrder;
		}

		private void AddExclusionsByToWin(Domain domain, int pickNumber, Lottery lottery, State state, DateTime drawDate, List<SubTicket<IPick>> subticketsWithoutExclusions, decimal betAmount, TicketType ticketType,
			ExcludeSubtickets exclusionsByToWin, ToWinAccumulator toWinAccumulator, bool withFireball, bool applyToleranceFactor, string issued, DateTime now)
        {
            LotteryGame lotteryGame = lottery.LotteryGame;
            var riskProfile = lotteryGame.RiskProfiles.GetRiskProfile(domain);
			var riskPerLottery = riskProfile.Risks.Risk.GetRiskPerLottery(pickNumber, lottery);
			var schedule = lottery.FindScheduleAt(drawDate);
			if (!string.IsNullOrWhiteSpace(issued))
			{
				var issuedAsText = lottery.Company.EncryptionHelper.Decrypt(issued);
				if (!DateTime.TryParseExact(issuedAsText, "MM/dd/yyyy HH:mm:ss", Integration.CultureInfoEnUS, DateTimeStyles.None, out DateTime dateTimeIssued)) throw new GameEngineException($"Invalid date format for {nameof(issued)} {issued}");
				if (dateTimeIssued.AddSeconds(riskProfile.Risks.Risk.RiskQuotationExpirationTime) > now) return;
			}

            List<SubTicket<IPick>> subticketsExceedingToWin = riskPerLottery.SubticketsExceedingToWin((PrizesPicks)riskProfile.Prizes, schedule, drawDate, subticketsWithoutExclusions, betAmount, ticketType, domain, toWinAccumulator, withFireball, applyToleranceFactor).ToList();
            var hourOfDateFormattedAsText = drawDate.ToString("h:mm tt");
            var day = new DateTime(drawDate.Year, drawDate.Month, drawDate.Day);
            var allSubticketsWereExcluded = subticketsWithoutExclusions.Count > 0 &&
                                            subticketsWithoutExclusions.Count == subticketsExceedingToWin.Count;
            if (allSubticketsWereExcluded)
            {
                exclusionsByToWin.Add(state, day, hourOfDateFormattedAsText, INDICATOR_FOR_ALL_SUBTICKETS_EXCLUDED, pickNumber);
            }
            else
            {
                foreach (var subticket in subticketsExceedingToWin)
                {
                    var subticketAsString = string.Empty;
                    switch (pickNumber)
                    {
                        case 2:
                            subticketAsString = $"{subticket[1]}{subticket[2]}";
                            break;
                        case 3:
                            subticketAsString = $"{subticket[1]}{subticket[2]}{subticket[3]}";
                            break;
                        case 4:
                            subticketAsString = $"{subticket[1]}{subticket[2]}{subticket[3]}{subticket[4]}";
                            break;
                        case 5:
                            subticketAsString = $"{subticket[1]}{subticket[2]}{subticket[3]}{subticket[4]}{subticket[5]}";
                            break;
                        default:
                            throw new GameEngineException($"There is no {nameof(IPick)} implementation for {nameof(pickNumber)} {pickNumber}");
                    }

					exclusionsByToWin.Add(state, day, hourOfDateFormattedAsText, subticketAsString, pickNumber);
				}
			}
		}

		private IEnumerable<SubTicket<IPick>> SubticketsWithoutExclusions(ExcludeSubtickets exclusionsByPurchase, int pickNumber, State state, DateTime drawDate, IEnumerable<SubTicket<IPick>> subticketsToBuy)
		{
			var subticketsExcludesInDraw = new List<SubTicket<IPick>>();
			foreach (var exclude in exclusionsByPurchase.SubticketsToExclude())
			{
				if (exclude.DrawingDate == drawDate && exclude.State == state)
				{
					IPick pick;
					switch (pickNumber)
					{
						case 2:
							pick = new Pick2(exclude.Subticket);
							break;
						case 3:
							pick = new Pick3(exclude.Subticket);
							break;
						case 4:
							pick = new Pick4(exclude.Subticket);
							break;
						case 5:
							pick = new Pick5(exclude.Subticket);
							break;
						default:
							throw new GameEngineException($"There is no {nameof(IPick)} implementation for {nameof(pickNumber)} {pickNumber}");
					}
					subticketsExcludesInDraw.AddRange(pick.SubTickets());
				}
			}

			var result = new List<SubTicket<IPick>>();
			foreach (var subticket in subticketsToBuy)
			{
				var isExcluded = subticketsExcludesInDraw.Any(s => s.AsString() == subticket.AsString());
				if (!isExcluded)
				{
					result.Add(subticket);
				}
			}
			return result;
		}

		internal Order CreatePowerballOrder(Player player, List<DateTime> dates, List<string> numbers, List<bool> withPowerPlays, List<decimal> ticketAmount, OrderCart order, Domain domain, Product withPowerPlay, Product withoutPowerPlay)
		{
			if (player == null) throw new ArgumentNullException(nameof(player));
			if (order == null) throw new ArgumentNullException(nameof(order));
			if (domain == null) throw new ArgumentNullException(nameof(domain));
			if (withPowerPlay == null) throw new ArgumentNullException(nameof(withPowerPlay));
			if (withoutPowerPlay == null) throw new ArgumentNullException(nameof(withoutPowerPlay));
			if (dates.Any(date => date == default(DateTime))) throw new GameEngineException("Powerball dates must be provided");
			if (ticketAmount.Any(amount => amount <= 0)) throw new GameEngineException("Powerball amounts must be provided");
			if (numbers.Any(dozenOfNumber => dozenOfNumber.Length != 12)) throw new GameEngineException($"Some of Powerball Numbers are invalid");
			if (dates.Count != numbers.Count) throw new GameEngineException("Dates must be equal than total of tickets");
			if (withPowerPlays.Count != numbers.Count) throw new GameEngineException("Power plays must be equal than total of tickets");
			if (ticketAmount.Count != numbers.Count) throw new GameEngineException("Amounts must be equal than total of tickets");

			OrderCart newOrder = order;
			//TODO: Erick, Validar las fechas
			for (int index = 0; index < numbers.Count; index++)
			{
				DateTime nextDate = dates[index];
				bool powerPlay = withPowerPlays[index];
				decimal amount = ticketAmount[index];

				var ticketCost = TicketCost(amount, powerPlay);
				PowerballActivator activator;
				if (powerPlay)
				{
					activator = CreatePowerballPowerPlayTicket(player, nextDate, ticketCost, numbers[index], domain);
					newOrder.Add(withPowerPlay, activator, ticketCost);
				}
				else
				{
					activator = CreatePowerballSingleTicket(player, nextDate, ticketCost, numbers[index], domain);
					newOrder.Add(withoutPowerPlay, activator, ticketCost);
				}
			}
			return newOrder;
		}
		internal Order CreateKenoOrder(Player player, List<int> selection, bool multiplier, bool bulleye, decimal amount, string drawIdPrefix, DateTime purchaseDate, OrderCart order, Domain domain, Product kenoSingleProd, Product kenoWithMultiplierProd, Product kenoWithBullEyeProd, Product kenoWithMultiplierAndBulleyeProd)
		{
			if (string.IsNullOrEmpty(drawIdPrefix)) throw new ArgumentNullException(nameof(drawIdPrefix));
			if (player == null) throw new ArgumentNullException(nameof(player));
			if (order == null) throw new ArgumentNullException(nameof(order));
			if (domain == null) throw new ArgumentNullException(nameof(domain));
			if (selection == null) throw new ArgumentNullException(nameof(selection));
			if (kenoSingleProd == null) throw new ArgumentNullException(nameof(kenoSingleProd));
			if (kenoWithMultiplierProd == null) throw new ArgumentNullException(nameof(kenoWithMultiplierProd));
			if (kenoWithBullEyeProd == null) throw new ArgumentNullException(nameof(kenoWithBullEyeProd));
			if (selection.Count != 10 && selection.Count != 12) throw new GameEngineException($"Select [10|12] numbers at least.");

			var lotteries = Lotto900();
			var kenoLottery = lotteries.GetKeno();
			var nextKenoDraw = lotteries.NextPendingAndNoRegradedSchedulesAtForKeno(purchaseDate);
			var nextValidDrawDate = kenoLottery.NextValidDrawDate(purchaseDate);

			if (nextKenoDraw.IdPrefix != drawIdPrefix)
			{
				throw new GameEngineException($"{drawIdPrefix} is not longer valid for date {purchaseDate}.");
			}

			LotteryKeno.CheckNumbersRepeated(selection);
			var numbers = LotteryKeno.SortNumbers(selection);
			LotteryKeno.CheckRange(numbers, 1, 80);
			OrderCart newOrder = order;
			//TODO: Erick, Validar las fechas
			var ticketCost = TicketCostForKeno(multiplier, bulleye, amount, kenoSingleProd, kenoWithMultiplierProd, kenoWithBullEyeProd, kenoWithMultiplierAndBulleyeProd);
			var ticketBaseCost = amount;

			KenoActivator activator;

			if (multiplier && bulleye)
			{
				activator = CreateKenoMultiplierAndBulleyeTicket(player, nextKenoDraw.Id, nextValidDrawDate, ticketBaseCost, ticketCost, numbers, bulleye, domain);
				newOrder.Add(kenoWithMultiplierAndBulleyeProd, activator, ticketCost);
			}
			else if (multiplier)
			{
				activator = CreateKenoMultiplierTicket(player, nextKenoDraw.Id, nextValidDrawDate, ticketBaseCost, ticketCost, numbers, bulleye, domain);
				newOrder.Add(kenoWithMultiplierProd, activator, ticketCost);
			}
			else if (bulleye)
			{
				activator = CreateKenoBulleyeTicket(player, nextKenoDraw.Id, nextValidDrawDate, ticketBaseCost, ticketCost, numbers, bulleye, domain);
				newOrder.Add(kenoWithBullEyeProd, activator, ticketCost);
			}
			else
			{
				activator = CreateKenoSingleTicket(player, nextKenoDraw.Id, nextValidDrawDate, ticketBaseCost, ticketCost, numbers, domain);
				newOrder.Add(kenoSingleProd, activator, ticketCost);
			}


			return newOrder;
		}



		internal decimal TotalTicketOrderForPowerball(List<DateTime> dates, List<string> numbers, List<bool> withPowerPlays, List<decimal> ticketAmount, Product withPowerPlay, Product withoutPowerPlay)
		{
			if (withPowerPlay == null) throw new ArgumentNullException(nameof(withPowerPlay));
			if (withoutPowerPlay == null) throw new ArgumentNullException(nameof(withoutPowerPlay));
			if (dates.Any(date => date == default(DateTime))) throw new GameEngineException("Powerball dates must be provided");
			if (ticketAmount.Any(amount => amount <= 0)) throw new GameEngineException("Powerball amounts must be provided");
			if (numbers.Any(dozenOfNumber => dozenOfNumber.Length != 12)) throw new GameEngineException($"Some of Powerball Numbers are invalid");
			if (dates.Count != numbers.Count) throw new GameEngineException("Dates must be equal than total of tickets");
			if (withPowerPlays.Count != numbers.Count) throw new GameEngineException("Power plays must be equal than total of tickets");
			if (ticketAmount.Count != numbers.Count) throw new GameEngineException("Amounts must be equal than total of tickets");

			decimal total = 0m;

			//TODO: Erick, Validar las fechas
			for (int index = 0; index < numbers.Count; index++)
			{
				DateTime nextDate = dates[index];
				bool powerPlay = withPowerPlays[index];
				decimal amount = ticketAmount[index];

				var ticketCost = TicketCost(amount, powerPlay);
				PowerballActivator activator;
				if (powerPlay)
				{
					total += withPowerPlay.Price * ticketCost;
				}
				else
				{
					total += withoutPowerPlay.Price * ticketCost;
				}
			}
			return total;
		}

		internal decimal TotalTicketOrderForKeno(List<int> selection, bool multiplier, bool bulleye, decimal amount, string drawIdPrefix, DateTime purchaseDate, Product kenoSingleProd, Product kenoWithMultiplierProd, Product kenoWithBullEyeProd, Product kenoWithMultiplierAndBulleyeProd)
		{
			if (string.IsNullOrEmpty(drawIdPrefix)) throw new ArgumentNullException(nameof(drawIdPrefix));
			if (selection == null) throw new ArgumentNullException(nameof(selection));
			if (kenoSingleProd == null) throw new ArgumentNullException(nameof(kenoSingleProd));
			if (kenoWithMultiplierProd == null) throw new ArgumentNullException(nameof(kenoWithMultiplierProd));
			if (kenoWithBullEyeProd == null) throw new ArgumentNullException(nameof(kenoWithBullEyeProd));
			if (selection.Count != 10 && selection.Count != 12) throw new GameEngineException($"Select [10|12] numbers at least.");

			var lotteries = Lotto900();
			var kenoLottery = lotteries.GetKeno();
			var nextKenoDraw = lotteries.NextPendingAndNoRegradedSchedulesAtForKeno(purchaseDate);
			var nextValidDrawDate = kenoLottery.NextValidDrawDate(purchaseDate);

			if (nextKenoDraw.IdPrefix != drawIdPrefix)
			{
				throw new GameEngineException($"{drawIdPrefix} is not longer valid for date {purchaseDate}.");
			}

			LotteryKeno.CheckNumbersRepeated(selection);
			var numbers = LotteryKeno.SortNumbers(selection);
			LotteryKeno.CheckRange(numbers, 1, 80);
			var ticketCost = TicketCostForKeno(multiplier, bulleye, amount, kenoSingleProd, kenoWithMultiplierProd, kenoWithBullEyeProd, kenoWithMultiplierAndBulleyeProd);

			return ticketCost;
		}

		private decimal TicketCostForKeno(bool multiplier, bool bulleye, decimal amount, Product kenoSingleProd, Product kenoWithMultiplierProd, Product kenoWithBullEyeProd, Product kenoWithMultiplierAndBulleyeProd)
		{
			decimal ticketCost = amount;

			//if (multiplier && !bulleye) ticketCost += kenoWithMultiplierProd.Price;
			//else if (bulleye && !multiplier) ticketCost += kenoWithMultiplierProd.Price;
			//else if (multiplier && bulleye) ticketCost += kenoWithMultiplierAndBulleyeProd.Price;

			if (multiplier) ticketCost += amount;
			if (bulleye) ticketCost += amount;
			return ticketCost;

		}

		[Obsolete("Use AddOrders with one parameter instead")]
		internal void AddOrders(OrderCart order, decimal founds)
		{
			AddOrders(order);
		}

		internal void AddOrders(OrderCart order)
		{
			if (order == null) throw new GameEngineException("Order cannot be null.");

			orders.Add(order);
		}

        internal void CreateTicket(TicketPickActivator activator, Player player, State state, NextDateItem nextDateItem, bool withFireBall, decimal ticketCost, string selectionMode, IEnumerable<string> numbers, string[] includedNumbersForInput, ExcludeSubtickets excludedSubtickets, int indexInOrder)
		{
			activator.Player = player;
			activator.State = state;
			activator.DrawDate = nextDateItem.Date;
			activator.WithFireBall = withFireBall;
			
			if (selectionMode == nameof(Ticket.Selection.MultipleInputSingleAmount) || selectionMode == nameof(Ticket.Selection.SingleInputMultipleAmount))
			{
				activator.Numbers = includedNumbersForInput;
			}
			else if (selectionMode == "balls")
			{
				int i = 0;
                foreach (var number in numbers)
                {
					activator[i + 1] = number;
					i++;
				}
			}
			else
				throw new GameEngineException($"Selection mode {selectionMode} is unknown.");
			activator.SelectionMode = selectionMode;
			activator.TicketCost = ticketCost;
			activator.ExcludedNumbers = excludedSubtickets.SubticketsAsString(state, nextDateItem.DesiredDate);
			activator.IndexInOrder = indexInOrder;
		}

		private void CreateTicket(PowerballActivator activator, Player player, DateTime date, decimal ticketCost, string dozenOfNumbers)
		{
			activator.Player = player;
			activator.DrawDate = date;
			activator.Numbers = dozenOfNumbers;
			activator.TicketCost = ticketCost;
		}
		private void CreateTicket(KenoActivator activator, Player player, string drawId, DateTime purchaseDate, decimal ticketBaseCost, decimal ticketCost, int[] numbers)
		{
			activator.Player = player;
			activator.DrawDate = purchaseDate;
			activator.Numbers = numbers;
			activator.TicketCost = ticketCost;
			activator.TicketBaseCost = ticketBaseCost;
			activator.DrawId = drawId;
		}

		[Obsolete("Use SaleTickets with eight parameters instead, founds is no longer necessary")]
		internal void SaleTickets(bool itIsThePresent, OrderCart order, DateTime now, decimal founds, int ticketNumber, Domain domain)
		{
			if (order == null) throw new ArgumentNullException(nameof(order));
			if (!order.HasItems) throw new GameEngineException($"Order {order.Number} has not added any items yet.");
			if (!order.CanBeModified) throw new GameEngineException("This order was already processed.");
			if (!customers.Contains(order.Customer)) throw new GameEngineException("Customer is unknown. Order only can be delivered to know customers.");
			/*var productsOnOrder = order.ProductsOnOrder();
            var areProductsOfLotto = lotto900.ContainsAll(productsOnOrder);
            if (!areProductsOfLotto) throw new GameEngineException("Order contains products that are unknown. Only products on sale can be bought.");*/

			Tickets tickets = new Tickets();

			OrderPlaced orderPlaced = (OrderPlaced)order.Purchase();
			OrderPayed orderReadyToDispatch = (OrderPayed)orderPlaced.ConfirmSale();
			orderReadyToDispatch.Domain = domain;
			OrderCompleted orderCompleted = (OrderCompleted)orderReadyToDispatch.Dispatch(itIsThePresent, now);
			accounting.Purchase(itIsThePresent, orderCompleted, now);
			orders.Replace(order, orderCompleted);

			var customer = orderCompleted.Customer;
			if (Integration.UseKafka || Integration.UseKafkaForAuto) Integration.Kafka.Send(itIsThePresent, Integration.Kafka.TopicForCustomers,
				new CustomerSaleMessage(customer.Identifier,
				customer.AccountNumber, 
				this.sales.CurrentStore.Id,
                domain,
                now,
				Currency.Factory(orderCompleted.CurrencyCode, orderCompleted.Total()),
				(int)customer.Player.Agent));
		}

		private KenoSingleTicketActivator CreateKenoSingleTicket(Player player, string drawId, DateTime date, decimal ticketBaseCost, decimal ticketCost, int[] numbers, Domain domain)
		{
			KenoSingleTicketActivator activator = new KenoSingleTicketActivator(this);
			CreateTicket(activator, player, drawId, date, ticketBaseCost, ticketCost, numbers);
			return activator;
		}

		private KenoMultiplierAndBulleyeTicketActivator CreateKenoMultiplierAndBulleyeTicket(Player player, string drawId, DateTime drawdate, decimal ticketBaseCost, decimal ticketCost, int[] numbers, bool bulleye, Domain domain)
		{
			KenoMultiplierAndBulleyeTicketActivator activator = new KenoMultiplierAndBulleyeTicketActivator(this);
			CreateTicket(activator, player, drawId, drawdate, ticketBaseCost, ticketCost, numbers);
			return activator;
		}
		private KenoMultiplierTicketActivator CreateKenoMultiplierTicket(Player player, string drawId, DateTime drawDate, decimal ticketBaseCost, decimal ticketCost, int[] numbers, bool bulleye, Domain domain)
		{
			KenoMultiplierTicketActivator activator = new KenoMultiplierTicketActivator(this);
			CreateTicket(activator, player, drawId, drawDate, ticketBaseCost, ticketCost, numbers);
			return activator;
		}
		private KenoBulleyeTicketActivator CreateKenoBulleyeTicket(Player player, string drawId, DateTime drawDate, decimal ticketBaseCost, decimal ticketCost, int[] numbers, bool bulleye, Domain domain)
		{
			KenoBulleyeTicketActivator activator = new KenoBulleyeTicketActivator(this);
			CreateTicket(activator, player, drawId, drawDate, ticketBaseCost, ticketCost, numbers);
			return activator;
		}
		private PowerballSingleTicketActivator CreatePowerballSingleTicket(Player player, DateTime drawDate, decimal ticketCost, string dozenOfNumbers, Domain domain)
		{
			PowerballSingleTicketActivator activator = new PowerballSingleTicketActivator(this);
			CreateTicket(activator, player, drawDate, ticketCost, dozenOfNumbers);
			return activator;
		}

		private PowerballPowerPlayTicketActivator CreatePowerballPowerPlayTicket(Player player, DateTime date, decimal ticketCost, string dozenOfNumbers, Domain domain)
		{
			PowerballPowerPlayTicketActivator activator = new PowerballPowerPlayTicketActivator(this);
			CreateTicket(activator, player, date, ticketCost, dozenOfNumbers);
			return activator;
		}

		[Obsolete("Use SaleTickets with eight parameters instead, founds is no longer necessary")]
		internal void SaleTickets(bool itIsThePresent, OrderCart order, DateTime now, decimal founds, int ticketNumber, Domain domain, int lowBetId, int theHighestBetId, int orderNumber)
		{
			SaleTickets(itIsThePresent, order, now, ticketNumber, domain, lowBetId, theHighestBetId, orderNumber);
		}

		[Obsolete("Do not use SaleTickets, it is for legacy purposes")]
		internal void SaleTickets(bool itIsThePresent, OrderCart order, DateTime now, int ticketNumber, Domain domain, int lowBetId, int theHighestBetId, int orderNumber)
		{
			if (order == null) throw new ArgumentNullException(nameof(order));
			if (!order.HasItems) throw new GameEngineException($"Order {order.Number} has not added any items yet.");
			if (!order.CanBeModified) throw new GameEngineException("This order was already processed.");
			if (!customers.Contains(order.Customer)) throw new GameEngineException("Customer is unknown. Order only can be delivered to know customers.");

			order.NextBetNumber = lowBetId;
			order.Number = orderNumber;
			order.AuthorizationId = ticketNumber;

			OrderPlaced orderPlaced = (OrderPlaced)order.Purchase();
			OrderPayed orderReadyToDispatch = (OrderPayed)orderPlaced.ConfirmSale();
			orderReadyToDispatch.Domain = domain;
			OrderCompleted orderCompleted = (OrderCompleted)orderReadyToDispatch.Dispatch(itIsThePresent, now);
			accounting.Purchase(itIsThePresent, orderCompleted, now);
			orders.Replace(order, orderCompleted);

			var customer = orderCompleted.Customer;
			if (Integration.UseKafka || Integration.UseKafkaForAuto) Integration.Kafka.Send(itIsThePresent, Integration.Kafka.TopicForCustomers,
				new CustomerSaleMessage(customer.Identifier,
				customer.AccountNumber, 
				this.sales.CurrentStore.Id,
                domain,
                now,
				Currency.Factory(orderCompleted.CurrencyCode, orderCompleted.Total()),
				(int)customer.Player.Agent));
		}

		internal void PurchaseTickets(bool itIsThePresent, OrderCart order, DateTime now, int ticketNumber, Domain domain, int orderNumber)
		{
			if (order == null) throw new ArgumentNullException(nameof(order));
			if (domain == null) throw new ArgumentNullException(nameof(domain));
			if (!order.HasItems) throw new GameEngineException($"Order {order.Number} has not added any items yet.");
			if (!order.CanBeModified) throw new GameEngineException("This order was already processed.");
			if (!customers.Contains(order.Customer)) throw new GameEngineException("Customer is unknown. Order only can be delivered to know customers.");

			//order.NextBetNumber = lowBetId;
			order.Number = orderNumber;
			order.AuthorizationId = ticketNumber;

			OrderPlaced orderPlaced = (OrderPlaced)order.Purchase();
			OrderPayed orderReadyToDispatch = (OrderPayed)orderPlaced.ConfirmSale();
			orderReadyToDispatch.Domain = domain;
			OrderCompleted orderCompleted = (OrderCompleted)orderReadyToDispatch.Dispatch(itIsThePresent, now);
			var customer = orderCompleted.Customer;

			//TODO En fururas versiones theHighestBetId deberia ser removido como parametro y calculado como el MAX BetId de la orden
			accounting.Purchase(itIsThePresent, orderCompleted, now);
			orders.Replace(order, orderCompleted);

			if (Integration.UseKafka) Integration.Kafka.Send(itIsThePresent, Integration.Kafka.TopicForCustomers,
				new CustomerSaleMessage(customer.Identifier,
				customer.AccountNumber,
				this.sales.CurrentStore.Id,
                domain,
				now,
				Currency.Factory(order.CurrencyCode, orderCompleted.Total()),
				(int)customer.Player.Agent));
		}

		internal void PurchaseTickets(bool itIsThePresent, OrderCart order, DateTime now, int ticketNumber, Domain domain, int lowBetId, int theHighestBetId, int orderNumber)
		{
			if (order == null) throw new ArgumentNullException(nameof(order));
			if (domain == null) throw new ArgumentNullException(nameof(domain));
			if (!order.HasItems) throw new GameEngineException($"Order {order.Number} has not added any items yet.");
			if (!order.CanBeModified) throw new GameEngineException("This order was already processed.");
			if (!customers.Contains(order.Customer)) throw new GameEngineException("Customer is unknown. Order only can be delivered to know customers.");
            if (!lotto900.StandardCurrency.Equals(order.CurrencyCode.ToString(), StringComparison.OrdinalIgnoreCase) && !lotto900.RewardCurrency.Equals(order.CurrencyCode.ToString(), StringComparison.OrdinalIgnoreCase)) throw new GameEngineException($"Currency code {order.CurrencyCode} is not valid for purchase");

            order.ConfigureWithSingleAuthorization(lowBetId, orderNumber, ticketNumber);
			order.Domain = domain;
			OrderCompleted orderCompleted = (OrderCompleted)order.Purchase(itIsThePresent, now);
			var customer = orderCompleted.Customer;

			//TODO En fururas versiones theHighestBetId deberia ser removido como parametro y calculado como el MAX BetId de la orden
			if (itIsThePresent)
			{
                var ticket = lotto900.FindTicketMatchingWith(ticketNumber);
                accounting.Purchase(itIsThePresent, orderCompleted, now, Enumerable.Empty<Ticket>().Append(ticket));
			}
			orders.Replace(order, orderCompleted);

            if (Integration.UseKafka) Integration.Kafka.Send(itIsThePresent, Integration.Kafka.TopicForCustomers,
                new CustomerSaleMessage(customer.Identifier,
                customer.AccountNumber, 
				this.sales.CurrentStore.Id,
				domain,
				now, 
				Currency.Factory(order.CurrencyCode, orderCompleted.Total()),
				(int)customer.Player.Agent));
		}

        internal void PurchaseTickets(bool itIsThePresent, OrderCart order, DateTime now, AuthorizationsNumbers authorizationsNumbers, Domain domain, int lowBetId, int orderNumber)
        {
            PurchaseTickets(itIsThePresent, order, now, authorizationsNumbers.Numbers, domain, lowBetId, orderNumber, "Artemis_ThirdParty_Deposit_USD");
        }

        internal void PurchaseTickets(bool itIsThePresent, OrderCart order, DateTime now, AuthorizationsNumbers authorizationsNumbers, Domain domain, int lowBetId, int orderNumber, string processorKey)
		{
			PurchaseTickets(itIsThePresent, order, now, authorizationsNumbers.Numbers, domain, lowBetId, orderNumber, processorKey);
		}

        internal void PurchaseTickets(bool itIsThePresent, OrderCart order, DateTime now, IEnumerable<int> ticketNumbers, Domain domain, int lowBetId, int orderNumber, string processorKey)
		{
            int entityIdArtemis = 2;
            int paymentMethodId = (int)town.connectors.drivers.PaymentMethod.ThirdParty;
            PurchaseTickets(itIsThePresent, order, now, ticketNumbers, domain, lowBetId, orderNumber, processorKey, entityIdArtemis, paymentMethodId);
        }
        internal void PurchaseTickets(bool itIsThePresent, OrderCart order, DateTime now, IEnumerable<int> ticketNumbers, Domain domain, int lowBetId, int orderNumber, string processorKey, int entityId, int paymentMethodId)
		{
			if (order == null) throw new ArgumentNullException(nameof(order));
			if (domain == null) throw new ArgumentNullException(nameof(domain));
			if (!order.HasItems) throw new GameEngineException($"Order {order.Number} has not added any items yet.");
			if (!order.CanBeModified) throw new GameEngineException("This order was already processed.");
			if (!customers.Contains(order.Customer)) throw new GameEngineException("Customer is unknown. Order only can be delivered to know customers.");
			if (ticketNumbers == null) throw new ArgumentNullException(nameof(ticketNumbers));
			if (!ticketNumbers.Any()) throw new GameEngineException("Ticket numbers must be provided.");
			if (string.IsNullOrWhiteSpace(processorKey)) throw new ArgumentNullException(nameof(processorKey));
			if (entityId <= 0) throw new GameEngineException("EntityId must be greater than zero.");
            if (paymentMethodId <= 0) throw new GameEngineException("EntityId must be greater than zero.");

            order.ConfigureWithMultipleAuthorization(lowBetId, orderNumber, ticketNumbers, processorKey, entityId, paymentMethodId);
			order.Domain = domain;
			OrderCompleted orderCompleted = (OrderCompleted)order.Purchase(itIsThePresent, now);
			var customer = orderCompleted.Customer;

			if (itIsThePresent)
			{
                var tickets = order.Items.Where(x => x.Gameboard is Ticket).Select(x => x.Gameboard as Ticket).ToList();
                accounting.Purchase(itIsThePresent, orderCompleted, now, tickets);

                if (Integration.UseKafka)
                {
                    var ticketTypes = new List<TicketType>();
                    var numbers = new List<SubTicket<IPick>>();

                    foreach (var ticket in tickets)
                    {
                        var subtickets = ticket.SubTickets();
                        if (ticket.Count == 1) ticketTypes.Add(ticket.IdOfType());
                        else ticketTypes.AddRange(Enumerable.Repeat(ticket.IdOfType(), subtickets.Count()));
                        numbers.AddRange(subtickets);
                    }
                    Integration.Kafka.Send(itIsThePresent, Integration.Kafka.TopicForRecents, new RecentsMessage(customer.AccountNumber, ticketTypes, numbers, now));
                }
            }
			orders.Replace(order, orderCompleted);

			if (Integration.UseKafka) Integration.Kafka.Send(itIsThePresent, Integration.Kafka.TopicForCustomers,
				new CustomerSaleMessage(customer.Identifier,
				customer.AccountNumber,
				this.sales.CurrentStore.Id,
				domain,
				now,
				Currency.Factory(order.CurrencyCode, orderCompleted.Total()),
				(int)customer.Player.Agent));
			
        }

		internal void PurchaseOrder(bool itIsThePresent, OrderCart order, Domain domain, DateTime now)
		{
			if (order == null) throw new ArgumentNullException(nameof(order));
			if (domain == null) throw new ArgumentNullException(nameof(domain));
			if (!order.HasItems) throw new GameEngineException($"Order {order.Number} has not added any items yet.");
			if (!order.CanBeModified) throw new GameEngineException("This order was already processed.");
			if (!customers.Contains(order.Customer)) throw new GameEngineException("Customer is unknown. Order only can be delivered to know customers.");

			OrderPlaced orderPlaced = (OrderPlaced)order.Purchase();
			OrderPayed orderReadyToDispatch = (OrderPayed)orderPlaced.ConfirmSale();
			orderReadyToDispatch.Domain = domain;
			OrderCompleted orderCompleted = (OrderCompleted)orderReadyToDispatch.Dispatch(itIsThePresent, now);
			accounting.Purchase(itIsThePresent, orderCompleted, now);
			orders.Add(orderCompleted);

			var customer = orderCompleted.Customer;
			if (Integration.UseKafka || Integration.UseKafkaForAuto) Integration.Kafka.Send(itIsThePresent, Integration.Kafka.TopicForCustomers,
				new CustomerSaleMessage(customer.Identifier,
				customer.AccountNumber, 
				this.sales.CurrentStore.Id,
                domain,
                now,
				Currency.Factory(orderCompleted.CurrencyCode, orderCompleted.Total()),
				(int)customer.Player.Agent));
		}

		internal Settlers Setters
		{
			get
			{
				return settlers;
			}
		}

		internal void GradeWagerForAccounting(bool itIsThePresent, TicketWager wager, DateTime now)
		{
			if (wager == null) throw new ArgumentNullException(nameof(wager));
			if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");

			const string employeeName = "";

			accounting.GradeTicketWager(itIsThePresent, wager, now, employeeName);
		}

		internal void DeleteTickets(bool itIsThePresent, IEnumerable<Ticket> tickets, DateTime now, string employeeName)
		{
			if (tickets == null) throw new ArgumentNullException(nameof(tickets));
			if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");

			accounting.DeleteTickets(itIsThePresent, tickets, this.Sales.CurrentStore, now, employeeName);
		}

		internal void DeleteWager(bool itIsThePresent, TicketWager wager, DateTime now, string employeeName)
		{
			if (wager == null) throw new ArgumentNullException(nameof(wager));
			if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");

			accounting.DeleteWager(itIsThePresent, wager, now, employeeName);
		}

		private bool RefundFor(bool itIsThePresent, Bet betToRemove, MarchMadnessTournament tournament, DateTime now)
		{
			var betsToRemove = new List<Bet>();
			var isItPossibleToRemoveTransaction = (betToRemove != null) && (betToRemove is BetLocked);
			if (isItPossibleToRemoveTransaction)
			{
				betsToRemove.Add(betToRemove);
				var player = betToRemove.Player;
				var bracket = (MarchMadnessBracket)betToRemove.Gameboard;
				var pool = (Pool)betToRemove.Reward;
				var rules = GetMarchMadnessEdition(bracket.Year);

				var isTransactionFromBracket = rules.IsGeneralPool(pool);
				if (isTransactionFromBracket)
				{
					var betsWithTheSameBracketAndDifferentBetToRemove = BetsWithTheSameBracketAndDistinctOfABet(bracket, betToRemove);
					var hasMoreTransactionRelated = betsWithTheSameBracketAndDifferentBetToRemove.Count() > 0;
					if (hasMoreTransactionRelated)
					{
						var poolesWithTheSameBracket = betsWithTheSameBracketAndDifferentBetToRemove.Select(b => b.Reward).Cast<Pool>();
						if (poolesWithTheSameBracket.Any(aPool => !IsPossibleToCancelTransactionForGroup(player, bracket, aPool))) return false;
						foreach (var otherPool in poolesWithTheSameBracket)
						{
							CancelTransactionForGroup(player, bracket, otherPool);
						}
						betsToRemove.AddRange(betsWithTheSameBracketAndDifferentBetToRemove);
					}

					gameboards.Remove(player, bracket);
					tournament.Remove(bracket);
				}
				else
				{
					if (!IsPossibleToCancelTransactionForGroup(player, bracket, pool)) return false;
					CancelTransactionForGroup(player, bracket, pool);
				}

				if (isItPossibleToRemoveTransaction)
				{
					for (int index = betsToRemove.Count - 1; index >= 0; index--)
					{
						var bet = betsToRemove.ElementAt(index);
						const int MAXIMUM_NAME_LENGTH_TO_SHOW = 10;
						var firstLettersOfName = bracket.Name.Length > MAXIMUM_NAME_LENGTH_TO_SHOW ? $"{bracket.Name.Substring(0, MAXIMUM_NAME_LENGTH_TO_SHOW)}..." : bracket.Name;
						var additionalInfo = $"Bracket #{bracket.Id} name {firstLettersOfName} price {betToRemove.Contribution}";
						accounting.Refund(itIsThePresent, bet.Number, player.AccountNumber, bet.Contribution, $"Brackets: Refund Bracket #{bracket.Id}", additionalInfo, now);
						bet.Player.Remove(bet);
					}
				}
			}
			return isItPossibleToRemoveTransaction;
		}

		internal bool RefundFor(bool itIsThePresent, int authorizationId, MarchMadnessTournament tournament, DateTime now)
		{
			if (tournament == null) throw new ArgumentNullException(nameof(tournament));
			if (authorizationId <= 0) throw new GameEngineException($"{nameof(authorizationId)} {authorizationId} must be greater than 0");
			if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");

			var betToRemove = book.FindBetForTournamentBy(authorizationId, tournament.Year);
			var isItPossibleToRemoveTransaction = RefundFor(itIsThePresent, betToRemove, tournament, now);
			return isItPossibleToRemoveTransaction;
		}

		private bool IsPossibleToCancelTransactionForGroup(Bets.Player player, MarchMadnessBracket bracket, Pool pool)
		{
			var rules = GetMarchMadnessEdition(bracket.Year);
			var room = rules.Room(pool);
			var isPossibleRemoveTransaction = room.HasOnlyOneParticipant();
			return isPossibleRemoveTransaction;
		}

		private void CancelTransactionForGroup(Bets.Player player, MarchMadnessBracket bracket, Pool pool)
		{
			var rules = GetMarchMadnessEdition(bracket.Year);
			var room = rules.Room(pool);
			if (room.HasOnlyThisOwnerParticipating(player))
			{
				room.Remove(bracket);
				rules.RemoveRoom(pool, room);
			}
			else if (room.HasOnlyOneParticipant())
			{
				room.Remove(bracket);
			}
		}

		internal Betboard Betboard(Tournament tournament)
		{
			if (tournament == null) throw new ArgumentNullException(nameof(tournament));

			var betBoard = this.book.GetOrCreateBetboard(tournament, players);
			return betBoard;
		}


		[Obsolete("Do not use it is for legacy purposes")]
		internal Bet CreateNewBet(Bets.Player player, Gameboard gameboard, Reward reward, DateTime creationDate)
		{
			if (player == null) throw new ArgumentNullException(nameof(player));
			if (gameboard == null) throw new ArgumentNullException(nameof(gameboard));
			if (reward == null) throw new ArgumentNullException(nameof(reward));
			BetAlive bet = (BetAlive)book.GetNewBet(player, gameboard, reward, creationDate);
			return bet;
		}

		internal Bet CreateNewBet(Bets.Player player, Gameboard gameboard, Reward reward, DateTime creationDate, int nextBetNumber)
		{
			if (player == null) throw new ArgumentNullException(nameof(player));
			if (gameboard == null) throw new ArgumentNullException(nameof(gameboard));
			if (reward == null) throw new ArgumentNullException(nameof(reward));
			BetAlive bet;
			if (nextBetNumber < Book.NEW_CONSECUTIVE)
			{
				bet = (BetAlive)book.GetNewBet(player, gameboard, reward, creationDate);
				bet.Number = nextBetNumber;
			}
			else
			{
				bet = (BetAlive)book.NewBet(player, gameboard, reward, creationDate, nextBetNumber);
			}

			return bet;
		}

		internal IEnumerable<Pool> Pools()
		{
			IEnumerable<Pool> result = book.Rewards().Select(x => (Pool)x).Distinct();
			return result;
		}

		internal Tournaments Tournaments
		{
			get
			{
				return tournaments;
			}
		}

		internal Player.EncryptionHelper EncryptionHelper { get; } = new Player.EncryptionHelper();

        private Games.Lotto.PicksLotteryGame lotto900 = null;
        internal Games.Lotto.PicksLotteryGame Lotto900()
		{
            if (lotto900 == null)
            {
                lotto900 = LotteryGamesPool.PicksLotteryGame;
            }
            return lotto900;
		}

		private LotteryGamesPool lotteryGamesPool;
		internal LotteryGamesPool LotteryGamesPool
		{
			get
			{
				if (lotteryGamesPool == null) lotteryGamesPool = new LotteryGamesPool(this);
                return lotteryGamesPool;
            }
		}		

        internal MarchMadnessEdition GetMarchMadnessEdition(int year)
		{
			MarchMadnessEdition result = rules.FirstOrDefault(rule => rule.Year == year);
			bool alreadyExists = result != null;
			if (alreadyExists)
			{
				return result;
			}
			if (year == 2019)
			{
				decimal[] fees = new decimal[] { 1, 5, 10, 25 };
				result = new MarchMadnessEdition(this, year);
				result.CreateGeneralPools(result.RoundsToSaleBrackets().ToArray(), fees);
				rules.Add(result);
			}
			else if (year != 0)
			{
				result = new MarchMadnessEdition(this, year);
				rules.Add(result);
			}
			else
			{
				throw new GameEngineException("There are not defined rules for this MM tournament. Make sure Tournament setting are updated.");
			}
			return result;
		}

		private CustomerBalancesList balancesList;

        private TimeZoneInfo companyTimeZone = TimeZoneInfo.Local;

        internal string TimeZone
        {
            get
            {
                return TimeZoneConverter.Instance.CountryCode(companyTimeZone);
            }
            set
            {
                if (string.IsNullOrWhiteSpace(value)) throw new GameEngineException("The time zone is empty.");
                companyTimeZone = TimeZoneConverter.Instance.GetTimeZoneInfo(value);
            }
        }

        internal CustomerBalancesList CustomerBalancesList
		{
			get
			{
				if (balancesList != null) return balancesList;
				balancesList = new CustomerBalancesList(this);
				return balancesList;
			}
		}

		internal Order OrderByNumber(int orderNumber)
		{
			var order = orders.SearchByNumber(orderNumber);
			return order;
		}

		internal void Lock(BetAlive bet)
		{
			if (bet == null) throw new ArgumentNullException(nameof(bet));

			var betLocked = bet.Lock();
			book.Lock(bet, (BetLocked)betLocked);
		}

		internal Bet FindBetById(int betNumber)
		{
			if (betNumber <= 0) throw new GameEngineException($"{nameof(betNumber)} {betNumber} is not valid.");
			var result = book.FindBetById(betNumber);
			return result;
		}

		internal IEnumerable<Bet> ListAllBets()
		{
			var result = book.ListAllBets();
			return result;
		}

		internal bool ExistBetId(int betNumber)
		{
			if (betNumber <= 0) throw new GameEngineException($"{nameof(betNumber)} {betNumber} is not valid.");
			var result = book.ExistBetId(betNumber);
			return result;
		}

		internal IEnumerable<Bet> FindBetsInRange(int theLowestBetId, int theHighestBetId)
		{
			if (theLowestBetId <= 0) throw new GameEngineException($"{nameof(theLowestBetId)} {theLowestBetId} is not valid.");
			if (theHighestBetId <= 0) throw new GameEngineException($"{nameof(theHighestBetId)} {theHighestBetId} is not valid.");
			var bets = book.FindBetsInRange(theLowestBetId, theHighestBetId);

			return bets;
		}

		internal SummaryOfBetsGroupedByPoolFee WinnerBetsInGeneralPools(int year)
		{
			var tournament = tournaments.GetMarchMadnessTournamentOf(year);
			if (!tournament.IsReadyToPay()) throw new GameEngineException("The tournament is not ready to pay");

			var rules = GetMarchMadnessEdition(year);
			var generalPools = rules.GeneralPools().ToList();
			var superLeaguePools = rules.GeneralPoolsForSuperLeague().ToList();
			var finalFourPools = rules.GeneralPoolsForFinalFour().ToList();
			var result = generalPools.Where(x => !superLeaguePools.Contains(x) && !finalFourPools.Contains(x)).ToList();

			var bets = book.WinnerBetsInGeneralPools(result);
			return bets;
		}

		internal SummaryOfBetsGroupedByPoolFee WinnerBetsForSuperLeagueInGeneralPools(int year)
		{
			var tournament = tournaments.GetMarchMadnessTournamentOf(year);
			if (!tournament.IsReadyToPay()) throw new GameEngineException("The tournament is not ready to pay");

			var rules = GetMarchMadnessEdition(year);
			var generalPools = rules.GeneralPoolsForSuperLeague().ToList();
			var bets = book.WinnerBetsInGeneralPools(generalPools);
			return bets;
		}

		internal SummaryOfBetsGroupedByPoolFee WinnerBetsForFinalFourInGeneralPools(int year)
		{
			var tournament = tournaments.GetMarchMadnessTournamentOf(year);
			if (!tournament.IsReadyToPay()) throw new GameEngineException("The tournament is not ready to pay");

			var rules = GetMarchMadnessEdition(year);
			var generalPools = rules.GeneralPoolsForFinalFour().ToList();
			var bets = book.WinnerBetsInGeneralPools(generalPools);
			return bets;
		}

		internal SummaryOfBetsLockedOrPayed WinnerBetsInNonGeneralPools(int year)
		{
			var tournament = tournaments.GetMarchMadnessTournamentOf(year);
			if (!tournament.IsReadyToPay()) throw new GameEngineException("The tournament is not ready to pay");

			var rules = GetMarchMadnessEdition(year);
			var generalPools = rules.GeneralPools().ToList();
			var bets = book.WinnerBetsInNonGeneralPools(generalPools);
			return bets;
		}

		internal void SetDocumentNumber(Bets.Player player, Gameboard gameboard, Pool pool, int documentNumber)
		{
			if (player == null) throw new ArgumentNullException(nameof(player));
			if (gameboard == null) throw new ArgumentNullException(nameof(gameboard));
			if (pool == null) throw new ArgumentNullException(nameof(pool));
			if (documentNumber <= 0) throw new GameEngineException($"{nameof(documentNumber)} must be greater than 0");

			book.SetDocumentNumber(player, gameboard, pool, documentNumber);
		}

		internal bool HasDocumentNumberAssigned(int documentNumber)
		{
			if (documentNumber < 0) throw new GameEngineException($"{nameof(documentNumber)} must be greater than 0");
			var result = book.HasDocumentNumberAssigned(documentNumber);
			return result;
		}

		internal int AuthorizationId(Bets.Player player, Gameboard gameboard, Pool pool)
		{
			if (player == null) throw new ArgumentNullException(nameof(player));
			if (gameboard == null) throw new ArgumentNullException(nameof(gameboard));
			if (pool == null) throw new ArgumentNullException(nameof(pool));

			var result = book.AuthorizationId(player, gameboard, pool);
			return result;
		}

		internal void PayPrize(bool itIsThePresent, BetPayed bet, DateTime now, string employeeName)
		{
			if (bet == null) throw new ArgumentNullException(nameof(bet));
			if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (string.IsNullOrEmpty(employeeName)) throw new ArgumentException(nameof(employeeName));

			var description = string.Empty;
			var additionalInfo = string.Empty;
			if (bet.Gameboard is MarchMadnessBracket)
			{
				var bracket = (MarchMadnessBracket)bet.Gameboard;
				const int MAXIMUM_NAME_LENGTH_TO_SHOW = 10;
				var firstLettersOfName = bracket.Name.Length > MAXIMUM_NAME_LENGTH_TO_SHOW ? $"{bracket.Name.Substring(0, MAXIMUM_NAME_LENGTH_TO_SHOW)}..." : bracket.Name;
				additionalInfo = $"Bracket #{bracket.Id} name {firstLettersOfName} price {bet.Contribution}";
				var pool = (Pool)bet.Reward;
				var rules = GetMarchMadnessEdition(bracket.Year);
				if (rules.IsGeneralPool(pool))
				{
					description = $"Brackets: Prize Payout Bracket #{bracket.Id}";
				}
				else
				{
					var room = rules.Room(pool);
					description = $"Brackets: Prize Payout Group:'{room.Name}' Bracket #{bracket.Id}";
				}

				var tournament = tournaments.GetMarchMadnessTournamentOf(bracket.Year);
				tournament.AddLogFor(bracket, $"{employeeName} approved to pay ${bet.AmountApproved} instead of ${bet.AmountToPay} at {now}<br>");
			}
			else
			{
				description = "Betting Contest paying prize to winner";
			}
			accounting.PayPrize(itIsThePresent, bet.Number, bet.Player.AccountNumber, bet.AmountApproved, description, additionalInfo, now);
		}

		internal BetPayed NewBetPayed(Bets.Player player, Gameboard gameboard, Pool pool, decimal prize)
		{
			if (player == null) throw new ArgumentNullException(nameof(player));
			if (gameboard == null) throw new ArgumentNullException(nameof(gameboard));
			if (pool == null) throw new ArgumentNullException(nameof(pool));
			if (prize <= 0) throw new GameEngineException("The prize must be greater than 0");
			Commons.ValidateAmount(prize);

			var authorizationId = book.AuthorizationId(player, gameboard, pool);
			if (authorizationId <= 0) throw new GameEngineException($"{nameof(authorizationId)} is {authorizationId}. It must be greater than 0 to be paid");
			var betPayed = book.NewBetPayed(player, gameboard, pool, prize);
			return betPayed;
		}

		internal void ReturnPrize(bool itIsThePresent, Bets.Player player, Gameboard gameboard, Pool pool, DateTime now, string employeeName)
		{
			if (player == null) throw new ArgumentNullException(nameof(player));
			if (gameboard == null) throw new ArgumentNullException(nameof(gameboard));
			if (pool == null) throw new ArgumentNullException(nameof(pool));
			if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (string.IsNullOrEmpty(employeeName)) throw new ArgumentException(nameof(employeeName));

			var bet = NewBetLockedFromBetPayed(player, gameboard, pool);
			var description = "Brackets: Payout Adjustment";
			var additionalInfo = string.Empty;
			if (bet.Gameboard is MarchMadnessBracket)
			{
				var bracket = (MarchMadnessBracket)bet.Gameboard;
				const int MAXIMUM_NAME_LENGTH_TO_SHOW = 10;
				var firstLettersOfName = bracket.Name.Length > MAXIMUM_NAME_LENGTH_TO_SHOW ? $"{bracket.Name.Substring(0, MAXIMUM_NAME_LENGTH_TO_SHOW)}..." : bracket.Name;
				additionalInfo = $"Bracket #{bracket.Id} name {firstLettersOfName} price {bet.Contribution}";

				var tournament = tournaments.GetMarchMadnessTournamentOf(bracket.Year);
				tournament.AddLogFor(bracket, $"{employeeName} returned prize of ${bet.AmountApproved} at {now}<br>");
			}
			accounting.ReturnPrize(itIsThePresent, bet.Number, bet.Player.AccountNumber, bet.AmountApproved, description, additionalInfo, now);
		}

		internal BetLocked NewBetLockedFromBetPayed(Bets.Player player, Gameboard gameboard, Pool pool)
		{
			if (player == null) throw new ArgumentNullException(nameof(player));
			if (gameboard == null) throw new ArgumentNullException(nameof(gameboard));
			if (pool == null) throw new ArgumentNullException(nameof(pool));

			var bet = book.NewBetLockedFromBetPayed(player, gameboard, pool);
			return bet;
		}

		internal void CancelBet(Bets.Player player, Gameboard gameboard, Pool pool)
		{
			if (player == null) throw new ArgumentNullException(nameof(player));
			if (gameboard == null) throw new ArgumentNullException(nameof(gameboard));
			if (pool == null) throw new ArgumentNullException(nameof(pool));

			book.CancelBet(player, gameboard, pool);
		}

		internal void PunchBet(Bets.Player player, Gameboard gameboard, Pool pool)
		{
			if (player == null) throw new ArgumentNullException(nameof(player));
			if (gameboard == null) throw new ArgumentNullException(nameof(gameboard));
			if (pool == null) throw new ArgumentNullException(nameof(pool));

			book.PunchBet(player, gameboard, pool);
		}

		internal void LockBetPunched(Bets.Player player, Gameboard gameboard, Pool pool)
		{
			if (player == null) throw new ArgumentNullException(nameof(player));
			if (gameboard == null) throw new ArgumentNullException(nameof(gameboard));
			if (pool == null) throw new ArgumentNullException(nameof(pool));

			book.LockBetPunched(player, gameboard, pool);
		}

		internal void LockBetCanceled(Bets.Player player, Gameboard gameboard, Pool pool)
		{
			if (player == null) throw new ArgumentNullException(nameof(player));
			if (gameboard == null) throw new ArgumentNullException(nameof(gameboard));
			if (pool == null) throw new ArgumentNullException(nameof(pool));

			book.LockBetCanceled(player, gameboard, pool);
		}

		internal void RemoveBet(Bets.Player player, Gameboard gameboard, Reward reward)
		{
			if (player == null) throw new ArgumentNullException(nameof(player));
			if (gameboard == null) throw new ArgumentNullException(nameof(gameboard));
			if (reward == null) throw new ArgumentNullException(nameof(reward));

			book.RemoveBet(player, gameboard, reward);
		}

		internal void RemoveDisposedGameboards()
		{
			book.RemoveDisposedGameboards();
			orders.RemoveOrdersWithoutGameboards();
		}

		internal void UpdateAmountApproved(Bets.Player player, Gameboard gameboard, Pool pool, decimal amountApproved)
		{
			if (player == null) throw new ArgumentNullException(nameof(player));
			if (gameboard == null) throw new ArgumentNullException(nameof(gameboard));
			if (pool == null) throw new ArgumentNullException(nameof(pool));
			if (amountApproved < 0) throw new GameEngineException("The amount approved must be greater or equal than 0");

			book.UpdateAmountApproved(player, gameboard, pool, amountApproved);
		}

		internal List<Gameboard> GameboardsOnPoolDistinctOf(Player player, Pool pool)
		{

			if (pool == null) throw new ArgumentNullException(nameof(pool));
			if (player == null) throw new ArgumentNullException(nameof(player));

			var result = book.GameboardsOnPoolDistinctOf(player.MyGameboardsOnPool(pool), pool);
			return result;
		}

		internal IEnumerable<Gameboard> GameboardsOf(Pool pool)
		{
			if (pool == null) throw new ArgumentNullException(nameof(pool));

			var result = book.GameboardsOf(pool);
			return result;
		}

		internal IEnumerable<Bets.Player> PlayersWithAvatarsToBeApproved(string gameName, int initialIndex, int finalIndex)
		{
			if (string.IsNullOrWhiteSpace(gameName)) throw new ArgumentNullException(nameof(gameName));

			var playersWithAvatarsToBeApproved = players.WithAvatarsToBeApproved(gameName, initialIndex, finalIndex);
			return playersWithAvatarsToBeApproved;
		}

		internal int CountPlayersWithAvatarsToBeApproved(string gameName)
		{
			if (string.IsNullOrWhiteSpace(gameName)) throw new ArgumentNullException(nameof(gameName));

			var count = players.CountAvatarsToBeApproved(gameName);
			return count;
		}

		internal IEnumerable<Gameboard> GameboardsOf(IEnumerable<Pool> pools)
		{
			if (pools == null) throw new ArgumentNullException(nameof(pools));

			var result = book.GameboardsOf(pools);
			return result;
		}

		internal IEnumerable<Gameboard> GameboardsWithSearchCriteriaSimilarThan(string searchCriteria)
		{
			var result = book.GameboardsWithSearchCriteriaSimilarThan(searchCriteria);
			return result;
		}

		internal MarketingKPIs MarketingKPIs()
		{
			return this.marketingKPIs;
		}

		internal IEnumerable<Player> SearchPlayersWithThisIds(string listOdPlayersIdsSerializated)
		{
			if (String.IsNullOrEmpty(listOdPlayersIdsSerializated)) throw new ArgumentException(nameof(listOdPlayersIdsSerializated));

			var result = players.WithThisIds(listOdPlayersIdsSerializated);
			return result;
		}

		internal IEnumerable<Customer> SearchCustomersWithTheseIds(List<string> customersIds)
		{
			if (customersIds.Count == 0) throw new GameEngineException($"{nameof(customersIds)} cannot be empty");

			var result = customers.WithTheseIds(customersIds);
			return result;
		}

		internal bool ExistsCustomersWithTheseIds(List<string> customersIds)
		{
			if (customersIds.Count == 0) throw new GameEngineException($"{nameof(customersIds)} cannot be empty");

			var result = customers.ExistsAllTheseIds(customersIds);
			return result;
		}

		private IEnumerable<Bet> BetsWithTheSameBracketAndDistinctOfABet(Gameboard gameboard, Bet bet)
		{
			var result = book.BetsOfGameboardDistinctOfBet(gameboard, bet);
			return result;
		}

		internal void RefundBetsWithPoolWithOnlyOneMember(bool itIsThePresent, MarchMadnessTournament tournament, Round round, DateTime now)
		{
			if (tournament == null) throw new ArgumentNullException(nameof(tournament));
			if (round == null) throw new ArgumentNullException(nameof(round));
			if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");

			var betsOfPoolWithOnlyOneBracket = BetsWithPoolWithOnlyOneMemberIn(tournament, round);
			for (int index = betsOfPoolWithOnlyOneBracket.Count() - 1; index >= 0; index--)
			{
				var bet = betsOfPoolWithOnlyOneBracket.ElementAt(index);
				var rules = GetMarchMadnessEdition(tournament.Year);
				var room = rules.Room(bet.Reward);

				var isItRefunded = RefundFor(itIsThePresent, bet, tournament, now);
				if (isItRefunded)
				{
					Subscriber me = bet.Player;
					var notification = new Notification(tournament, $"Group #{room.Id} '{room.Name}' has been refunded.");
					notification.CreationDate = now;
					me.Messages.SendMessageTo(itIsThePresent, me, notification);
				}
			}
		}

		private IEnumerable<Bet> BetsWithPoolWithOnlyOneMemberIn(MarchMadnessTournament tournament, Round round)
		{
			var rules = Tournaments.Company.GetMarchMadnessEdition(tournament.Year);
			var pools = rules.PoolsWithOnlyOneMemberIn(round);
			var result = book.BetsFrom(pools);
			return result;
		}

		internal bool BetHasDefaultAuthorizationId(Player player, Gameboard gameboard, Reward reward)
		{
			if (player == null) throw new ArgumentNullException(nameof(player));
			if (gameboard == null) throw new ArgumentNullException(nameof(gameboard));
			if (reward == null) throw new ArgumentNullException(nameof(reward));

			var result = book.BetHasDefaultAuthorizationId(player, gameboard, reward);
			return result;
		}

		internal Bet FirstBetOf(MarchMadnessBracket gameboard)
		{
			if (gameboard == null) throw new ArgumentNullException(nameof(gameboard));

			var rules = Tournaments.Company.GetMarchMadnessEdition(gameboard.Year);
			var result = book.BetsOf(gameboard).First(bet => bet.Reward.GetType().Equals(typeof(Pool)) && rules.IsGeneralPool((Pool)bet.Reward));
			return result;
		}

		internal DateTime CreationDateOf(MarchMadnessBracket gameboard)
		{
			if (gameboard == null) throw new ArgumentNullException(nameof(gameboard));

			var rules = Tournaments.Company.GetMarchMadnessEdition(gameboard.Year);
			var betFound = book.BetsOf(gameboard).
				FirstOrDefault(bet => bet.Reward.GetType().Equals(typeof(Pool)) && rules.IsGeneralPool((Pool)bet.Reward));
			if (betFound == null) return default(DateTime);
			return betFound.CreationDate;
		}

		internal bool IsThisNickNameAlreadyTakenByAnOtherUser(Player currentPlayer, string nickname)
		{
			if (String.IsNullOrWhiteSpace(nickname)) throw new ArgumentNullException("The nickname is required.");

			return customers.IsThisNickNameAlreadyTakenByAnOtherUser(currentPlayer, nickname);
		}

		internal int NextPlayerConsecutive()
		{
			return players.NextConsecutive();
		}

		internal void SendAnnouncementTo(bool itIsThePresent, Announcement campaign, string criteria, DateTime now, string employeeName)
		{
			if (campaign == null) throw new ArgumentNullException(nameof(campaign));
			if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");
			if (string.IsNullOrWhiteSpace(employeeName)) throw new ArgumentNullException(nameof(employeeName));
			if (!IsCriteriaValid(criteria)) throw new GameEngineException($"{nameof(criteria)} contains some invalid character");

			Targeting target;
			Segment segment;

			if (string.Equals(criteria, "all", StringComparison.OrdinalIgnoreCase))
			{
				target = new Targeting(campaign, this.players, Segmentation.OVERALL_MARKET);
				segment = target.CalculateAllTarget(now);
			}
			else
			{
				Segmentation segmentation = new Segmentation(criteria);
				target = new Targeting(campaign, this.players, segmentation);
				segment = target.CalculateCustomTarget(now);
			}

			campaign.Segment = segment;
			campaign.SentBy = employeeName;
			campaign.Criteria = target.Segmentation.Criteria;
			campaign.SentDate = now;
			campaign.WasSent = true;

			if (itIsThePresent && segment.Storage != null)
			{
				if (segment != null) segment.SendAnnouncement(itIsThePresent, now);
				int lastMessageId = campaign.LastMessageIdSaved;
				segment.StoreAnnouncement("all", lastMessageId, now, employeeName);
			}
		}

		const char INVALID_CHAR = '\'';
		bool IsCriteriaValid(string criteria)
		{
			var result = criteria.IndexOf(INVALID_CHAR, StringComparison.OrdinalIgnoreCase) == -1;
			return result;
		}

		internal CustomerTypes CustomerTypes()
		{
			if (customerTypes == null) customerTypes = new CustomerTypes();
			return customerTypes;
		}

        internal RiskRatingTypes RiskRatingTypes()
        {
            if (riskRatingTypes == null) riskRatingTypes = new RiskRatingTypes();
            return riskRatingTypes;
        }

        internal bool IsAlreadyRegisteredTenantAndStore { get; private set; }
		internal void RegisterTenantAndStore(bool itIsThePresent, string tenantName, CustomSettingsCollection customSettings)
		{
			IsAlreadyRegisteredTenantAndStore = true;
			if (itIsThePresent && (Integration.UseKafka || Integration.UseKafkaForAuto))
			{
				var store = sales.CurrentStore;
				var msg = new StoreRegistrationMessage(tenantName, store.Name, store.Alias, customSettings);
				Integration.Kafka.Send(itIsThePresent, Integration.Kafka.TopicForStoreRegistration, msg);
			}
		}

		internal void ReplaceStoreIdAndCreateTenant(string tenantName, int tenantId, int storeId)
		{
			var store = sales.CurrentStore;
			store.Id = storeId;

			var tenant = (Tenant)System.Tenants.Add(tenantId, tenantName);
			tenant.MakeCurrent();

			Integration.CurrentStoreId = storeId;
			Integration.CurrentTenantId = tenantId;
		}
		internal Players Players
		{
			get
			{
				return players;
			}
		}
    }
}
