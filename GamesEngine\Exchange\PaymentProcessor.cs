﻿using GamesEngine.Business;
using GamesEngine.Custodian;
using GamesEngine.Domains;
using GamesEngine.Finance;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using town.connectors.drivers;
using town.connectors.drivers.artemis;
using static GamesEngine.Business.WholePaymentMethods;
using static GamesEngine.Business.WholePaymentProcessor;
using static town.connectors.drivers.Result;

namespace GamesEngine.Exchange
{
    public class DespositBody // Its a class because AccountNumber its been used as reference.
    {
        public DespositBody(string accountNumber, string identificationDocumentNumber, decimal amount, string description, DateTime date, string additionalInfo, string employeeName, Domain domain)
        {
            Identifier = identificationDocumentNumber;
            Amount = amount;
            Description = description;
            Date = date;
            AdditionalInfo = additionalInfo;
            EmployeeName = employeeName;
            Domain = domain;
            AccountNumber = accountNumber;
		}

		public DespositBody(
            string accountNumber, string identificationDocumentNumber, decimal amount, string description, DateTime date, string additionalInfo, string employeeName, Domain domain,
			string path, PaymentMethod paymentMethod, int entityId, int storeId, string sendersName, string country, string state, string city, string controlNum, int providerId
		) : this(accountNumber, identificationDocumentNumber, amount, description, date, additionalInfo, employeeName, domain)
		{
			AgentPath = path;
			SendersName = sendersName;
			Country = country;
			State = state;
			City = city;
			ControlNum = controlNum;
			ProviderId = providerId;
			PaymentMethod = paymentMethod;
			EntityId = entityId;
			StoreId = storeId;
		}

        public DespositBody(
            string accountNumber, string identificationDocumentNumber, decimal amount, string description, DateTime date, string additionalInfo, string employeeName, Domain domain,
            string path, PaymentMethod paymentMethod, int entityId, int storeId, string sendersName, string country, string state, string city, string controlNum, int providerId,
			string reference) : this(accountNumber, identificationDocumentNumber, amount, description, date, additionalInfo, employeeName, domain, path, paymentMethod, entityId, storeId, sendersName, country, state, city, controlNum, providerId)
        {
			Reference = reference;
        }

        public string Reference { get; set; }
        public string AccountNumber { get; set; }
        public string Identifier { get; }
        public decimal Amount { get; }
        public string Description { get; }
        public DateTime Date { get; }
        public string AdditionalInfo { get; }
        public string EmployeeName { get; }
        public Domain Domain { get; }
		public string AgentPath { get; }
		public List<ArtemisToWinByDrawAndNumber> Tickets { get; set; }

		public string SendersName { get; }
		public string Country { get; }
		public string State { get; }
		public string City { get; }
		public string ControlNum { get; }
		public int ProviderId { get; }
		public PaymentMethod PaymentMethod { get; }
		public int EntityId { get; }
		public int StoreId { get; }
	}

	public class WithdrawBody
	{
		public WithdrawBody(string identificationDocumentNumber, decimal amount, string description, DateTime date, string additionalInfo, int domainId, string domainUrl, PaymentMethod paymentMethod, int entityId, 
			int storeId, string receiversName, string country, string state, string city)
		{
			Identifier = identificationDocumentNumber;
			Amount = amount;
			Description = description;
			Date = date;
			AdditionalInfo = additionalInfo;
			AccountNumber = "N/A";
			DomainId = domainId;
			DomainUrl = domainUrl;
			Domain = new Domain(false, domainId, domainUrl, PaymentChannels.Agents.INSIDER);
			PaymentMethod = paymentMethod;
			EntityId = entityId;
			StoreId = storeId;

			ReceiversName = receiversName;
			Country = country;
			State = state;
			City = city;
		}

        public WithdrawBody(string identificationDocumentNumber, decimal amount, string description, DateTime date, string additionalInfo, int domainId, string domainUrl, PaymentMethod paymentMethod, int entityId, int storeId, string receiversName, string country, string state, string city,
            Domain domain, string employeeName, string reference) : this(identificationDocumentNumber, amount, description, date, additionalInfo, domainId, domainUrl, paymentMethod, entityId, storeId, receiversName, country, state, city)
        {
            Domain = domain;
            EmployeeName = employeeName;
            Reference = reference;
        }

		public string EmployeeName { get; }
        public Domain Domain { get; }
        public string Reference { get; set; }
        public string Identifier { get; }
		public decimal Amount { get; }
		public string Description { get; }
		public DateTime Date { get; }
		public string AdditionalInfo { get; }
		public string AccountNumber { get; set; }
		public int DomainId { get; }
		public string DomainUrl { get; }

		public PaymentMethod PaymentMethod { get; }
		public int EntityId { get; }
		public int StoreId { get; }

		public string ReceiversName { get; }
		public string Country { get; }
		public string State { get; }
		public string City { get; }
	}

	[Puppet]
	public class PaymentProcessorsAndActionsByDomains : Objeto
	{
		private readonly List<PaymentProcessor> processorsEnabled = new List<PaymentProcessor>();
		private readonly Dictionary<int, Dictionary<string, MultipleProcessors>> agentByDomainWholeDrivers;
		internal IEnumerable<PaymentProcessor> ActionsWithDomainAndDriver
		{
			get
			{
				return processorsEnabled;
			}
		}

		internal PaymentProcessorsAndActionsByDomains(WholePaymentProcessor processors)
		{
			Processors = processors;
			foreach (var processor in processors.Values)
			{
				if (!Contains(processor)) Enable(processor);
			}
            agentByDomainWholeDrivers = new Dictionary<int, Dictionary<string, MultipleProcessors>>();

        }

		internal void Reload(IEnumerable<PaymentProcessor> oldActionsWithDomainAndDriver, MultipleProcessors processorsToLoad)
		{
			if (oldActionsWithDomainAndDriver != null && oldActionsWithDomainAndDriver.Count() == 0) return;
			foreach (var processorEnabled in oldActionsWithDomainAndDriver)
			{
				var processorIsAvailable = processorsToLoad.FirstOrDefault(
					x => x == processorEnabled &&
					x.Visible) != null;

				if (processorIsAvailable && !Contains(processorEnabled))
				{
					Enable(processorEnabled);
				}
			}
		}

		internal void ReorderEnabledProcessors(ProcessorTransaction transactionType, Coin coin, List<int> entityIdsToReorder)
		{
			var processorsToReorder = new List<PaymentProcessor>();
			var processorsToRemove = processorsEnabled.Where(processor => processor.ContainsTransactionType(transactionType) && processor.Coin == coin && entityIdsToReorder.Contains(processor.Entity.Id)).ToList();
			if (!processorsToRemove.Any() || processorsToRemove.Count() == 1) return;

			for (int index = processorsEnabled.Count - 1; index >= 0; index--)
			{
				if (processorsToRemove.Contains(processorsEnabled[index]))
				{
					processorsToReorder.Add(processorsEnabled[index]);
					processorsEnabled.Remove(processorsEnabled[index]);
				}
			}
			processorsEnabled.Sort(new EntityOrderComparer(entityIdsToReorder));
			foreach (var processor in processorsToReorder)
			{
				processorsEnabled.Add(processor);
			}
		}

		public void Reset()
		{
			Clear();
		}

		internal IEnumerable<string> CurrenciesAsText()
		{
			List<string> result = new List<string>();

			foreach (PaymentProcessor driver in Processors.Values)
			{
				if (!result.Contains(driver.CurrencyIso4217Code)) result.Add(driver.CurrencyIso4217Code);
			}
			return result;
		}

		internal void Disable(PaymentProcessor processor)
		{
			processorsEnabled.Remove(processor);
		}

		internal void Clear()
		{
			processorsEnabled.Clear();
		}

		internal void Enable(PaymentProcessor processor)
		{
			if (processor == null) throw new ArgumentException(nameof(processor));

			ValidateDriverItExists(processor);
			CreateSetting(processor);

			if (Contains(processor)) throw new GameEngineException("Already resgistered");
			processorsEnabled.Add(processor);
		}

        internal void AssociateProcessorWith(int agentId, string domain, PaymentProcessor processor)
        {
            if (processor == null) throw new ArgumentException(nameof(processor));

            Dictionary<string, MultipleProcessors> afiliateProcessors;
            if (!agentByDomainWholeDrivers.TryGetValue(agentId, out afiliateProcessors))
            {
                afiliateProcessors = new Dictionary<string, MultipleProcessors>();
                agentByDomainWholeDrivers.Add(agentId, afiliateProcessors);
            }

            MultipleProcessors processors;
            if (!afiliateProcessors.TryGetValue(domain, out processors))
            {
                processors = new MultipleProcessors();
                afiliateProcessors.Add(domain, processors);
            }

			if (!processor.Transactions.Any()) throw new GameEngineException($"Processor '{processor.Id}' has no transaction type.");
			if (processor.Transactions.Count != 1) throw new GameEngineException($"Processor '{processor.Id}' has more than one transaction type.");

            ProcessorTransaction processorTransaction = processor.Transactions.First();
			TransactionType transactionType;
			if (!Enum.TryParse(processorTransaction.Name, out transactionType)) throw new GameEngineException($"Transaction type '{processorTransaction}' is not valid.");

            processors.AddPaymentProcessor(transactionType, processor.Coin, processor.Entity, processor.PaymentMethodType, processor);
        }

        internal bool Contains(PaymentProcessor processor)
		{
			foreach (var actionWithDomainAndDriver in processorsEnabled)
			{
				if (actionWithDomainAndDriver.Driver.Id == processor.Driver.Id) return true;
			}
			return false;
		}

		private readonly Dictionary<TransactionTypeSettingKey, TransactionTypeSetting> transactionTypeSettings = new Dictionary<TransactionTypeSettingKey, TransactionTypeSetting>();

		internal Company Company { get { return Processors.Company; } }

		internal WholePaymentProcessor Processors { get; }

		private TransactionTypeSetting FindSetting(string driverId, ProcessorTransaction transactionType)
		{
			var processor = SearchById(driverId);
			if (!processor.ContainsTransactionType(transactionType.ToString())) throw new GameEngineException($"Processor '{driverId}' does not handle {nameof(transactionType)} '{transactionType}'.");
			var key = new TransactionTypeSettingKey(processor, transactionType);
			TransactionTypeSetting setting = null;
			transactionTypeSettings.TryGetValue(key, out setting);
			if (setting == null) throw new GameEngineException($"There is no {nameof(TransactionTypeSetting)} registered for '{transactionType}'");
			return setting;
		}

		private TransactionTypeSetting FindSetting(PaymentProcessor processor, ProcessorTransaction transactionType)
		{
			if (!processor.ContainsTransactionType(transactionType.ToString())) throw new GameEngineException($"Processor '{processor.Id}' does not handle {nameof(transactionType)} '{transactionType}'.");
			var key = new TransactionTypeSettingKey(processor, transactionType);
			TransactionTypeSetting setting = null;
			transactionTypeSettings.TryGetValue(key, out setting);
			if (setting == null) throw new GameEngineException($"There is no {nameof(TransactionTypeSetting)} registered for '{transactionType}'");
			return setting;
		}

		internal bool ExistsSetting(string driverId, ProcessorTransaction transactionType)
		{
			var processor = SearchById(driverId);
			if (!processor.ContainsTransactionType(transactionType.ToString())) return false;
			var key = new TransactionTypeSettingKey(processor, transactionType);
			TransactionTypeSetting setting = null;
			transactionTypeSettings.TryGetValue(key, out setting);
			return setting != null;
		}

		private void CreateSetting(PaymentProcessor processor)
		{
			TransactionTypeSetting setting = null;
			foreach (var transactionType in processor.Transactions)
			{
				var key = new TransactionTypeSettingKey(processor, transactionType);
				transactionTypeSettings.TryGetValue(key, out setting);
				if (setting == null)
					transactionTypeSettings.Add(key, new TransactionTypeSetting(key));
			}
		}

		internal IEnumerable<TransactionTypeSetting> ListSettings(string driverId)
		{
			if (string.IsNullOrWhiteSpace(driverId)) throw new ArgumentNullException(nameof(driverId));

			var driver = SearchById(driverId);
			var settings = new List<TransactionTypeSetting>();
			foreach (var setting in transactionTypeSettings.Values)
			{
				if (setting.Driver == driver)
					settings.Add(setting);
			}
			return settings;
		}

		internal bool IsNotificationEnabled(string driverId, ProcessorTransaction transactionType, TransactionStatus status)
		{
			if (string.IsNullOrWhiteSpace(driverId)) throw new ArgumentNullException(nameof(driverId));

			var setting = FindSetting(driverId, transactionType);
			return setting.IsNotificationEnabled(status);
		}

		internal bool IsTransactionTypeEnabled(string driverId, ProcessorTransaction transactionType)
		{
			if (string.IsNullOrWhiteSpace(driverId)) throw new ArgumentNullException(nameof(driverId));

			var setting = FindSetting(driverId, transactionType);
			return setting.IsTransactionEnabled;
		}

		private bool IsTransactionTypeEnabled(PaymentProcessor processor, ProcessorTransaction transactionType)
		{
			if (processor == null) throw new ArgumentNullException(nameof(processor));

			var setting = FindSetting(processor, transactionType);
			return setting.IsTransactionEnabled;
		}

		internal void EnableNotification(string driverId, ProcessorTransaction transactionType, TransactionStatus status)
		{
			if (string.IsNullOrWhiteSpace(driverId)) throw new ArgumentNullException(nameof(driverId));

			var setting = FindSetting(driverId, transactionType);
			if (setting.IsNotificationEnabled(status)) throw new GameEngineException($"Notification is already enabled for processor '{driverId}' and {nameof(transactionType)} '{transactionType}'.");
			setting.EnableNotification(status);
		}

		internal void EnableTransactionType(string driverId, ProcessorTransaction transactionType)
		{
			if (string.IsNullOrWhiteSpace(driverId)) throw new ArgumentNullException(nameof(driverId));

			var setting = FindSetting(driverId, transactionType);
			if (setting.IsTransactionEnabled) throw new GameEngineException($"Notification is already enabled for processor '{driverId}' and {nameof(transactionType)} '{transactionType}'.");
			setting.IsTransactionEnabled = true;
		}

		internal void DisableNotification(string driverId, ProcessorTransaction transactionType, TransactionStatus status)
		{
			if (string.IsNullOrWhiteSpace(driverId)) throw new ArgumentNullException(nameof(driverId));

			var setting = FindSetting(driverId, transactionType);
			if (!setting.IsNotificationEnabled(status)) throw new GameEngineException($"Notification is already disabled for processor '{driverId}' and {nameof(transactionType)} '{transactionType}'.");
			setting.DisableNotification(status);
		}

		internal void DisableTransactionType(string driverId, ProcessorTransaction transactionType)
		{
			if (string.IsNullOrWhiteSpace(driverId)) throw new ArgumentNullException(nameof(driverId));

			var setting = FindSetting(driverId, transactionType);
			if (!setting.IsTransactionEnabled) throw new GameEngineException($"Notification is already disabled for processor '{driverId}' and {nameof(transactionType)} '{transactionType}'.");
			setting.IsTransactionEnabled = false;
		}

		private bool IsDomainEnabled(string driverId)
		{
			foreach (var processor in processorsEnabled)
			{
				if (processor.Driver.Id == driverId) return true;
			}

			return false;
		}


		internal IEnumerable<PaymentProcessor> List()
		{
			List<PaymentProcessor> result = new List<PaymentProcessor>();
			foreach (var processor in processorsEnabled)
			{
				if (!result.Contains(processor))
				{
					result.Add(processor);
				}
			}

			return result;
		}

		internal IEnumerable<PaymentProcessor> ListAllBanksProcessor()
		{
			List<PaymentProcessor> result = new List<PaymentProcessor>();
			foreach (var processor in processorsEnabled)
			{
				if (processor.Group == Company.System.PaymentMethods.SearchByName<ProcessorPaymentMethod>(PaymentMethod.Bank.ToString())
					&& !result.Contains(processor))
				{
					result.Add(processor);
				}
			}

			return result;
		}

		internal IEnumerable<PaymentProcessor> ListAllCardsProcessor()
		{
			List<PaymentProcessor> result = new List<PaymentProcessor>();
			foreach (var processor in processorsEnabled)
			{
				if (processor.Group == Company.System.PaymentMethods.SearchByName<ProcessorPaymentMethod>(PaymentMethod.Creditcard.ToString())
					&& !result.Contains(processor))
				{
					result.Add(processor);
				}
			}

			return result;
		}

		internal IEnumerable<PaymentProcessor> ListAllBtcProcessor()
		{
			List<PaymentProcessor> result = new List<PaymentProcessor>();
			foreach (var processor in processorsEnabled)
			{
				if (processor.Group == Company.System.PaymentMethods.SearchByName<ProcessorPaymentMethod>(PaymentMethod.Secrets.ToString())
						&& !result.Contains(processor))
				{
					result.Add(processor);
				}
			}

			return result;
		}

		internal IEnumerable<PaymentProcessor> ListAllFinancialServicesProcessor()
		{
			List<PaymentProcessor> result = new List<PaymentProcessor>();
			foreach (var processor in processorsEnabled)
			{
				if (processor.Group == Company.System.PaymentMethods.SearchByName<ProcessorPaymentMethod>(PaymentMethod.FinancialServices.ToString())
						&& !result.Contains(processor))
				{
					result.Add(processor);
				}
			}

			return result;
		}

		internal IEnumerable<PaymentProcessor> ListAllThirdPartyProcessor()
		{
			List<PaymentProcessor> result = new List<PaymentProcessor>();
			foreach (var processor in processorsEnabled)
			{
				if (processor.Group == Company.System.PaymentMethods.SearchByName<ProcessorPaymentMethod>(PaymentMethod.ThirdParty.ToString())
					&& !result.Contains(processor))
				{
					result.Add(processor);
				}
			}

			return result;
		}

		internal IEnumerable<PaymentProcessor> ListAllCashProcessor()
		{
			List<PaymentProcessor> result = new List<PaymentProcessor>();
			foreach (var processor in processorsEnabled)
			{
				if (processor.Group == Company.System.PaymentMethods.SearchByName<ProcessorPaymentMethod>(PaymentMethod.Cash.ToString())
					&& !result.Contains(processor))
				{
					result.Add(processor);
				}
			}

			return result;
		}

		internal IEnumerable<Entity> EntitiesWithProviders(ProcessorTransaction transactionType)
		{
			var result = new HashSet<Entity>();
			foreach (var processor in processorsEnabled)
			{
				if (processor.ContainsTransactionType(transactionType) &&
					processor.Group.HasProviders())
				{
					result.Add(processor.Entity);
				}
			}

			return result;
		}

		internal IEnumerable<ProcessorPaymentMethod> PaymentMethodsWithProviders(Entity entity, ProcessorTransaction transactionType)
		{
			var result = new HashSet<ProcessorPaymentMethod>();
			foreach (var processor in processorsEnabled)
			{
				if (processor.ContainsTransactionType(transactionType) &&
					entity == processor.Entity &&
					processor.Group.HasProviders())
				{
					result.Add(processor.Group);
				}
			}

			return result;
		}

		internal int Count()
		{
			return processorsEnabled.Count();
		}
		internal void Add(PaymentProcessor processor)
		{
			if (processor == null) throw new ArgumentException(nameof(processor));

			Processors.Add(processor);
		}
		private void ValidateDriverItExists(PaymentProcessor processor)
		{
			if (processor == null) throw new ArgumentException(nameof(processor));

			PaymentProcessor result;
			Processors.TryGetValue(processor.Driver.Id, out result);
			if (result == null) throw new GameEngineException($"There is no {nameof(PaymentProcessor)} with the key {processor.Id}.");
		}
		internal PaymentProcessor SearchById(string driverId)
		{
			if (string.IsNullOrEmpty(driverId)) throw new ArgumentException(nameof(driverId));

			PaymentProcessor result;
			Processors.TryGetValue(driverId, out result);
			if (result == null)
				throw new GameEngineException($"There is no {nameof(PaymentProcessor)} with the key {driverId}.");

			return result;
		}
		internal bool ExistsId(string driverId)
		{
			if (string.IsNullOrEmpty(driverId)) throw new ArgumentException(nameof(driverId));

			PaymentProcessor result;
			Processors.TryGetValue(driverId, out result);
			return result != null;
		}

		internal MultipleProcessors SearchFor(Coin currencyCode, ProcessorTransaction transactionType)
		{
			MultipleProcessors drivers = new MultipleProcessors();
			foreach (var actionWithDomainAndDriver in processorsEnabled)
			{
				if (actionWithDomainAndDriver.Coin == currencyCode
					&& actionWithDomainAndDriver.ContainsTransactionType(transactionType))
				{
					((IndexedProcessors)drivers).Addon(actionWithDomainAndDriver);
				}
			}

			if (drivers.Count() == 0) throw new GameEngineException($"There is no {nameof(PaymentProcessor)} for {currencyCode.Iso4217Code} {transactionType.ToString()}.");

			return drivers;
		}

		internal MultipleProcessors SearchForX(int agentId, string domain)
		{
			if (string.IsNullOrWhiteSpace(domain)) throw new ArgumentNullException(nameof(domain));

			if (agentByDomainWholeDrivers.TryGetValue(agentId, out var domainProcessors))
			{
                if (domainProcessors.TryGetValue(domain, out var processors))
				{
                    return processors;
                }
            }

			throw new GameEngineException($"There is no {nameof(PaymentProcessor)} for Affiliate: {agentId} and Domain: {domain}.");
		}

        internal bool IsOnlyOneDriverEnabled(Coin currencyCode, ProcessorTransaction transactionType)
		{
			if (processorsEnabled.Count() == 0) return false;

			MultipleProcessors drivers = SearchFor(currencyCode, transactionType);
			if (!drivers.ThereIsOnlyOne()) return false;
			var driver = drivers.First();
			var result = IsTransactionTypeEnabled(driver, transactionType);
			return result;
		}

		internal bool IsOnlyOneDriverEnabled(string currencyCode, string transactionName)
		{
			var transactionType = Company.System.TransactionTypes.SearchByName<ProcessorTransaction>(transactionName);
			return IsOnlyOneDriverEnabled(Coinage.Coin(currencyCode), transactionType);
		}


		internal struct TransactionTypeSettingKey
		{
			internal PaymentProcessor Driver { get; }
			internal ProcessorTransaction TransactionType { get; }

			internal TransactionTypeSettingKey(PaymentProcessor driver, ProcessorTransaction transactionType)
			{
				Driver = driver;
				TransactionType = transactionType;
			}
		}

		internal class TransactionTypeSetting : Objeto
		{
			private readonly TransactionTypeSettingKey key;
			internal PaymentProcessor Driver => key.Driver;
			internal string CurrencyCode => key.Driver.CurrencyIso4217Code;
			internal ProcessorTransaction TransactionType => key.TransactionType;
			private NotificationSettingByStatus approvedNotificationSetting;
			private NotificationSettingByStatus deniedNotificationSetting;
			internal bool IsTransactionEnabled { get; set; } = true;

			internal TransactionTypeSetting(TransactionTypeSettingKey key)
			{
				this.key = key;
				approvedNotificationSetting = new NotificationSettingByStatus(TransactionStatus.APPROVED);
				deniedNotificationSetting = new NotificationSettingByStatus(TransactionStatus.DENIED);
			}

			internal List<NotificationSettingByStatus> ListNotificationsSetting()
			{
				var notifications = new List<NotificationSettingByStatus>();
				notifications.Add(approvedNotificationSetting);
				notifications.Add(deniedNotificationSetting);
				return notifications;
			}

			internal bool IsNotificationEnabled(TransactionStatus status)
			{
				if (status != TransactionStatus.APPROVED && status != TransactionStatus.DENIED) throw new GameEngineException($"No notification for {nameof(status)} {status}.");

				if (status == TransactionStatus.APPROVED) return approvedNotificationSetting.Enabled;
				return deniedNotificationSetting.Enabled;
			}

			internal void EnableNotification(TransactionStatus status)
			{
				if (status == TransactionStatus.APPROVED) approvedNotificationSetting.Enable();
				else if (status == TransactionStatus.DENIED) deniedNotificationSetting.Enable();
				else throw new GameEngineException($"No notification for {nameof(status)} {status}.");
			}

			internal void DisableNotification(TransactionStatus status)
			{
				if (status == TransactionStatus.APPROVED) approvedNotificationSetting.Disable();
				else if (status == TransactionStatus.DENIED) deniedNotificationSetting.Disable();
				else throw new GameEngineException($"No notification for {nameof(status)} {status}.");
			}

			internal class NotificationSettingByStatus : Objeto
			{
				internal TransactionStatus Status { get; }
				internal bool Enabled { get; private set; } = true;

				internal NotificationSettingByStatus(TransactionStatus status)
				{
					Status = status;
				}

				internal void Enable()
				{
					if (Enabled) throw new GameEngineException($"Notification is already enabled for {nameof(Status)} {Status}.");
					Enabled = true;
				}

				internal void Disable()
				{
					if (!Enabled) throw new GameEngineException($"Notification is already disabled for {nameof(Status)} {Status}.");
					Enabled = false;
				}
			}
		}
	}
}
