﻿using Connectors.town.connectors.commons;
using Connectors.town.connectors.drivers.hades;
using GamesEngine.Business;
using GamesEngine.Custodian.Operations;
using GamesEngine.Domains;
using GamesEngine.Exchange;
using GamesEngine.Marketing;
using GamesEngine.PurchaseOrders;
using GamesEngine.RealTime;
using GamesEngine.RealTime.Events;
using GamesEngine.Settings;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using town.connectors.commons;
using town.connectors.drivers;
using town.connectors.drivers.artemis;
using town.connectors.drivers.hades;
using static Connectors.town.connectors.drivers.artemis.Fragment_an_Authorization;
using static GamesEngine.Business.WholePaymentMethods;
using static GamesEngine.Business.WholePaymentProcessor;
using static GamesEngine.Business.WholePaymentProcessor.PaymentProcessor;
using static town.connectors.CustomSettings;
using static town.connectors.drivers.Result;
using DepositTransaction = Connectors.town.connectors.driver.transactions.DepositTransaction;
using DespositResponse = town.connectors.commons.DespositResponse;
using WithdrawalTransaction = Connectors.town.connectors.driver.transactions.WithdrawalTransaction;


namespace GamesEngine.Finance
{
	public class PaymentChannels
	{
		public enum Agents
		{
			INSIDER = 0,
			TEST_BOOK = 1,
			TEST_BOOK_DGS = 2,
			ARTEMIS = 3,
			ZEUS = 4
		}

		private PaymentChannels() { }


        public static async Task<int> LockBalanceAsync(string accountNumber, decimal purchaseTotal, string currency, string concept, string referenceNumber, string accounNumber,
            string employeeName, int storeId, DateTime useless, int agentId, string domainUrl, int domainId, PaymentMethod paymentMethodType, int entityId)
        {
            MultipleProcessors drivers = WholePaymentProcessor.Instance().PaymentProcessorsAndActionsByDomains.SearchForX(agentId, domainUrl);
            PaymentProcessor paymentProcessor = drivers.SearchByX(TransactionType.Authorization, currency, paymentMethodType, entityId);

			DateTime now = DateTime.Now;
            try
			{
				int authorizationNumber = ASITenantDriver.FAKE_TICKET_NUMBER;
                using (RecordSet recordSet = paymentProcessor.GetRecordSet())
				{
					recordSet.SetParameter("storeId", storeId);
					recordSet.SetParameter("purchaseTotal", purchaseTotal);
					recordSet.SetParameter("concept", concept);
					recordSet.SetParameter("reference", referenceNumber);
					recordSet.SetParameter("accountNumber", accounNumber);
					recordSet.SetParameter("processorId", paymentProcessor.Id);
					recordSet.SetParameter("who", employeeName);
					//recordSet.SetParameter("fragmentInformation", body.FragmentInformation);

                    recordSet.SetParameter("atAddress", accountNumber);
					recordSet.SetParameter("authorizationNumber", useless);
					recordSet.SetParameter("useless", useless);

					ExecuteResponse<int> responseAuthorizationNumber = await paymentProcessor.ExecuteAsync<int>(now, recordSet);
					if (responseAuthorizationNumber != null && responseAuthorizationNumber.Status == Status.Ok)
					{
                        authorizationNumber = responseAuthorizationNumber.Data;

                        NotifyOperation(
							true,
							now,
							accountNumber,
                            authorizationNumber,
                            concept,
							Currency.Factory(currency, purchaseTotal),
							accountNumber,
							TransactionType.Deposit,
							domainId,
							domainUrl,
							new TransactionFees(),
							new Execution(TransactionStatus.APPROVED, ""),
							paymentProcessor.Group,
							paymentProcessor.Name,
							storeId,
							employeeName
						);

					}
					return authorizationNumber;
				}
			}
			catch (Exception e)
			{
				ErrorsSender.Send(e);
				return ASITenantDriver.FAKE_TICKET_NUMBER;
			}
			finally
			{
				
			}
        }

		public static readonly int FAKE_DOCUMENT_NUMBER = -1;
		public static readonly FragmentResponse DEFAULT_FRAGMENT_RESPONSE = new FragmentResponse(
			FAKE_DOCUMENT_NUMBER,
			TransactionStatus.DENIED,
			AuthorizationResponseCode.AuthorizationFail,
			"Sorry, your purchase cannot be completed at this moment. Please try again. If the problem persists, please contact customer service."
		);

        internal static async Task<FragmentResponse> LockWithExternalResponseAsync(
			HttpContext context,
			Agents agent,
			string atAddress,
			string account,
			string decription,
			DateTime date,
			string addicionalInfo,
			Coin currencyCode,
			decimal total,
			int storeId,
			Domain domain,
			DateTime useless,
			List<ToWinByDrawAndNumber> tickets
		)
		{
			int agentId = (int)agent;
			MultipleProcessors drivers = WholePaymentProcessor.Instance().PaymentProcessorsAndActionsByDomains.SearchForX(agentId, domain.Url);
			var listPaymentProcessor = drivers.SearchByX(TransactionType.Authorization, currencyCode.Iso4217Code);

			var sortCascadeProcessors = new CascadePaymentProcessor(listPaymentProcessor);
			PaymentProcessor paymentProcessor;
			while ((paymentProcessor = sortCascadeProcessors.Next()) != null)
			{
				using (RecordSet recordSet = paymentProcessor.GetRecordSet())
				{
					recordSet.SetParameter("accountNumber", atAddress);
					recordSet.SetParameter("amount", total);
					recordSet.SetParameter("description", decription);
					recordSet.SetParameter("toWinsByDrawAndNumber", tickets);

					recordSet.SetParameter("currencyCode", currencyCode.Iso4217Code);
					recordSet.SetParameter("storeId", storeId);
					recordSet.SetParameter("fragmentInformation", null);
					recordSet.SetParameter("referenceNumber", null);
					recordSet.SetParameter("useless", useless);
					recordSet.SetParameter("context", context);

                    ExecuteResponse<InsertWagersResponse> result = await paymentProcessor.ExecuteAsync<InsertWagersResponse>(date, recordSet);

					if (result.Data?.Code != null)
					{
                        switch (result.Data.Code)
                        {
                            case AuthorizationResponseCode.OK:
                                return new FragmentResponse(result.Data.idTransaction, TransactionStatus.APPROVED, result.Data, paymentProcessor.ProcessorKey, paymentProcessor.Entity.Id, (int)paymentProcessor.PaymentMethodType);
                            case AuthorizationResponseCode.InsufficientFunds:
                                paymentProcessor.Driver.NotifyWarn(nameof(LockWithExternalResponseAsync), $"Url:{result.Data?.Url} \nResponse:  {result.Data?.Response}", result.Data.Code.ToString());
                                return new FragmentResponse(FAKE_DOCUMENT_NUMBER, TransactionStatus.DENIED, AuthorizationResponseCode.InsufficientFunds, "Sorry, your balance is not enough to purchase these tickets.");
                            case AuthorizationResponseCode.UnexpectedFormat:
                                paymentProcessor.Driver.NotifyWarn(nameof(LockWithExternalResponseAsync), $"Url:{result.Data?.Url} \nResponse:  {result.Data?.Response}", result.Data.Code.ToString());
                                return new FragmentResponse(FAKE_DOCUMENT_NUMBER, TransactionStatus.DENIED, AuthorizationResponseCode.UnexpectedFormat, "Sorry, your purchase cannot be completed at this moment. Please try again. If the problem persists, please contact customer service.");
                        }
                        //if AuthorizationFail continues until gets one
                    }
				}
			}

			return DEFAULT_FRAGMENT_RESPONSE;
		}

		internal static async Task<MultiRefundResponse> RefundAsync(HttpContext context, IEnumerable<TicketForRefunding> ticketsForRefunding, int storeId, string employeeName)
		{
			if (context == null) throw new ArgumentNullException(nameof(context));
			if (ticketsForRefunding == null || !ticketsForRefunding.Any()) throw new ArgumentNullException(nameof(ticketsForRefunding));
			if (storeId <= 0) throw new GameEngineException($"{nameof(storeId)} {storeId} is not valid");
			if (string.IsNullOrWhiteSpace(employeeName)) throw new ArgumentNullException(nameof(employeeName));

			MultiRefundResponse multiprocessorResponse = new MultiRefundResponse();
			ExecuteResponse<RefundResponse> processorResponse = null;
			foreach (var ticket in ticketsForRefunding)
			{
				var authorization = new AuthorizationNumber(ticket.AuthorizationNumber);
				MultipleProcessors multipleDrivers = WholePaymentProcessor.Instance().PaymentProcessorsAndActionsByDomains.SearchForX(ticket.AgentId, ticket.DomainUrl);
				var entityId = 4; // fiero
				PaymentProcessor refundProcessor = multipleDrivers.SearchByX(TransactionType.Refund, ticket.CurrencyCode, PaymentMethod.ThirdParty, entityId);
				List<FragmentPaymentBody> payFragments = new List<FragmentPaymentBody>();
				FragmentPaymentBody fragmentPaymentBody = new FragmentPaymentBody()
				{
					AtAddress = ticket.AccountNumber,
					AuthorizationNumber = authorization.Number,
					WagerNumber = ticket.WagerNumber,
					Outcome = "D",
					FragmentPaymentDate = DateTime.Now,
					AdjustedWinAmount = ticket.AdjustedWinAmount,
					AdjustedLossAmount = ticket.AdjustedLossAmount,
					agentId = ticket.AgentId,
					Currency = ticket.CurrencyCode,
					IsValidTicketNumber = true
				};
				payFragments.Add(fragmentPaymentBody);

                string appToken = string.Empty;
                if (SecurityConfiguration.WasConfigure() && SecurityConfiguration.ItsSecuritySchemeConfigured())
                {
                    string header = context.Request.Headers["Authorization"];
                    string[] tokens = header.Split(' ');
                    appToken = tokens[1];
                }
                using (RecordSet recordSet = refundProcessor.GetRecordSet())
				{
					recordSet.SetParameter("storeId", storeId);
					recordSet.SetParameter("domainUrl", ticket.DomainUrl);
					recordSet.SetParameter("who", employeeName);
					recordSet.SetParameter("concept", $"Lotto Refund for TN#{authorization.Number} - date {DateTime.Now}");
					recordSet.SetParameter("payFragments", payFragments);
					recordSet.SetParameter("account", refundProcessor.ProcessorKey);
					recordSet.SetParameter("processorId", refundProcessor.Id);
					processorResponse = await refundProcessor.ExecuteAsync<RefundResponse>(DateTime.Now, recordSet);
					if (processorResponse.Status == Status.Ok) multiprocessorResponse.Append(processorResponse.Data, fragmentPaymentBody);
                }
			}

			return multiprocessorResponse;
		}

        internal static async Task<RefundResponse> RefundSubticketsAsync(HttpContext context, IEnumerable<TicketForRefunding> ticketsForRefunding, int storeId, string employeeName)
        {
            if (context == null) throw new ArgumentNullException(nameof(context));
            if (ticketsForRefunding == null || !ticketsForRefunding.Any()) throw new ArgumentNullException(nameof(ticketsForRefunding));
            if (storeId <= 0) throw new GameEngineException($"{nameof(storeId)} {storeId} is not valid");
            if (string.IsNullOrWhiteSpace(employeeName)) throw new ArgumentNullException(nameof(employeeName));

            RefundResponse result = null;
            foreach (var ticket in ticketsForRefunding)
            {
                var authorization = new AuthorizationNumber(ticket.AuthorizationNumber);
                MultipleProcessors multipleDrivers = WholePaymentProcessor.Instance().PaymentProcessorsAndActionsByDomains.SearchForX(ticket.AgentId, ticket.DomainUrl);
                var entityId = 4; // fiero
                PaymentProcessor refundProcessor = multipleDrivers.SearchByX(TransactionType.Refund, ticket.CurrencyCode, PaymentMethod.ThirdParty, entityId);
                List<FragmentPaymentBody> payFragments = new List<FragmentPaymentBody>();
                FragmentPaymentBody fragmentPaymentBody = new FragmentPaymentBody()
                {
                    AtAddress = ticket.AccountNumber,
                    AuthorizationNumber = authorization.Number,
                    WagerNumber = ticket.WagerNumber,
                    Outcome = "D",
                    FragmentPaymentDate = DateTime.Now,
                    AdjustedWinAmount = ticket.AdjustedWinAmount,
                    AdjustedLossAmount = ticket.AdjustedLossAmount,
                    agentId = ticket.AgentId,
                    Currency = ticket.CurrencyCode,
                    IsValidTicketNumber = true
                };
                payFragments.Add(fragmentPaymentBody);

				string appToken = string.Empty;
				if (SecurityConfiguration.WasConfigure() && SecurityConfiguration.ItsSecuritySchemeConfigured())
				{
					string header = context.Request.Headers["Authorization"];
					string[] tokens = header.Split(' ');
                    appToken = tokens[1];
                }
                using (RecordSet recordSet = refundProcessor.GetRecordSet())
                {
                    recordSet.SetParameter("storeId", storeId);
                    recordSet.SetParameter("domainUrl", ticket.DomainUrl);
                    recordSet.SetParameter("who", employeeName);
                    recordSet.SetParameter("concept", $"Lotto Refund for TN#{authorization.Number} - date {DateTime.Now}");
                    recordSet.SetParameter("payFragments", payFragments);
                    recordSet.SetParameter("account", refundProcessor.ProcessorKey);
                    recordSet.SetParameter("processorId", refundProcessor.Id);
                    var processorResponse = await refundProcessor.ExecuteAsync<RefundResponse>(DateTime.Now, recordSet);
					if (processorResponse.Status == Status.Ok) result = processorResponse.Data;
                }
            }

            return result;
        }

        private static void NotifyOperation(bool itsThePresent, DateTime creationDate, string accountNumber, int AuthorizationId, string description, Currency amount, string identificationDocumentNumber,
			TransactionType transactionType, int domainId, string domainUrl, TransactionFees fees, Execution execution, ProcessorPaymentMethod paymentMethod, string driverId, int storeId, string employeeName)
		{
			if (string.IsNullOrEmpty(description)) throw new ArgumentNullException(nameof(description));
			if (string.IsNullOrEmpty(domainUrl)) throw new ArgumentNullException(nameof(domainUrl));
			if (fees == null) throw new ArgumentNullException(nameof(fees));

			if (itsThePresent)
			{
				OperationMessage msn = new OperationMessage(
					accountNumber,
					transactionType,
					creationDate,
					AuthorizationId,
					description,
					amount,
					paymentMethod,
					driverId,
					domainId,
					domainUrl,
					fees,
					execution,
					identificationDocumentNumber,
					storeId,
					employeeName
					);

				Integration.Kafka.Send(itsThePresent, Integration.Kafka.TopicForGuardianOperations, msn);
			}
		}

		public struct ProcessorAccount
		{
			public int accountId { get; set; }
		}

        public static void DepositX(bool itsThePresent, DateTime now, RestAPISpawnerActor actor, DepositMessage message, out int authorization)
        {
            var description = Validator.StringEscape(message.Description);

            MultipleProcessors drivers = WholePaymentProcessor.Instance().PaymentProcessorsAndActionsByDomains.SearchForX((int)message.Agent, message.Domain);
            PaymentProcessor paymentProcessor = drivers.SearchByX(TransactionType.Deposit, message.Currency, PaymentMethod.ThirdParty, entityId: 4);//Drivers: entityId 4 means FIERO segun Driver de Deposit

            if (message.ProcessorId == 0)
            {
                var resultQry = actor.PerformCmd(RestAPISpawnerActor.GENERAL, $@"
					{{
						account = guardian.Accounts().SearchByProcessor('{paymentProcessor.ProcessorKey}');
						print account.Id accountId;
					}}
				");
                if (!(resultQry is OkObjectResult)) throw new GameEngineException($"No account for processor {paymentProcessor.ProcessorKey}");
                OkObjectResult o = (OkObjectResult)resultQry;
                string json = o.Value.ToString();
                var processorAccount = JsonConvert.DeserializeObject<ProcessorAccount>(json);

                message.ProcessorId = processorAccount.accountId;
            }

            DepositTransaction result = null;
            using (RecordSet recordSet = paymentProcessor.GetRecordSet())
            {
                recordSet.SetParameter("amount", message.Amount);
                recordSet.SetParameter("accountNumber", message.AccountNumber);
                recordSet.SetParameter("currency", message.Currency);
                recordSet.SetParameter("atAddress", message.AtAddress);
                recordSet.SetParameter("domain", message.Domain);
                recordSet.SetParameter("sourceNumber", message.SourceNumber);
                recordSet.SetParameter("sourceName", message.SourceName);
                recordSet.SetParameter("who", message.Who);
                recordSet.SetParameter("agent", message.Agent);
                recordSet.SetParameter("storeId", message.StoreId);
                recordSet.SetParameter("processorId", message.ProcessorId);
                recordSet.SetParameter("description", description);
                recordSet.SetParameter("reference", message.Reference);

                //WebHook eventType: Deposit.USD ---> Deposit.USD.1
                result = paymentProcessor.Execute<Connectors.town.connectors.driver.transactions.DepositTransaction>(now, recordSet);
                authorization = result.AuthorizationId;
            }

            if (result != null && result.Status == TransactionStatus.APPROVED)
            {
                NotifyOperation(
                    itsThePresent,
                    now,
                    message.AccountNumber,
                    authorization,
                    message.Description,
                    Currency.Factory(paymentProcessor.CurrencyIso4217Code, message.Amount),
                    message.AtAddress,
                    TransactionType.Deposit,
                    0,//Drivers: Aqui se debe implementar el domainId
                    message.Domain,
                    new TransactionFees(),
                    new Execution(result.Status, ""),
                    paymentProcessor.Group,
                    paymentProcessor.Driver.Id,
                    message.StoreId,
                    message.Who
                    );
            }
            else
            {
                throw new GameEngineException($"Deposit was not saved because command fails on cashier.");
            }

            //Notificar por SignalR que se tiene que actualizar el saldo
            if (message.NotifyPlayer)
            {
                var resultQry = actor.PerformCmd(RestAPISpawnerActor.GENERAL, $@"
					{{
						customer = company.CustomerByAccountNumber('{message.AtAddress}');
						print customer.Player.Id PlayerId;
					}}
				");
                if (!(resultQry is OkObjectResult)) throw new GameEngineException($"Error getting playerId for {message.AtAddress}");

                OkObjectResult o = (OkObjectResult)resultQry;
                string json = o.Value.ToString();
                var playerDto = JsonConvert.DeserializeObject<PlayerDTO>(json);

                if (!string.IsNullOrEmpty(playerDto.PlayerId))
                {
                    _ = Task.Run(async () => {
                        BalancesResponse balancesResponse = await CustomerBalancesAsync(message.Domain, message.AtAddress, (int)message.Agent, new List<string>() { message.Currency });

                        if (balancesResponse == null) throw new GameEngineException($"No balance response for {message.AtAddress} and {message.Currency}");
                        if (balancesResponse.Balances.Count() > 1) throw new GameEngineException($"More than one balance response for {message.AtAddress} and {message.Currency}");

                        BalanceResponse singleBalance = balancesResponse.Balances.FirstOrDefault();

                        // CREAR EL EVENTO PARA ACTUALIZAR EL SALDO
                        UpdatedBalanceEvent updateBalanceEvent = new UpdatedBalanceEvent(now, playerDto.PlayerId, message.Currency, singleBalance.Balance, singleBalance.BalanceFormatted);
                        PlatformMonitor.GetInstance().WhenNewEvent(updateBalanceEvent);
                    });
                }
            }
        }

		public static void WithDrawX(bool itsThePresent, DateTime now, RestAPISpawnerActor actor, WithdrawMessage message, out int authorization)
		{
            MultipleProcessors drivers = WholePaymentProcessor.Instance().PaymentProcessorsAndActionsByDomains.SearchForX((int)message.Agent, message.Domain);
            PaymentProcessor paymentProcessor = drivers.SearchByX(TransactionType.Withdrawal, message.Currency, PaymentMethod.ThirdParty, entityId: 4);

			authorization = 0;
            if (message.ProcessorId == 0)
            {
                var resultQry = actor.PerformCmd(RestAPISpawnerActor.GENERAL, $@"
					{{
						account = guardian.Accounts().SearchByProcessor('{paymentProcessor.ProcessorKey}');
						print account.Id accountId;
					}}
					");
                if (!(resultQry is OkObjectResult)) throw new GameEngineException($"No account for processor {paymentProcessor.ProcessorKey}");
                OkObjectResult o = (OkObjectResult)resultQry;
                string json = o.Value.ToString();
                var processorAccount = JsonConvert.DeserializeObject<ProcessorAccount>(json);

                message.ProcessorId = processorAccount.accountId;
            }

            WithdrawalTransaction result = null;
            using (RecordSet recordSet = paymentProcessor.GetRecordSet())
            {
                recordSet.SetParameter("amount", message.Amount);
                recordSet.SetParameter("accountNumber", message.AccountNumber);
                recordSet.SetParameter("currency", message.Currency);
                recordSet.SetParameter("atAddress", message.AtAddress);
                recordSet.SetParameter("domain", message.Domain);
                recordSet.SetParameter("sourceNumber", message.SourceNumber);
                recordSet.SetParameter("sourceName", message.SourceName);
                recordSet.SetParameter("who", message.Who);
                recordSet.SetParameter("agent", message.Agent);
                recordSet.SetParameter("storeId", message.StoreId);
                recordSet.SetParameter("processorId", message.ProcessorId);
                recordSet.SetParameter("description", message.Description);
                recordSet.SetParameter("reference", message.Reference);

				result = paymentProcessor.Execute<WithdrawalTransaction>(now, recordSet);
                authorization = result.AuthorizationId;
            }

            if (result != null && result.Status == TransactionStatus.APPROVED)
            {
                NotifyOperation(
                    itsThePresent,
                    now,
                    message.AccountNumber,
                    authorization,
                    message.Description,
                    Currency.Factory(paymentProcessor.CurrencyIso4217Code, message.Amount),
                    message.AtAddress,
                    TransactionType.Deposit,
                    0,//Drivers: Aqui se debe implementar el domainId
                    message.Domain,
                    new TransactionFees(),
                    new Execution(result.Status, ""),
                    paymentProcessor.Group,
                    paymentProcessor.Driver.Id,
                    message.StoreId,
                    message.Who
                );
            }

			//Notificar por SignalR que se tiene que actualizar el saldo
			if (message.NotifyPlayer)
			{
                var resultQry = actor.PerformCmd(RestAPISpawnerActor.GENERAL, $@"
					{{
						customer = company.CustomerByAccountNumber('{message.AtAddress}');
						print customer.Player.Id PlayerId;
					}}
				");
				if (!(resultQry is OkObjectResult)) throw new GameEngineException($"Error getting playerId for {message.AtAddress}");

                OkObjectResult o = (OkObjectResult)resultQry;
				string json = o.Value.ToString();
				var playerDto = JsonConvert.DeserializeObject<PlayerDTO>(json);

				if (!string.IsNullOrEmpty(playerDto.PlayerId))
				{
                    _ = Task.Run(async () => {
                        BalancesResponse balancesResponse = await CustomerBalancesAsync(message.Domain, message.AtAddress, (int)message.Agent, new List<string>() { message.Currency });

                        if (balancesResponse == null) throw new GameEngineException($"No balance response for {message.AtAddress} and {message.Currency}");
                        if (balancesResponse.Balances.Count() > 1) throw new GameEngineException($"More than one balance response for {message.AtAddress} and {message.Currency}");

                        BalanceResponse singleBalance = balancesResponse.Balances.FirstOrDefault();

                        // CREAR EL EVENTO PARA ACTUALIZAR EL SALDO
                        UpdatedBalanceEvent updateBalanceEvent = new UpdatedBalanceEvent(now, playerDto.PlayerId, message.Currency, singleBalance.Balance, singleBalance.BalanceFormatted);
                        PlatformMonitor.GetInstance().WhenNewEvent(updateBalanceEvent);
                    });
                }
            }

        }

		internal class PlayerDTO
		{
            public string PlayerId { get; set; }
        }

        internal static void UpdateWagers(List<PayFragmentsMessage> wagers)
		{
			var artemisWagers = wagers.Where(wager => wager.AgentId == (int)Agents.ARTEMIS);
			if (artemisWagers.Any())
			{
                PayFragmentsMessage firstFragment = artemisWagers.First();
                MultipleProcessors drivers = WholePaymentProcessor.Instance().PaymentProcessorsAndActionsByDomains.SearchForX(firstFragment.AgentId, firstFragment.DomainUrl);
                IEnumerable<PaymentProcessor> paymentProcessorList = drivers.SearchByX(TransactionType.PayPrize, Currencies.CODES.USD.ToString());

                PaymentProcessor paymentProcessor = paymentProcessorList.FirstOrDefault();
                if (paymentProcessor == null) throw new Exception($"No payment processor for transactionType: {TransactionType.CreateFragment}, currencyCode: {Currencies.CODES.USD}");

                using (RecordSet recordSet = paymentProcessor.GetRecordSet())
				{
					recordSet.SetParameter("gradeFreeFormWagers", artemisWagers.ToList());

                    paymentProcessor.Execute<GradeFreeFormWagersResponse>(DateTime.Now, recordSet);
				}
			}
			else
            {
				throw new GameEngineException("No wagers to update in agent");
			}
		}

        public static async Task<BalancesResponse> CustomerBalancesAsync(string domain, string atAddress, int agent, IEnumerable<string> codes)
        {
			if (string.IsNullOrEmpty(domain)) throw new ArgumentNullException(nameof(domain));
			if (string.IsNullOrEmpty(atAddress)) throw new ArgumentNullException(nameof(atAddress));
            if (codes == null && codes.Count() == 0) throw new ArgumentNullException(nameof(codes));

            BalancesResponse response = new BalancesResponse();
            MultipleProcessors multipleProcessors = WholePaymentProcessor.Instance().PaymentProcessorsAndActionsByDomains.SearchForX(agent, domain);
            foreach (var iso4217Code in codes)
            {
                var paymentProcessors = multipleProcessors.SearchByX(TransactionType.RetrieveBalance, iso4217Code);
                if (paymentProcessors == null || paymentProcessors.Count() == 0) throw new GameEngineException($"No processor for {iso4217Code}");

				var paymentProcessor = paymentProcessors.FirstOrDefault();

                ExecuteResponse<decimal> resultBalance;
                using (RecordSet recordSet = paymentProcessor.GetRecordSet())
                {
                    recordSet.SetParameter("atAddress", atAddress);
                    resultBalance = await paymentProcessor.ExecuteAsync<decimal>(DateTime.Now, recordSet);

                    if (resultBalance.Status == Status.Ok)
                    {
                        var balanceResponse = new BalanceResponse()
                        {
                            Balance = resultBalance.Data,
                            CurrencyCodeAsText = iso4217Code
                        };
                        response.Add(balanceResponse);
                    }
				}   				
            }

            return response;
        }

        public static async Task<BalancesResponse> CustomerBalancesAsync(string domain, int agent, IEnumerable<(string, string)> driversAccountAndCurrency)
        {
            if (string.IsNullOrEmpty(domain)) throw new ArgumentNullException(nameof(domain));
            if (driversAccountAndCurrency == null && driversAccountAndCurrency.Count() == 0) throw new ArgumentNullException(nameof(driversAccountAndCurrency));

            BalancesResponse response = new BalancesResponse();
            MultipleProcessors multipleProcessors = WholePaymentProcessor.Instance().PaymentProcessorsAndActionsByDomains.SearchForX(agent, domain);
			foreach (var accountAndCurrency in driversAccountAndCurrency)
			{
                var atAddress = accountAndCurrency.Item1;
                var iso4217Code = accountAndCurrency.Item2;

                var paymentProcessors = multipleProcessors.SearchByX(TransactionType.RetrieveBalance, iso4217Code);
                if (paymentProcessors == null || paymentProcessors.Count() == 0) throw new GameEngineException($"No processor for {iso4217Code}");

                var paymentProcessor = paymentProcessors.FirstOrDefault();

                ExecuteResponse<decimal> resultBalance;
                using (RecordSet recordSet = paymentProcessor.GetRecordSet())
                {
                    recordSet.SetParameter("atAddress", atAddress);
                    resultBalance = await paymentProcessor.ExecuteAsync<decimal>(DateTime.Now, recordSet);

                    if (resultBalance.Status == Status.Ok)
                    {
                        var balanceResponse = new BalanceResponse()
                        {
							AtAddress = atAddress,
                            Balance = resultBalance.Data,
                            CurrencyCodeAsText = iso4217Code
                        };
                        response.Add(balanceResponse);
                    }
                }
            }
            return response;
        }

        public static async Task<PlayerBalanceBody> CustomerBalancesAsync(string domain, string atAddress, int agent, string iso4217Code)
        {
            if (string.IsNullOrEmpty(domain)) throw new ArgumentNullException(nameof(domain));
            if (string.IsNullOrEmpty(atAddress)) throw new ArgumentNullException(nameof(atAddress));
            if (string.IsNullOrEmpty(iso4217Code)) throw new ArgumentNullException(nameof(iso4217Code));

            BalancesResponse response = new BalancesResponse();
            MultipleProcessors multipleProcessors = WholePaymentProcessor.Instance().PaymentProcessorsAndActionsByDomains.SearchForX(agent, domain);

            var paymentProcessors = multipleProcessors.SearchByX(TransactionType.RetrieveBalance, iso4217Code);
            if (paymentProcessors == null || paymentProcessors.Count() == 0) throw new GameEngineException($"No processor for {iso4217Code}");

            var paymentProcessor = paymentProcessors.FirstOrDefault();            
            using (RecordSet recordSet = paymentProcessor.GetRecordSet())
            {
                recordSet.SetParameter("atAddress", atAddress);
				ExecuteResponse<PlayerBalanceBody> resultBalance = await paymentProcessor.ExecuteAsync<PlayerBalanceBody>(DateTime.Now, recordSet);
				if (resultBalance.Status == Status.Ok)
				{
					return resultBalance.Data;
				}

				return PlayerBalanceBody.DEFAULT_EMPTY;
            }

        }

        public static async Task<bool> ValidateAsync(int agentId, string accountNumber, string token)
		{
			string Iso4217Code = Currencies.CODES.USD.ToString();
			PaymentProcessor paymentProcessor = WholePaymentProcessor.Instance().SearchValidateProcesorBy(Iso4217Code);
			
            ExecuteResponse<bool> validateResut;

			using (RecordSet recordSet = paymentProcessor.GetRecordSet())
			{
				recordSet.SetParameter("customerId", accountNumber);
				recordSet.SetParameter("token", token);

                validateResut = await paymentProcessor.ExecuteAsync<bool>(DateTime.Now, recordSet);
			}

			if(validateResut.Status == Status.Ok)
			{
				return validateResut.Data;
            }

			return false;

			//BalancesResponse response = new BalancesResponse();
			//decimal balance = await (allAvailableDrivers[agent].Driver as ASIdriver).AvailableBalanceAsync(atAddress);
			//var balanceResponse = new BalanceResponse()
			//{
			//Balance = balance,
			//CurrencyCodeAsText = Iso4217Code
			//};
			//response.Add(balanceResponse);
			//return response;

			//Agents agent = (Agents)agentId;
			//if (agent == Agents.INSIDER)//insider
			//{
			//return true;
			//}
			//else if (agent == Agents.TEST_BOOK)
			//{
			//return await (allAvailableDrivers[agent].Driver as ASIdriver).PostValidateCustomerAsync(accountNumber, token);
			//}
			//else
			//{
			//return await (allAvailableDrivers[agent].Driver as DGSdriver).ValidatePlayerAsync(token);
			//}
		}


		[Obsolete("USE THIS ONLY FOR TEST PORPUSE")]
		internal static DespositResponse Deposit(bool itIsThePresent, Coin currencyCode, Domain domain, ProcessorTransaction transaction, DespositBody despositBody)
		{
            PaymentProcessor paymentProcessor = WholePaymentProcessor.Instance().SearchPaymentProcessor(TransactionType.Deposit, currencyCode.Iso4217Code, despositBody.EntityId, despositBody.PaymentMethod);            
			return Deposit(itIsThePresent, paymentProcessor, despositBody);
		}

		[Obsolete("USE THIS ONLY FOR TEST PORPUSE")]
        internal static DespositResponse DepositForTest(bool itIsThePresent, int agentId, string domain, string currencyCode, DespositBody despositBody)
        {
            MultipleProcessors drivers = WholePaymentProcessor.Instance().PaymentProcessorsAndActionsByDomains.SearchForX(agentId, domain);
            PaymentProcessor paymentProcessor = drivers.SearchByX(TransactionType.Deposit, currencyCode, despositBody.PaymentMethod, despositBody.EntityId);

            return Deposit(itIsThePresent, paymentProcessor, despositBody);
        }

        [Obsolete("USE THIS ONLY FOR TEST PORPUSE")]
        internal static DespositResponse Deposit(bool itIsThePresent, Coin coin, string domain, ProcessorTransaction transaction, DespositBody despositBody)
        {
            if (coin == null) throw new ArgumentNullException(nameof(coin));
            if (transaction == null) throw new ArgumentNullException(nameof(transaction));
            if (despositBody == null) throw new ArgumentNullException(nameof(despositBody));
            if (string.IsNullOrEmpty(domain)) throw new ArgumentNullException(nameof(domain));

            PaymentProcessor paymentProcessor = WholePaymentProcessor.Instance().SearchPaymentProcessor(TransactionType.Deposit, coin.Iso4217Code, despositBody.EntityId, despositBody.PaymentMethod);

            return Deposit(itIsThePresent, paymentProcessor, despositBody);
        }

        internal static DespositResponse CreditNoteX(bool itIsThePresent, int agentId, string domain, string coinCode, DespositBody despositBody)
        {
            if (despositBody == null) throw new ArgumentNullException(nameof(despositBody));
            if (string.IsNullOrWhiteSpace(domain)) throw new ArgumentNullException(nameof(domain));
            if (string.IsNullOrWhiteSpace(coinCode)) throw new ArgumentNullException(nameof(coinCode));

            MultipleProcessors drivers = WholePaymentProcessor.Instance().PaymentProcessorsAndActionsByDomains.SearchForX(agentId, domain);
            PaymentProcessor paymentProcessor = drivers.SearchByX(TransactionType.CreditNote, coinCode, despositBody.PaymentMethod, despositBody.EntityId);

            return Deposit(itIsThePresent, paymentProcessor, despositBody);
        }

        internal static DespositResponse DepositX(bool itIsThePresent, int agentId, string domain, string coinCode, DespositBody despositBody)
        {
            if (despositBody == null) throw new ArgumentNullException(nameof(despositBody));
            if (string.IsNullOrWhiteSpace(domain)) throw new ArgumentNullException(nameof(domain));
            if (string.IsNullOrWhiteSpace(coinCode)) throw new ArgumentNullException(nameof(coinCode));

            MultipleProcessors drivers = WholePaymentProcessor.Instance().PaymentProcessorsAndActionsByDomains.SearchForX(agentId, domain);
            PaymentProcessor paymentProcessor = drivers.SearchByX(TransactionType.Deposit, coinCode, despositBody.PaymentMethod, despositBody.EntityId);

            return Deposit(itIsThePresent, paymentProcessor, despositBody);
        }

        internal static DespositResponse TransferX(bool itIsThePresent, int agentId, string domain, string coinCode, DespositBody despositBody)
        {
            if (despositBody == null) throw new ArgumentNullException(nameof(despositBody));
            if (string.IsNullOrWhiteSpace(domain)) throw new ArgumentNullException(nameof(domain));
            if (string.IsNullOrWhiteSpace(coinCode)) throw new ArgumentNullException(nameof(coinCode));

            MultipleProcessors drivers = WholePaymentProcessor.Instance().PaymentProcessorsAndActionsByDomains.SearchForX(agentId, domain);
            PaymentProcessor paymentProcessor = drivers.SearchByX(TransactionType.Transfer, coinCode, despositBody.PaymentMethod, despositBody.EntityId);

            return Deposit(itIsThePresent, paymentProcessor, despositBody);
        }

        [Obsolete("NOT IN USE, MAY BE DELETE")]
        internal static DespositResponse Deposit(bool itIsThePresent, string processorId, DespositBody despositBody)
		{
			PaymentProcessor paymentProcessor = WholePaymentProcessor.Instance().PaymentProcessorsAndActionsByDomains.SearchById(processorId);

			return Deposit(itIsThePresent, paymentProcessor, despositBody);

		}

		private static DespositResponse Deposit(bool itIsThePresent, PaymentProcessor paymentProcessor, DespositBody despositBody)
		{
			DespositResponse response;
			using (RecordSet recordSet = paymentProcessor.GetRecordSet())
			{
                //recordSet.SetParameter("customerId", despositBody.AccountNumber);
                //recordSet.SetParameter("amount", despositBody.Amount);
                //recordSet.SetParameter("description", $"{nameof(TransactionType.Deposit)} {despositBody.Description}");
                //recordSet.SetParameter("additionalInfo", string.Empty);
                //recordSet.SetParameter("referenceID", despositBody.AccountNumber);

                //recordSet.SetParameter("sendersName", despositBody.SendersName);
                //recordSet.SetParameter("country", despositBody.Country);
                //recordSet.SetParameter("state", despositBody.State);
                //recordSet.SetParameter("city", despositBody.City);
                //recordSet.SetParameter("controlNum", despositBody.ControlNum);
                //recordSet.SetParameter("amount", despositBody.Amount);
                //recordSet.SetParameter("readyForProcessing", "true");
                //recordSet.SetParameter("Input$Provider", despositBody.ProviderId);
                //var execResult = paymentProcessor.Execute<DepositTransaction>(DateTime.Now, recordSet);
                recordSet.SetParameter("amount", despositBody.Amount);
                recordSet.SetParameter("atAddress", despositBody.Identifier);
                //recordSet.SetParameter("accountNumber", despositBody.Identifier);
                recordSet.SetParameter("currency", paymentProcessor.CurrencyIso4217Code);
                //recordSet.SetParameter("atAddress", despositBody.AccountNumber);
                recordSet.SetParameter("accountNumber", despositBody.AccountNumber);
                recordSet.SetParameter("domain", despositBody.Domain.Url);
                recordSet.SetParameter("sourceNumber", TransactionMessage.NO_SOURCE);
                recordSet.SetParameter("sourceName", TransactionMessage.NO_SOURCE_NAME);
                recordSet.SetParameter("who", despositBody.EmployeeName);
                recordSet.SetParameter("agent", despositBody.Domain.AgentId);
                recordSet.SetParameter("storeId", despositBody.StoreId);
                recordSet.SetParameter("processorId", paymentProcessor.Id);
                recordSet.SetParameter("description", $"{nameof(TransactionType.Deposit)} {despositBody.Description}");
                recordSet.SetParameter("reference", despositBody.Reference);
                var execResult = paymentProcessor.Execute<DepositTransaction>(DateTime.Now, recordSet);
                response = new DespositResponse(execResult.AuthorizationId, execResult.Status, paymentProcessor.ProcessorKey);
			}

			if (response.Status == TransactionStatus.APPROVED)
            {
				NotifyOperation(
					itIsThePresent,
					DateTime.Now,
					despositBody.AccountNumber,
					response.AuthorizationId,
					despositBody.Description,
					Currency.Factory(paymentProcessor.CurrencyIso4217Code, despositBody.Amount),
					despositBody.Identifier,
					TransactionType.Deposit,
					despositBody.Domain.Id,
					despositBody.Domain.Url,
					new TransactionFees(),
					new Execution(response.Status, ""),
					paymentProcessor.Group,
					paymentProcessor.Driver.Id,
					despositBody.StoreId,
					despositBody.EmployeeName
					);
			}
			return response;
		}

        internal static WithdrawReponse WithDrawX(bool itIsThePresent, int agentId, string domain, string coinCode, WithdrawBody withdrawBody)
        {
			if (string.IsNullOrWhiteSpace(domain)) throw new ArgumentNullException(nameof(domain));
			if (string.IsNullOrWhiteSpace(coinCode)) throw new ArgumentNullException(nameof(coinCode));
            if (withdrawBody == null) throw new ArgumentNullException(nameof(withdrawBody));

            MultipleProcessors drivers = WholePaymentProcessor.Instance().PaymentProcessorsAndActionsByDomains.SearchForX(agentId, domain);
            PaymentProcessor paymentProcessor = drivers.SearchByX(TransactionType.Withdrawal, coinCode, withdrawBody.PaymentMethod, withdrawBody.EntityId);

            return WithDraw(itIsThePresent, paymentProcessor, withdrawBody);
        }

        internal static WithdrawReponse DebitNoteX(bool itIsThePresent, int agentId, string domain, string coinCode, WithdrawBody withdrawBody)
        {
            if (string.IsNullOrWhiteSpace(domain)) throw new ArgumentNullException(nameof(domain));
            if (string.IsNullOrWhiteSpace(coinCode)) throw new ArgumentNullException(nameof(coinCode));
            if (withdrawBody == null) throw new ArgumentNullException(nameof(withdrawBody));

            MultipleProcessors drivers = WholePaymentProcessor.Instance().PaymentProcessorsAndActionsByDomains.SearchForX(agentId, domain);
            PaymentProcessor paymentProcessor = drivers.SearchByX(TransactionType.DebitNote, coinCode, withdrawBody.PaymentMethod, withdrawBody.EntityId);

            return WithDraw(itIsThePresent, paymentProcessor, withdrawBody);
        }

        internal static WithdrawReponse WithDraw(bool itIsThePresent, string processorId, WithdrawBody withdrawBody)
		{
			PaymentProcessor paymentProcessor = WholePaymentProcessor.Instance().PaymentProcessorsAndActionsByDomains.SearchById(processorId);//Drivers debe buscar en el Index principal y no en un afiliate o domain ya que se tiene el processorId

            return WithDraw(itIsThePresent, paymentProcessor, withdrawBody);
		}

		static WithdrawReponse WithDraw(bool itIsThePresent, PaymentProcessor paymentProcessor, WithdrawBody withdrawBody)
        {
			WithdrawReponse response;

			using (RecordSet recordSet = paymentProcessor.GetRecordSet())
			{
                recordSet.SetParameter("amount", withdrawBody.Amount);
                recordSet.SetParameter("accountNumber", withdrawBody.AccountNumber);
                recordSet.SetParameter("currency", paymentProcessor.CurrencyIso4217Code);
                recordSet.SetParameter("atAddress", withdrawBody.Identifier);
                recordSet.SetParameter("domain", withdrawBody.Domain.Url);
                recordSet.SetParameter("sourceNumber", TransactionMessage.NO_SOURCE);
                recordSet.SetParameter("sourceName", TransactionMessage.NO_SOURCE_NAME);
                recordSet.SetParameter("who", withdrawBody.EmployeeName);
                recordSet.SetParameter("agent", withdrawBody.Domain.AgentId);
                recordSet.SetParameter("storeId", withdrawBody.StoreId);
                recordSet.SetParameter("processorId", paymentProcessor.Id);
                recordSet.SetParameter("description", $"{nameof(TransactionType.Deposit)} {withdrawBody.Description}");
                recordSet.SetParameter("reference", withdrawBody.Reference);

                var execResult = paymentProcessor.Execute<Connectors.town.connectors.driver.transactions.WithdrawalTransaction>(DateTime.Now, recordSet);
				response = new WithdrawReponse(execResult.AuthorizationId, execResult.Status, paymentProcessor.ProcessorKey);
			}

			return response;
		}
    }

}
