﻿using Connectors.town.connectors.commons;
using Connectors.town.connectors.drivers.artemis;
using GamesEngine.Business;
using GamesEngine.Settings;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using RestSharp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using town.connectors;
using town.connectors.commons;
using town.connectors.drivers;
using town.connectors.drivers.artemis;
using town.connectors.drivers.fiero;

namespace GamesEngine.Exchange.town.connectors.drivers.artemis.tenant
{
    public abstract class Fragment_an_AuthorizationInsider : Fragment_an_Authorization, IDriverUserProperties
    {
        public string CashierUrl { get; private set; }
        public const int FAKE_TICKET_NUMBER = -1;

        public string DriverUserName { get; set; }
        public string DriverPassword { get; set; }

        public Fragment_an_AuthorizationInsider(string currencyCode) : base(currencyCode)
        {
        }

        public override void Prepare(DateTime now)
        {
            base.Prepare(now);
            CashierUrl = CustomSettings.Get(now, "CashierDriver.url").AsString;

            DriverUserName = CustomSettings.Get(now, "CashierDriver.userName").AsString;
            DriverPassword = CustomSettings.Get(now, "CashierDriver.password").AsString;
        }

        public override async Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet)
        {
            // ARTEMIS REQUEST
            var artemisResponse = await base.ExecuteAsync<InsertWagersResponse>(now, recordSet);
            if (artemisResponse.Code != AuthorizationResponseCode.OK)
            {
                InsertWagersResponse failResponse = new InsertWagersResponse()
                {
                    idTransaction = FAKE_DOCUMENT_NUMBER,
                    Code = AuthorizationResponseCode.AuthorizationFail,
                    Response = artemisResponse.Response,
                    Url = URL
                };
                return (T)Convert.ChangeType(failResponse, typeof(T));
            }

            bool validTickets = artemisResponse.tickets != null && artemisResponse.tickets.Any(t => t.ticketId != null);
            if (!validTickets)
            {
                InsertWagersResponse failResponse = new InsertWagersResponse()
                {
                    idTransaction = FAKE_DOCUMENT_NUMBER,
                    Code = AuthorizationResponseCode.AuthorizationFail,
                    Response = "No ticket id were provide from artemis." + artemisResponse.Response,
                    Url = URL
                };
                return (T)Convert.ChangeType(FAIL_DEFAULT_RESPONSE, typeof(T));
            }

            //CASHIER REQUEST
            bool userPassHasChange = DriverUserName != CustomSettings.Get(now, "CashierDriver.userName").AsString || DriverPassword != CustomSettings.Get(now, "CashierDriver.password").AsString;
            if (userPassHasChange)
            {
                DriverUserName = CustomSettings.Get(now, "CashierDriver.userName").AsString;
                DriverPassword = CustomSettings.Get(now, "CashierDriver.password").AsString;
            }

            bool itsSecuritySchemeConfigured = SecurityConfiguration.ItsSecuritySchemeConfigured();

            bool hasExpired = Authorization.CashierToken == null || SecurityConfiguration.ItsSecurityTokenExpired(Authorization.CashierToken.access_token, now);

            bool needToChangeToken = (Authorization.CashierToken == null || userPassHasChange || hasExpired) && itsSecuritySchemeConfigured;
            if (needToChangeToken) Authorization.CashierToken = await TokenDriver.GetTokenAsync(DriverUserName, DriverPassword);
            var result = await AuthorizationAsync(now, recordSet, artemisResponse.tickets);
            if (result.AuthorizationNumber <= 0)
            {
                GradeFreeFormWagerCollectionChunked(artemisResponse.tickets);
                InsertWagersResponse failResponse = new InsertWagersResponse()
                {
                    idTransaction = FAKE_DOCUMENT_NUMBER,
                    Code = AuthorizationResponseCode.AuthorizationFail,
                    Response = result.Response,
                    Url = URL
                };
                return (T)Convert.ChangeType(failResponse, typeof(T));
            }

            return (T)Convert.ChangeType(artemisResponse, typeof(T));
        }

        private async Task<GamesEngine.Finance.LockBalanceResponse> AuthorizationAsync(DateTime now, CustomSettings.RecordSet recordSet, List<Finance.ToWinByDrawAndNumber> toWinByDrawAndNumbers)
        {
            //var AccountNumber = recordSet.Mappings["accountNumber"];
            //var AccountNumber = recordSet.Mappings["currencyCode"];
            var AtAddress = recordSet.Mappings["accountNumber"];
            var PurchaseTotalValue = recordSet.Mappings["amount"];
            var PurchaseTotalCurrencyCode = recordSet.Mappings["currencyCode"];
            var StoreId = recordSet.Mappings["storeId"];
            var Concept = recordSet.Mappings["description"];
            var fragmentInformation = recordSet.Mappings["fragmentInformation"];
            var ReferenceNumber = recordSet.Mappings["referenceNumber"];
            var Useless = recordSet.Mappings["useless"];
            //var Context = recordSet.Mappings["context"];
            //var ToWinsByDrawAndNumber = recordSet.Mappings["toWinsByDrawAndNumber"];

            string accountNumber = string.Empty;
            string atAddress = AtAddress.AsString;
            decimal purchaseTotal = PurchaseTotalValue.AsDecimal;

            var lockBalanceData = new Finance.ExternalMultiLockBalanceData();
            lockBalanceData.AtAddress = atAddress;
            lockBalanceData.UseLocalAuthorization = false;
            lockBalanceData.AccountNumber = accountNumber;
            lockBalanceData.PurchaseTotal = purchaseTotal;
            lockBalanceData.CurrencyCode = PurchaseTotalCurrencyCode.AsString;
            lockBalanceData.StoreId = StoreId.AsInt;
            lockBalanceData.Concept = Concept.AsString;
            if (recordSet.ContainsKeyName("fragmentInformation"))
                lockBalanceData.FragmentInformation = fragmentInformation.As<FragmentInformation>();
            if (recordSet.ContainsKeyName("referenceNumber"))
                lockBalanceData.Reference = ReferenceNumber.AsString;
            lockBalanceData.Useless = Useless.AsDateTime;
            lockBalanceData.ToWinsByDrawAndNumber = toWinByDrawAndNumbers;
            //HttpContext context = Context.As<HttpContext>();

            GamesEngine.Finance.LockBalanceResponse lockBalanceResponse = new GamesEngine.Finance.LockBalanceResponse();
            try
            {
                var url = $"{CashierUrl}api/customers/{atAddress}/balance/externalMultiLock";
                HttpRestClientConfiguration restClientToCashier = HttpRestClientConfiguration.GetInstance();

                IActionResult resultFromCashier;
                if (SecurityConfiguration.WasConfigure() && SecurityConfiguration.ItsSecuritySchemeConfigured())
                {
                    resultFromCashier = await restClientToCashier.PostAsync(Authorization.CashierToken.Token, url, lockBalanceData);
                }
                else
                {
                    resultFromCashier = await restClientToCashier.PostAsync(url, lockBalanceData);
                }

                if (!(resultFromCashier is OkObjectResult))
                {
                    string body = string.Empty;
                    if (resultFromCashier is ContentResult)
                    {
                        body = $@"error:{((ObjectResult)resultFromCashier).ToString()} \n url:{url} \n accountNumber:{accountNumber} \n purchaseTotal:{purchaseTotal}";
                    }
                    else
                    {
                        body = $@"url:{url} \n accountNumber:{accountNumber} \n purchaseTotal:{purchaseTotal}";
                    }
                    ErrorsSender.Send(body, $@"Lock amount to {accountNumber} fails.");
                    lockBalanceResponse.Url = url;
                    lockBalanceResponse.Response = body;
                    lockBalanceResponse.Code = AuthorizationResponseCode.AuthorizationFail;
                    lockBalanceResponse.AuthorizationNumber = FAKE_TICKET_NUMBER;
                    return lockBalanceResponse;
                }

                string resp = ((OkObjectResult)resultFromCashier).Value.ToString();
                lockBalanceResponse = JsonConvert.DeserializeObject<GamesEngine.Finance.LockBalanceResponse>(resp);
                lockBalanceResponse.Url = url;
                lockBalanceResponse.Response = "OK";
            }
            catch (Exception e)
            {
                ErrorsSender.Send(e);
                lockBalanceResponse.Code = AuthorizationResponseCode.AuthorizationFail;
                lockBalanceResponse.AuthorizationNumber = FAKE_TICKET_NUMBER;
            }

            return lockBalanceResponse;
        }


        private RestClient _postUpdateWagersClient;

        void GradeFreeFormWagerCollectionChunked(List<Finance.ToWinByDrawAndNumber> wagers)
        {
            string url = "/GradingBet";
            if (_postUpdateWagersClient == null)
            {
                if (string.IsNullOrWhiteSpace(SystemId)) throw new ArgumentNullException("Custom variable 'TokenSystemId' is required");
                if (string.IsNullOrWhiteSpace(Password)) throw new ArgumentNullException("Custom variable 'TokenSystemPassword' is required");
                if (string.IsNullOrWhiteSpace(ServicesUrl)) throw new ArgumentNullException("Custom variable 'CompanyBaseUrlServices' is required");

                if (string.IsNullOrWhiteSpace(DGSProcessorDriver.AppToken))
                {
                    _ = Task.Run(async () =>
                    {
                        await DGSTenantDriver.RequestAppTokenAsync(ServicesUrl, SystemId, Password);
                    });
                }

                _postUpdateWagersClient = new RestClient(ServicesUrl);
            }

            var gradeFreeFormWagers = new WagersUpdateBody();
            //gradeFreeFormWagers.AddWagers(wagers);
            foreach (var wager in wagers)
            {
                gradeFreeFormWagers.tickets.Add(new Finance.ToWinByDrawAndNumber()
                {
                    status = $"{WagerStatus.X}",
                    ticketId = wager.ticketId,
                    toWin = 0m
                });
            }

            var jsonString = Commons.ToJson(gradeFreeFormWagers);
            string responseString = string.Empty;
            int retryNumber = 0;
            const int MAX_RETRIES_WAGER_COLLECTION = 5;
            while (true)
            {
                try
                {
                    var request = new RestRequest(url, Method.Post);
                    request.AddHeader("Authorization", $"Bearer {DGSProcessorDriver.AppToken}");
                    request.AddHeader("Content-Type", "application/json");
                    request.AddParameter("application/json", jsonString, ParameterType.RequestBody);

                    var response = _postUpdateWagersClient.Execute(request);
                    responseString = response.Content;
                    if ((int)response.StatusCode != 200)
                    {
                        if (responseString.Contains("Lotto wager not found.")) break;
                        retryNumber++;
                        Thread.Sleep(2000 + (1000 * (retryNumber - 1)));
                        if (retryNumber == MAX_RETRIES_WAGER_COLLECTION) break;
                    }
                    else
                    {
                        //if (!string.IsNullOrWhiteSpace(responseString))
                        break;
                    }
                }
                catch (Exception e)
                {
                    retryNumber++;
                    var extraErrorMessage = string.Empty;
                    if (e.InnerException is HttpRequestException) extraErrorMessage = "There was a problem getting the response from server.";
                    Thread.Sleep(2000 + (1000 * (retryNumber - 1)));
                    if (retryNumber == MAX_RETRIES_WAGER_COLLECTION)
                    {
                        InternalOnError(nameof(GradeFreeFormWagerCollectionChunked), retryNumber, e, $"Url:{url}", $"Request: {jsonString}", $"Response: {responseString}", extraErrorMessage);
                    }
                }
            }

        }
    }
}
