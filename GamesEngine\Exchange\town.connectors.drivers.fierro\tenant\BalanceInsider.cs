﻿using GamesEngine.Settings;
using Microsoft.AspNetCore.Http;
using RestSharp;
using System;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using town.connectors.drivers.artemis;

namespace town.connectors.drivers.fiero
{
    internal abstract class BalanceInsider : Balance, IDriverUserProperties
    {
        public static TokenDriver CashierToken { get; set; }
        public string CashierUrl { get; private set; }

        public string DriverUserName { get; set; }
        public string DriverPassword { get; set; }

        private RestClient _postClient;

        protected BalanceInsider(string currencyCode) : base(currencyCode)
        {
        }

        public override async Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet)
        {
            bool userPassHasChange = DriverUserName != CustomSettings.Get(now, "CashierDriver.userName").AsString || DriverPassword != CustomSettings.Get(now, "CashierDriver.password").AsString;
            if (userPassHasChange)
            {
                DriverUserName = CustomSettings.Get(now, "CashierDriver.userName").AsString;
                DriverPassword = CustomSettings.Get(now, "CashierDriver.password").AsString;
            }

            bool hasExpired = CashierToken == null || SecurityConfiguration.ItsSecurityTokenExpired(CashierToken.access_token, now);

            bool itsSecurityConfigured = SecurityConfiguration.WasConfigure() && SecurityConfiguration.ItsSecuritySchemeConfigured();

            bool needToChangeToken = (CashierToken == null || userPassHasChange || hasExpired) && itsSecurityConfigured;
            if (needToChangeToken) CashierToken = await TokenDriver.GetTokenAsync(DriverUserName, DriverPassword);

            var atAddress = recordSet.Mappings["atAddress"].As<string>();

            if (CurrencyIsoCodes.Length > 1) throw new NotImplementedException("Drives must have only one currency code");
            string currencyCode = CurrencyIsoCodes.FirstOrDefault();

            var result = await AvailableBalanceAsync(atAddress, currencyCode);
            if (!result.Success)
            {
                throw new Exception($"Error in Cashier: {result.Message}");
            }
            return await base.ExecuteAsync<T>(now, recordSet);
        }

        public override void Prepare(DateTime now)
        {
            base.Prepare(now);

            CashierUrl = CustomSettings.Get(now, "CashierDriver.url").AsString;

            DriverUserName = CustomSettings.Get(now, "CashierDriver.userName").AsString;
            DriverPassword = CustomSettings.Get(now, "CashierDriver.password").AsString;
        }

        protected override async Task<BalanceResponse> AvailableBalanceAsync(string atAddress, string currencyCode)
        {
            if (_postClient == null) _postClient = new RestClient(CashierUrl);

            string errorMessage = string.Empty;
            try
            {
                var url = $"api/customers/{atAddress}/balance/{currencyCode}/available";

                var request = new RestRequest(url, Method.Post);

                request.AddHeader("Content-Type", "application/json");
                if (SecurityConfiguration.WasConfigure() && SecurityConfiguration.ItsSecuritySchemeConfigured()) request.AddHeader("Authorization", $"Bearer {CashierToken.access_token}");

                var resultFromCashier = await _postClient.ExecuteAsync(request);
                if (resultFromCashier.StatusCode != HttpStatusCode.OK)
                {
                    return new BalanceResponse
                    {
                        Available = 0,
                        Success = true,
                        Message = errorMessage
                    };
                }
                else
                {
                    return new BalanceResponse
                    {
                        Available = decimal.Parse(resultFromCashier.Content),
                        Success = true,
                        Message = "Success"
                    };
                }
            }
            catch (Exception e)
            {
                ErrorsSender.Send(e);
            }  

            return new BalanceResponse
            {
                Available = 0,
                Success = false,
                Message = errorMessage
            };


        }
    }
}
