﻿using Connectors.town.connectors.commons;
using Connectors.town.connectors.driver.transactions;
using GamesEngine.Finance;
using GamesEngine.Settings;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using town.connectors.commons;
using static town.connectors.drivers.Result;

namespace town.connectors.drivers.fiero
{   
    public abstract class AuthorizationMultiple : FieroTenantDriver, IDriverUserProperties
    {
		public const int FAKE_TICKET_NUMBER = -1;
		private const float VERSION = 1.0F;

        public string DriverUserName { get; set; }
        public string DriverPassword { get; set; }

        public string CashierUrl { get; private set; }

        public override string Description => $"Fiero {nameof(AuthorizationMultiple)} driver {VERSION}";

		public AuthorizationMultiple(string currencyCode)
			: base(Tenant_Actions.AuthorizationInternal, TransactionType.Authorization, currencyCode)
        {
        }

		public override async Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet)
		{

            bool userPassHasChange = DriverUserName != CustomSettings.Get(now, "CashierDriver.userName").AsString || DriverPassword != CustomSettings.Get(now, "CashierDriver.password").AsString;
            if (userPassHasChange)
            {
                DriverUserName = CustomSettings.Get(now, "CashierDriver.userName").AsString;
                DriverPassword = CustomSettings.Get(now, "CashierDriver.password").AsString;
            }

            bool itsSecuritySchemeConfigured = SecurityConfiguration.ItsSecuritySchemeConfigured();

            bool hasExpired = Authorization.CashierToken == null || SecurityConfiguration.ItsSecurityTokenExpired(Authorization.CashierToken.access_token, now);

            bool needToChangeToken = (Authorization.CashierToken == null || userPassHasChange || hasExpired) && itsSecuritySchemeConfigured;
            if (needToChangeToken) Authorization.CashierToken = await TokenDriver.GetTokenAsync(DriverUserName, DriverPassword);

            var result = await AuthorzationAsync(now, recordSet);
			AuthorizationTransaction auth;
			if (result.AuthorizationNumber > 0)
			{
				auth = new AuthorizationTransaction(result.AuthorizationNumber, TransactionStatus.APPROVED, Entity, PaymentMethod, CurrencyIsoCodes);
				return (T)Convert.ChangeType(auth, typeof(T));
			}
			auth = new AuthorizationTransaction(result.AuthorizationNumber, TransactionStatus.DENIED, Entity, PaymentMethod, CurrencyIsoCodes, result.Code, result.Url, result.Response);
			return (T)Convert.ChangeType(auth, typeof(T));
		}

		public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet)
		{
			throw new NotImplementedException();
        }

		public override void Prepare(DateTime now)
		{
            CustomSettings.AddVariableParameter("storeId");
			CustomSettings.AddVariableParameter("purchaseTotal");
			CustomSettings.AddVariableParameter("concept");
			CustomSettings.AddVariableParameter("reference");
            CustomSettings.AddVariableParameter("accountNumber");
			CustomSettings.AddVariableParameter("fragmentInformation");
			CustomSettings.AddVariableParameter("toWinsByDrawAndNumber");

			CustomSettings.AddVariableParameter("atAddress");
			CustomSettings.AddVariableParameter("useless");

            CashierUrl = CustomSettings.Get(now, "CashierDriver.url").AsString;
            DriverUserName = CustomSettings.Get(now, "CashierDriver.userName").AsString;
            DriverPassword = CustomSettings.Get(now, "CashierDriver.password").AsString;
        }
        private async Task<GamesEngine.Finance.LockBalanceResponse> AuthorzationAsync(DateTime now, CustomSettings.RecordSet recordSet)
        {
            var StoreId = recordSet.Mappings["storeId"];
            var PurchaseTotalValue = recordSet.Mappings["purchaseTotal"];
            var Concept = recordSet.Mappings["description"];
            var ReferenceNumber = recordSet.Mappings["reference"];
            string accountNumber = recordSet.Mappings["accountNumber"].AsString;
            var FragmentInformation = recordSet.Mappings["fragmentInformation"].As<FragmentInformation>();
            var ToWinsByDrawAndNumber = recordSet.Mappings["toWinsByDrawAndNumber"];

            //var PurchaseTotalCurrencyCode = recordSet.Mappings["currencyCode"];
            if (CurrencyIsoCodes.Length > 1) throw new NotImplementedException("Drives must have only one currency code");
            string currencyCode = CurrencyIsoCodes.FirstOrDefault();

            var Useless = recordSet.Mappings["useless"];

            string atAddress = recordSet.Mappings["accountNumber"].AsString;
            decimal purchaseTotal = PurchaseTotalValue.AsDecimal;

            var lockBalanceData = new ExternalMultiLockBalanceData();
            lockBalanceData.AtAddress = atAddress;
            lockBalanceData.AccountNumber = accountNumber;
            lockBalanceData.PurchaseTotal = purchaseTotal;
            lockBalanceData.CurrencyCode = currencyCode;
            lockBalanceData.StoreId = StoreId.AsInt;
            lockBalanceData.Concept = Concept.AsString;
            if (recordSet.ContainsKeyName("fragmentInformation"))
                lockBalanceData.FragmentInformation = FragmentInformation;
            if (recordSet.ContainsKeyName("referenceNumber"))
                lockBalanceData.Reference = ReferenceNumber.AsString;
            lockBalanceData.Useless = Useless.AsDateTime;
            lockBalanceData.ToWinsByDrawAndNumber = ToWinsByDrawAndNumber.As<List<ToWinByDrawAndNumber>>();

            GamesEngine.Finance.LockBalanceResponse lockBalanceResponse = new GamesEngine.Finance.LockBalanceResponse();
            try
            {
                var url = $"{CashierUrl}api/customers/{atAddress}/balance/externalMultiLock";
                HttpRestClientConfiguration restClientToCashier = HttpRestClientConfiguration.GetInstance();

                IActionResult resultFromCashier;
                if (SecurityConfiguration.WasConfigure() && SecurityConfiguration.ItsSecuritySchemeConfigured())
                {
                     resultFromCashier = await restClientToCashier.PostAsync(Authorization.CashierToken.Token, url, lockBalanceData);
                }
                else
                {
                    resultFromCashier = await restClientToCashier.PostAsync(url, lockBalanceData);
                }
                if (!(resultFromCashier is OkObjectResult))
                {
                    string body = string.Empty;
                    if (resultFromCashier is ContentResult)
                    {
                        body = $@"error:{((ObjectResult)resultFromCashier).ToString()} \n url:{url} \n accountNumber:{accountNumber} \n purchaseTotal:{purchaseTotal}";
                    }
                    else
                    {
                        body = $@"url:{url} \n accountNumber:{accountNumber} \n purchaseTotal:{purchaseTotal}";
                    }
                    ErrorsSender.Send(body, $@"Lock amount to {accountNumber} fails.");
                    lockBalanceResponse.Url = url;
                    lockBalanceResponse.Response = body;
                    lockBalanceResponse.Code = AuthorizationResponseCode.AuthorizationFail;
                    lockBalanceResponse.AuthorizationNumber = FAKE_TICKET_NUMBER;
                    return lockBalanceResponse;
                }

                string resp = ((OkObjectResult)resultFromCashier).Value.ToString();
                lockBalanceResponse = JsonConvert.DeserializeObject<GamesEngine.Finance.LockBalanceResponse>(resp);
                lockBalanceResponse.Url = url;
                lockBalanceResponse.Response = resp;
            }
            catch (Exception e)
            {
                ErrorsSender.Send(e);
                lockBalanceResponse.Code = AuthorizationResponseCode.AuthorizationFail;
                lockBalanceResponse.AuthorizationNumber = FAKE_TICKET_NUMBER;
            }

            return lockBalanceResponse;
        }

	}
}
