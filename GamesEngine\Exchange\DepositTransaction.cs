﻿using GamesEngine.Business;
using GamesEngine.Finance;
using GamesEngine.Messaging;
using GamesEngine.Settings;
using System;
using town.connectors.drivers;
using static GamesEngine.Business.WholePaymentProcessor;
using static town.connectors.drivers.Result;

namespace GamesEngine.Exchange
{
    internal sealed class DepositTransaction : Transaction
	{
		internal const int TEMPLATE_ID = 3;
		private readonly PaymentProcessor selectedDriver;
		private int authorizationNumber;
		internal int AuthorizationId 
		{
			set
			{
				if (authorizationNumber != 0) throw new GameEngineException($"The {nameof(AuthorizationId)} only can be load one time.");
				if (value <= 0) throw new GameEngineException($"The {nameof(AuthorizationId)} must be greater than 0.");

				authorizationNumber = value;
			}

			get
			{
				if (authorizationNumber == 0) throw new GameEngineException($"The {nameof(AuthorizationId)} mustbe loaded");
				return authorizationNumber;
			}
		}
		internal DepositTransaction(TransactionDefinition transactionDefinition, IConversionSpread conversionSpread, string employeeName, Currency amount, DateTime now, PaymentProcessor processor) 
			: base(transactionDefinition, conversionSpread, amount, now, TransactionType.Deposit)
		{
			selectedDriver = processor; 
			transactionDefinition.Batch.AddTransactionDenifition(this);
		}

		internal TransactionCompleted Approve(DateTime date, bool itsThePresent, int authorizationId, string employeeName)
		{
			AuthorizationId = authorizationId;
			return base.Approve(date, itsThePresent, employeeName);
		}

		protected override void AfterApprove(DateTime date, bool itsThePresent, Currency gross, Currency comission, Currency profit, Currency net, Currency amountToCustomer, string employeeName, TransactionCompleted transactionCompleted)
		{
			if (!itsThePresent) return;
			CustomerAccount toAccount = TransactionDefinition.Account;
			var isTheSameCurrencyCode = gross.CurrencyCode == toAccount.CurrencyCode;
			string description = isTheSameCurrencyCode ? 
				$"Deposit {amountToCustomer.CurrencyCode} {amountToCustomer.Value} to {toAccount.Identificator}": 
				$"Deposit {gross.CurrencyCode} {gross.Value} converted to {amountToCustomer.CurrencyCode} {amountToCustomer.Value} in {toAccount.Identificator}";

            //SendDepositMessage(
            //    itsThePresent,
            //    toAccount.CurrencyCode.ToString(),
            //    toAccount.CustomerAccountNumber,
            //    amountToCustomer.Value,
            //    description,
            //    this.Id,
            //    employeeName,
            //    toAccount.Identificator,
            //    ProcessorAccountId,
            //    PaymentChannels.Agents.INSIDER
            //    );

            if (Integration.UseKafka || Integration.UseKafkaForAuto)
			{
				var marketplace = TransactionDefinition.Marketplace;
				Integration.Kafka.Send(
					itsThePresent,
					$"{KafkaMessage.TRANSACTION_EXCHANGE_CONSUMER_PREFIX}_{Integration.Kafka.TopicForTransacctions}",
					new ApprovedTransactionMessage(TransactionType.Deposit, Id, date, profit.Value, net.Value, amountToCustomer.Value, gross.Coin, amountToCustomer.Coin, employeeName, AuthorizationId, ProcessorAccountId)
				);

				var processors = marketplace.PaymentProcessors();
				if (processors.IsNotificationEnabled(selectedDriver.Driver.Id, WholePaymentProcessor.Instance().SearchTransactionByName(TransactionType.Deposit.ToString()), TransactionStatus.APPROVED))
				{
					Subscriber fromSubscriber = marketplace;
					var messageToSend = $"The transaction #{Id} '{description}' has been approved. Go to History to see the details";
					if (amountToCustomer.Coin == Coinage.Coin(Currencies.CODES.BTC))
					{
						Subscriber toSubscriber = TransactionDefinition.FindOwner().Player;
						Integration.Kafka.Send(itsThePresent, Integration.Kafka.TopicForNotifications,
							new NotificationMessage(NotificationMessageType.ADD, fromSubscriber, toSubscriber, messageToSend));
					}
				}
			}
		}


		internal override void AfterDeny(DateTime date, bool itsThePresent, string employeeName, string reason)
		{
			TransactionDefinition.Deny(this);

			if (!itsThePresent) return;

			if (Integration.UseKafka || Integration.UseKafkaForAuto)
			{
				Integration.Kafka.Send(
					itsThePresent,
					$"{KafkaMessage.TRANSACTION_EXCHANGE_CONSUMER_PREFIX}_{Integration.Kafka.TopicForTransacctions}",
					new DeniedTransactionMessage(TransactionType.Deposit, Id, date, reason, employeeName)
				);
			}
		}

		internal override bool isSameCurrencyTransaction()
		{
			return TransactionDefinition.Account.CurrencyCode == Amount.CurrencyCode;
		}

		protected sealed override int TemplateNumber
		{
			get
			{
				return TEMPLATE_ID;
			}
		}

		internal string Voucher { get; set; }
		internal string VoucherUrl { get; set; }
		internal string Depositor { get; set; }
	}

}
