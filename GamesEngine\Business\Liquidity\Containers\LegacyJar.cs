﻿using GamesEngine.Business.Liquidity.Transactions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GamesEngine.Business.Liquidity.Containers
{
    public class LegacyJar : Container
    {
        private readonly Jar lastJar;

        public LegacyJar(Jar jar) : base(jar.Kind)
        {
            if (jar == null) throw new GameEngineException("The Jar is null.");

            lastJar = jar;
            Amount = jar.Amount;
        }

        public int Version => lastJar.Version;

        // public Jar Jar { get { return lastJar; } }

        public IEnumerable<Deposit> Deposits()
        {
            return lastJar.Deposits;
        }

    }

    public class AuditRecord
    {
        public DateTime Timestamp { get; }
        public string Operation { get; }
        public string User { get; }

        public AuditRecord(string operation, string user)
        {
            Timestamp = DateTime.UtcNow;
            Operation = operation ?? throw new ArgumentNullException(nameof(operation));
            User = user ?? throw new ArgumentNullException(nameof(user));
        }
    }


}
