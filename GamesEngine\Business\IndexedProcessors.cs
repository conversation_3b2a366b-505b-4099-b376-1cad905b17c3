﻿using GamesEngine.Finance;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.CompilerServices;
using town.connectors.drivers;
using static GamesEngine.Business.WholePaymentProcessor;
[assembly: InternalsVisibleTo("GamesEngineTests")]
namespace GamesEngine.Business
{
    internal abstract class IndexedProcessors : Objeto
    {
        protected HashSet<PaymentProcessor> all_processors;
        protected Dictionary<TransactionType, Dictionary<string, Dictionary<int, Dictionary<PaymentMethod, List<PaymentProcessor>>>>> whole_processors;

        internal IndexedProcessors()
        {
            all_processors = new HashSet<PaymentProcessor>();
            whole_processors = new Dictionary<TransactionType, Dictionary<string, Dictionary<int, Dictionary<PaymentMethod, List<PaymentProcessor>>>>>();
        }

        internal HashSet<PaymentProcessor> AllProcessors => all_processors;

        protected int nextConsecutive = 0;
        protected int NextConsecutive()
        {
            return nextConsecutive + 1;
        }

        private List<PaymentProcessor> GetOrCreatePaymentProcessor(TransactionType transactionType, Coin coin, Entity entity, PaymentMethod method)
        {
            return GetOrCreateTransactionType(transactionType, coin, entity, method);
        }

        private List<PaymentProcessor> GetOrCreateTransactionType(TransactionType transactionType, Coin coin, Entity entity, PaymentMethod method)
        {
            return GetOrCreateTransactionType(transactionType, coin.Iso4217Code, entity.Id, method);
        }

        private List<PaymentProcessor> GetOrCreateTransactionType(TransactionType transactionType, string coinCode, int entityId, PaymentMethod method)
        {
            if (!whole_processors.TryGetValue(transactionType, out var remainingProcessors))
            {
                remainingProcessors = new Dictionary<string, Dictionary<int, Dictionary<PaymentMethod, List<PaymentProcessor>>>>();
                whole_processors.Add(transactionType, remainingProcessors);
            }

            return GetOrCreateCoin(remainingProcessors, coinCode, entityId, method);
        }

        internal List<PaymentProcessor> GetPaymentProcessors(TransactionType transactionType, string coinCode, int entityId, PaymentMethod method)
        {
            if (whole_processors.TryGetValue(transactionType, out var remainingProcessors))
            {
                if (remainingProcessors.TryGetValue(coinCode, out var remainingSparseMatrix))
                {
                    if (remainingSparseMatrix.TryGetValue(entityId, out var remainingSparseMatrix4))
                    {
                        if (remainingSparseMatrix4.TryGetValue(method, out var processors))
                        {
                            return processors;
                        }
                    }
                }
            }

            var entityText = WholePaymentProcessor.Instance().Company.System.Entities.Find(entityId).Name;
            throw new GameEngineException($"There is no {nameof(PaymentProcessor)} configured for Entity Id: {entityId}({entityText}), {nameof(PaymentMethod)}: {method}, {nameof(TransactionType)}: {transactionType} and Coin code: {coinCode}.");
        }

        private List<PaymentProcessor> GetOrCreateCoin(Dictionary<string, Dictionary<int, Dictionary<PaymentMethod, List<PaymentProcessor>>>> remainingProcessors,
            string coinCode, int entityId, PaymentMethod method)
        {
            if (!remainingProcessors.TryGetValue(coinCode, out var remainingSparseMatrix))
            {
                remainingSparseMatrix = new Dictionary<int, Dictionary<PaymentMethod, List<PaymentProcessor>>>();
                remainingProcessors.Add(coinCode, remainingSparseMatrix);
            }

            return GetOrCreateEntity(remainingSparseMatrix, entityId, method);
        }

        private List<PaymentProcessor> GetOrCreateEntity(Dictionary<int, Dictionary<PaymentMethod, List<PaymentProcessor>>> remainingProcessors,
            int entityId, PaymentMethod method)
        {
            Dictionary<PaymentMethod, List<PaymentProcessor>> remainingSparseMatrix;
            if (!remainingProcessors.TryGetValue(entityId, out remainingSparseMatrix))
            {
                remainingSparseMatrix = new Dictionary<PaymentMethod, List<PaymentProcessor>>();
                remainingProcessors.Add(entityId, remainingSparseMatrix);
            }

            return GetOrCreatePaymentMethod(remainingSparseMatrix, method);
        }

        private List<PaymentProcessor> GetOrCreatePaymentMethod(Dictionary<PaymentMethod, List<PaymentProcessor>> remainingProcessors, PaymentMethod method)
        {
            List<PaymentProcessor> processors;
            if (!remainingProcessors.TryGetValue(method, out processors))
            {
                processors = new List<PaymentProcessor>();
                remainingProcessors.Add(method, processors);
            }

            return processors;
        }

        internal PaymentProcessor GetOrCreatePaymentProcessor(TransactionType transactionType, Coin coin, Entity entity, PaymentMethod method, Driver implementation)
        {
            if (coin == null) throw new ArgumentNullException(nameof(coin));
            if (entity == null) throw new ArgumentNullException(nameof(entity));
            if (implementation == null) throw new ArgumentNullException(nameof(implementation));

            var processors = GetOrCreatePaymentProcessor(transactionType, coin, entity, method);

            var processor = processors.FirstOrDefault(x => x.Driver.Id == implementation.Id);
            if (processor != null) return processor;

            int nextConsecutive = NextConsecutive();
            var processorPaymentMethod = WholePaymentProcessor.Instance().SearchPaymentMethodByName(method.ToString());
            var processorCoin = WholePaymentProcessor.Instance().Company.System.Coins.SearchByIsoCode(coin.Iso4217Code);

            GroupOFTransactions processorGroupOFTransactions = new GroupOFTransactions();
            var processorTransaction = WholePaymentProcessor.Instance().SearchTransactionByName(transactionType.ToString());
            processorGroupOFTransactions.Add(processorTransaction);

            var result = new PaymentProcessor(nextConsecutive, $"{nameof(PaymentProcessor)} for {entity.Name}.", entity, processorPaymentMethod, processorCoin, processorGroupOFTransactions, implementation);
            processors.Add(result);
            all_processors.Add(result);

            return result;
        }

        internal PaymentProcessor AddPaymentProcessor(TransactionType transactionType, Coin coin, Entity entity, PaymentMethod method, PaymentProcessor paymentProcessor)
        {
            if (coin == null) throw new ArgumentNullException(nameof(coin));
            if (entity == null) throw new ArgumentNullException(nameof(entity));
            if (paymentProcessor == null) throw new ArgumentNullException(nameof(paymentProcessor));

            var processors = GetOrCreatePaymentProcessor(transactionType, coin, entity, method);

            if (!processors.Contains(paymentProcessor))
            {
                processors.Add(paymentProcessor);
            }

            return paymentProcessor;
        }

        internal PaymentProcessor SearchPaymentProcessorX(TransactionType transactionType, string coinCode, int entityId, PaymentMethod method)
        {
            List<PaymentProcessor> paymentProcessors = GetPaymentProcessors(transactionType, coinCode, entityId, method);
            if (!paymentProcessors.Any())
            {
                var entityText = WholePaymentProcessor.Instance().Company.System.Entities.Find(entityId).Name;
                throw new GameEngineException($"There is no {nameof(PaymentProcessor)} configured for Entity Id: {entityId}({entityText}), {nameof(PaymentMethod)}: {method}, {nameof(TransactionType)}: {transactionType} and Coin code: {coinCode}.");
            }

            List<PaymentProcessor> filterProcessors = paymentProcessors.FindAll(x => x.Visible && x.Enabled);
            if (!filterProcessors.Any()) throw new GameEngineException($"There is no {nameof(PaymentProcessor)} visible or enabled or object[ProcessorDriver] for Entity Id: {entityId}, {nameof(PaymentMethod)}: {method}, {nameof(TransactionType)}: {transactionType} and Coin code: {coinCode}.");

            PaymentProcessor processorWithHigherVersion = filterProcessors.FirstOrDefault();
            foreach (var paymentProcessor in filterProcessors.Skip(1))
            {
                if (paymentProcessor.Driver.Version > processorWithHigherVersion.Driver.Version)
                    processorWithHigherVersion = paymentProcessor;
            }

            return processorWithHigherVersion;
        }


        internal PaymentProcessor AllPaymentProcessorX(TransactionType transactionType, string coinCode, int entityId, PaymentMethod method)
        {
            List<PaymentProcessor> paymentProcessors = GetPaymentProcessors(transactionType, coinCode, entityId, method);
            if (!paymentProcessors.Any())
            {
                var entityText = WholePaymentProcessor.Instance().Company.System.Entities.Find(entityId).Name;
                throw new GameEngineException($"There is no {nameof(PaymentProcessor)} configured for Entity Id: {entityId}({entityText}), {nameof(PaymentMethod)}: {method}, {nameof(TransactionType)}: {transactionType} and Coin code: {coinCode}.");
            }

            PaymentProcessor processorWithHigherVersion = paymentProcessors.FirstOrDefault();
            foreach (var paymentProcessor in paymentProcessors.Skip(1))
            {
                if (paymentProcessor.Driver.Version > processorWithHigherVersion.Driver.Version)
                    processorWithHigherVersion = paymentProcessor;
            }

            return processorWithHigherVersion;
        }

        internal void EnablePaymentProcessor(TransactionType transactionType, string coinCode, int entityId, PaymentMethod method)
        {
            List<PaymentProcessor> paymentProcessors = GetPaymentProcessors(transactionType, coinCode, entityId, method);
            if (!paymentProcessors.Any())
            {
                var entityText = WholePaymentProcessor.Instance().Company.System.Entities.Find(entityId).Name;
                throw new GameEngineException($"There is no {nameof(PaymentProcessor)} configured for Entity Id: {entityId}({entityText}), {nameof(PaymentMethod)}: {method}, {nameof(TransactionType)}: {transactionType} and Coin code: {coinCode}.");
            }

            List<PaymentProcessor> filterProcessors = paymentProcessors.FindAll(x => !x.Enabled);
            if (!filterProcessors.Any()) throw new GameEngineException($"There is any {nameof(PaymentProcessor)} to be disable for Entity Id: {entityId}, {nameof(PaymentMethod)}: {method}, {nameof(TransactionType)}: {transactionType} and Coin code: {coinCode}.");


            PaymentProcessor processorWithHigherVersion = filterProcessors.FirstOrDefault();
            foreach (var paymentProcessor in filterProcessors.Skip(1))
            {
                if (paymentProcessor.Driver.Version > processorWithHigherVersion.Driver.Version)
                    processorWithHigherVersion = paymentProcessor;
            }

            processorWithHigherVersion.Enabled = true;

        }

        internal void DisablePaymentProcessor(TransactionType transactionType, string coinCode, int entityId, PaymentMethod method)
        {
            List<PaymentProcessor> paymentProcessors = GetPaymentProcessors(transactionType, coinCode, entityId, method);
            if (!paymentProcessors.Any())
            {
                var entityText = WholePaymentProcessor.Instance().Company.System.Entities.Find(entityId).Name;
                throw new GameEngineException($"There is no {nameof(PaymentProcessor)} configured for Entity Id: {entityId}({entityText}), {nameof(PaymentMethod)}: {method}, {nameof(TransactionType)}: {transactionType} and Coin code: {coinCode}.");
            }

            List<PaymentProcessor> filterProcessors = paymentProcessors.FindAll(x => x.Enabled);
            if (!filterProcessors.Any()) throw new GameEngineException($"There is any {nameof(PaymentProcessor)} to be enable for Entity Id: {entityId}, {nameof(PaymentMethod)}: {method}, {nameof(TransactionType)}: {transactionType} and Coin code: {coinCode}.");

            PaymentProcessor processorWithHigherVersion = filterProcessors.FirstOrDefault();
            foreach (var paymentProcessor in paymentProcessors.Skip(1))
            {
                if (paymentProcessor.Driver.Version > processorWithHigherVersion.Driver.Version)
                    processorWithHigherVersion = paymentProcessor;
            }
            processorWithHigherVersion.Enabled = false;
        }

        internal bool ExistPaymentProcessor(TransactionType transactionType, string coinCode, int entityId, PaymentMethod method)
        {
            if (whole_processors.TryGetValue(transactionType, out var remainingProcessors))
            {
                if (remainingProcessors.TryGetValue(coinCode, out var remainingSparseMatrix))
                {
                    if (remainingSparseMatrix.TryGetValue(entityId, out var remainingSparseMatrix4))
                    {
                        if (remainingSparseMatrix4.TryGetValue(method, out var processors))
                        {
                            return true;
                        }
                    }
                }
            }
            return false;
        }

        internal IEnumerable<PaymentProcessor> SearchByX(TransactionType transaction, string iso4217Code)
        {
            Dictionary<string, Dictionary<int, Dictionary<PaymentMethod, List<PaymentProcessor>>>> affiliateResults;
            if (!whole_processors.TryGetValue(transaction, out affiliateResults)) throw new GameEngineException($"There are no processors for that transaction type {transaction}.");

            Dictionary<int, Dictionary<PaymentMethod, List<PaymentProcessor>>> currencyResults;
            if (!affiliateResults.TryGetValue(iso4217Code, out currencyResults)) throw new GameEngineException($"There are no processors for that coin {iso4217Code}.");

            List<PaymentProcessor> results = new List<PaymentProcessor>();
            foreach (var currencyResult in currencyResults)
            {
                foreach (var paymentMethod in currencyResult.Value)
                {
                    results.AddRange(paymentMethod.Value);
                }
            }

            return results;
        }

        internal abstract void Addon(PaymentProcessor processor);
        internal abstract bool IsACollectionNeeded();
        internal abstract bool IsEmpty();
        internal abstract MultipleProcessors ChangeToMultipleIndexProcessor();
        internal abstract PaymentProcessor SearchBy(string iso4217Code);
        internal abstract PaymentProcessor SearchBy(TransactionType transactionType, string iso4217Code);
        internal abstract PaymentProcessor SearchBy(TransactionType transactionType, string iso4217Code, Type processorDriverType);
        internal abstract IEnumerable<PaymentProcessor> SearchBy(TransactionType transactionType, int entityId, int paymentMethodId, int coinId);
        internal abstract PaymentProcessor SearchBy(Tenant_Actions action);
        internal abstract IEnumerable<PaymentProcessor> SearchProcessorsBy(Tenant_Actions action);
        internal abstract PaymentProcessor SearchBy(Type processorDriverType);
        internal abstract PaymentProcessor FirstOne();
        internal abstract PaymentProcessor SearchBy(Func<PaymentProcessor, bool> filter);
    }

    internal sealed class Indexer : IndexedProcessors
    {
        internal override void Addon(PaymentProcessor processor)
        {
            throw new NotImplementedException();
        }

        internal override MultipleProcessors ChangeToMultipleIndexProcessor()
        {
            throw new NotImplementedException();
        }

        internal override PaymentProcessor FirstOne()
        {
            throw new NotImplementedException();
        }

        internal override bool IsACollectionNeeded()
        {
            throw new NotImplementedException();
        }

        internal override bool IsEmpty()
        {
            return !whole_processors.Any();
        }

        internal override PaymentProcessor SearchBy(string iso4217Code)
        {
            Coin coin = Coinage.Coin(iso4217Code);

            var result = AllProcessors.FirstOrDefault(x => x.Visible && x.Enabled && x.Coin == coin);
            if (result == null) throw new GameEngineException($"There is no {nameof(PaymentProcessor)} configured  for {iso4217Code}.");

            return result;
        }

        internal override PaymentProcessor SearchBy(TransactionType transactionType, string iso4217Code)
        {
            throw new NotImplementedException();
        }

        internal override PaymentProcessor SearchBy(TransactionType transactionType, string iso4217Code, Type processorDriverType)
        {
            Coin coin = Coinage.Coin(iso4217Code);

            var result = AllProcessors.FirstOrDefault(x =>
                x.UseDriver(processorDriverType) &&
                x.Visible && x.Enabled &&
                x.Transactions.MatchWith(transactionType.ToString(), processorDriverType) &&
                x.Coin == coin
            );
            if (result == null) throw new GameEngineException($"There is no {nameof(PaymentProcessor)} configured  for {transactionType.ToString()} and {iso4217Code}.");

            return result;
        }

        internal override IEnumerable<PaymentProcessor> SearchBy(TransactionType transactionType, int entityId, int paymentMethodId, int coinId)
        {
            throw new NotImplementedException();
        }

        internal override PaymentProcessor SearchBy(Tenant_Actions action)
        {
            throw new NotImplementedException();
        }

        internal override PaymentProcessor SearchBy(Type processorDriverType)
        {
            var result = AllProcessors.FirstOrDefault(x => x.UseDriver(processorDriverType) && x.Visible && x.Enabled);
            if (result == null) throw new GameEngineException($"There is no {nameof(PaymentProcessor)} configured for {processorDriverType}");

            return result;
        }

        internal override PaymentProcessor SearchBy(Func<PaymentProcessor, bool> filter)
        {
            throw new NotImplementedException();
        }

        internal IEnumerable<PaymentProcessor> SearchByCoin(Coin coin)
        {
            var result = AllProcessors.Where(x => x.Visible && x.Enabled && x.Coin == coin);
            if (result == null) throw new GameEngineException($"There is no {nameof(PaymentProcessor)} configured  for {coin.Iso4217Code}.");

            return result;
        }

        internal override IEnumerable<PaymentProcessor> SearchProcessorsBy(Tenant_Actions action)
        {
            throw new NotImplementedException();
        }

        internal void ReorderEntities(ProcessorTransaction transactionType, Coin coin, List<int> entityIdsToReorder)
        {
            var processorsToReorder = new List<PaymentProcessor>();
            var processorsToRemove = AllProcessors.Where(processor => processor.ContainsTransactionType(transactionType) && processor.Coin == coin && entityIdsToReorder.Contains(processor.Entity.Id));
            if (!processorsToRemove.Any()) throw new GameEngineException($"There are no processors matching with {transactionType} {coin} and entities {string.Join(',', entityIdsToReorder)}");
            if (processorsToRemove.Count() == 1) return;

            for (int index = AllProcessors.Count - 1; index >= 0; index--)
            {
                PaymentProcessor paymentProcessorIndex = AllProcessors.ElementAt(index);
                if (processorsToRemove.Contains(paymentProcessorIndex))
                {
                    processorsToReorder.Add(paymentProcessorIndex);
                    AllProcessors.Remove(paymentProcessorIndex);
                }
            }
            processorsToReorder.Sort(new EntityOrderComparer(entityIdsToReorder));
            foreach (var processor in processorsToReorder)
            {
                AllProcessors.Add(processor);
            }
        }

        internal void Clear()
        {
            all_processors.Clear();
            whole_processors.Clear();
        }
    }

    internal sealed class SingleProcessor : IndexedProcessors
    {
        private PaymentProcessor procesor;

        internal override void Addon(PaymentProcessor processor)
        {
            if (this.procesor != null) throw new GameEngineException("Only one instance can be added.");

            this.procesor = processor;
        }

        internal override MultipleProcessors ChangeToMultipleIndexProcessor()
        {
            MultipleProcessors result = new MultipleProcessors();
            result.Addon(this.procesor);
            return result;
        }

        internal override bool IsACollectionNeeded()
        {
            return (this.procesor != null);
        }

        internal override bool IsEmpty()
        {
            return (this.procesor == null);
        }

        internal override PaymentProcessor SearchBy(string iso4217Code)
        {
            Coin coin = Coinage.Coin(iso4217Code);

            if (procesor.Visible && procesor.Enabled &&
                 procesor.Coin == coin)
                return procesor;

            throw new GameEngineException($"There is no {nameof(PaymentProcessor)} configured  for {iso4217Code}.");

        }

        internal override PaymentProcessor SearchBy(TransactionType transactionType, string iso4217Code)
        {
            Coin coin = Coinage.Coin(iso4217Code);

            if (procesor.Visible && procesor.Enabled &&
                procesor.Transactions.FirstOrDefault(x => x.Name == transactionType.ToString()) != null &&
                procesor.Coin == coin)
                return procesor;

            throw new GameEngineException($"There is no {nameof(PaymentProcessor)} configured  for {transactionType.ToString()} and {iso4217Code}.");

        }

        internal override PaymentProcessor SearchBy(TransactionType transactionType, string iso4217Code, Type processorDriverType)
        {

            Coin coin = Coinage.Coin(iso4217Code);

            if (procesor.UseDriver(processorDriverType) && procesor.Visible && procesor.Enabled &&
                procesor.Transactions.MatchWith(transactionType.ToString(), processorDriverType) &&
                procesor.Coin == coin)
                return procesor;

            throw new GameEngineException($"There is no {nameof(PaymentProcessor)} configured  for {transactionType.ToString()} and {iso4217Code}.");

        }

        internal override IEnumerable<PaymentProcessor> SearchBy(TransactionType transactionType, int entityId, int paymentMethodId, int coinId)
        {
            if (entityId <= 0) throw new GameEngineException($"{nameof(entityId)} '{entityId}' must be greater than 0");
            if (paymentMethodId <= 0) throw new GameEngineException($"{nameof(paymentMethodId)} '{paymentMethodId}' must be greater than 0");
            if (coinId < 0) throw new GameEngineException($"{nameof(coinId)} '{coinId}' must be greater than 0");

            if (procesor.Visible && procesor.Enabled &&
                procesor.Entity.Id == entityId &&
                procesor.Group.Id == paymentMethodId &&
                procesor.Transactions.FirstOrDefault(x => x.Name == transactionType.ToString()) != null &&
                procesor.ProcessorCoin.Id == coinId)
                return Enumerable.Empty<PaymentProcessor>().Append(procesor);

            var entityText = WholePaymentProcessor.Instance().Company.System.Entities.Find(entityId).Name;
            throw new GameEngineException($"There is no {nameof(PaymentProcessor)} configured for {transactionType}, {nameof(entityId)} {entityId}({entityText}), {nameof(paymentMethodId)} {paymentMethodId} and {nameof(coinId)} {coinId}.");
        }

        internal override PaymentProcessor SearchBy(Tenant_Actions action)
        {
            if (procesor.Driver is TenantDriver && (procesor.Driver as TenantDriver).TenantAction == action && procesor.Visible && procesor.Enabled)
                return procesor;

            throw new GameEngineException($"There is no {nameof(PaymentProcessor)} configured for {action}");
        }

        internal override IEnumerable<PaymentProcessor> SearchProcessorsBy(Tenant_Actions action)
        {
            if (procesor.Driver is TenantDriver && (procesor.Driver as TenantDriver).TenantAction == action && procesor.Visible && procesor.Enabled)
                return Enumerable.Empty<PaymentProcessor>().Append(procesor);

            throw new GameEngineException($"There is no {nameof(PaymentProcessor)} configured for {action}");
        }

        internal override PaymentProcessor SearchBy(Type processorDriverType)
        {
            if (procesor.UseDriver(processorDriverType) && procesor.Visible && procesor.Enabled)
                return procesor;

            throw new GameEngineException($"There is no {nameof(PaymentProcessor)} configured for {processorDriverType}");
        }

        internal override PaymentProcessor FirstOne()
        {
            if (procesor.Visible && procesor.Enabled)
                return procesor;

            throw new GameEngineException($"There is no {nameof(PaymentProcessor)} configured.");
        }

        internal override PaymentProcessor SearchBy(Func<PaymentProcessor, bool> filter)
        {
            var result = filter(procesor);
            if (result ) return procesor;

            throw new GameEngineException($"There is no {nameof(PaymentProcessor)} configured for that filter.");
        }
    }

    internal class EntityOrderComparer : IComparer<PaymentProcessor>
    {
        private List<int> order;

        public EntityOrderComparer(List<int> order)
        {
            this.order = order;
        }

        public int Compare(PaymentProcessor x, PaymentProcessor y)
        {
            int xIndex = order.IndexOf(x.Entity.Id);
            int yIndex = order.IndexOf(y.Entity.Id);

            return xIndex.CompareTo(yIndex);
        }
    }

    [Puppet]
    internal sealed class MultipleProcessors : IndexedProcessors, IEnumerable<PaymentProcessor>
    {
        private readonly List<PaymentProcessor> processors = new List<PaymentProcessor>();
        
        internal void ReorderEntities(ProcessorTransaction transactionType, Coin coin, List<int> entityIdsToReorder)
        {
            var processorsToReorder = new List<PaymentProcessor>();
            var processorsToRemove = processors.Where(processor => processor.ContainsTransactionType(transactionType) && processor.Coin == coin && entityIdsToReorder.Contains(processor.Entity.Id));
            if (!processorsToRemove.Any()) throw new GameEngineException($"There are no processors matching with {transactionType} {coin} and entities {string.Join(',', entityIdsToReorder)}");
            if (processorsToRemove.Count() == 1) return;

            for (int index = processors.Count - 1; index >= 0; index--)
            {
                if (processorsToRemove.Contains(processors[index]))
                {
                    processorsToReorder.Add(processors[index]);
                    processors.Remove(processors[index]);
                }
            }
            processorsToReorder.Sort(new EntityOrderComparer(entityIdsToReorder));
            foreach (var processor in processorsToReorder)
            {
                processors.Add(processor);
            }
        }

        public IEnumerator<PaymentProcessor> GetEnumerator()
        {
            return this.processors.GetEnumerator();
        }

        IEnumerator IEnumerable.GetEnumerator()
        {
            return this.processors.GetEnumerator();
        }

        internal override void Addon(PaymentProcessor processor)
        {
            this.processors.Add(processor);
        }

        internal override bool IsACollectionNeeded()
        {
            return false;
        }

        internal override bool IsEmpty()
        {
            return false;
        }

        internal override MultipleProcessors ChangeToMultipleIndexProcessor()
        {
            return this;
        }

        internal IEnumerable<PaymentProcessor> SearchByCoin(Coin coin)
        {
            var result = this.processors.Where(x => x.Visible && x.Enabled && x.Coin == coin);

            if (result == null) throw new GameEngineException($"There is no {nameof(PaymentProcessor)} configured  for {coin.Iso4217Code}.");

            return result;
        }

        internal override PaymentProcessor SearchBy(string iso4217Code)
        {
            Coin coin = Coinage.Coin(iso4217Code);

            var result = this.processors.FirstOrDefault(x => x.Visible && x.Enabled &&
            x.Coin == coin);

            if (result == null) throw new GameEngineException($"There is no {nameof(PaymentProcessor)} configured  for {iso4217Code}.");

            return result;
        }

        internal PaymentProcessor SearchByTransactionType(TransactionType transactionType, string iso4217Code)
        {
            return SearchBy(transactionType, iso4217Code);
        }

        internal override PaymentProcessor SearchBy(TransactionType transactionType, string iso4217Code)
        {
            Coin coin = Coinage.Coin(iso4217Code);

            var result = this.processors.FirstOrDefault(x => x.Visible && x.Enabled &&
            x.Transactions.FirstOrDefault(x => x.Name == transactionType.ToString()) != null &&
            x.Coin == coin);

            if (result == null) throw new GameEngineException($"There is no {nameof(PaymentProcessor)} configured  for {transactionType.ToString()} and {iso4217Code}.");

            return result;
        }

        internal PaymentProcessor SearchByX(TransactionType transactionType, string coinCode, PaymentMethod paymentMethod, int entityId)
        {
            if (string.IsNullOrWhiteSpace(coinCode)) throw new ArgumentNullException(nameof(coinCode));
            if (entityId <= 0) throw new GameEngineException($"{nameof(entityId)} '{entityId}' must be greater than 0");

            PaymentProcessor processorResult = this.SearchPaymentProcessorX(transactionType, coinCode, entityId, paymentMethod);
            return processorResult;
        }

        internal IEnumerable<PaymentProcessor> SearchBy(ProcessorTransaction transaction, int entityId, int paymentMethodId, int coinId)
        {
            if (transaction == null) throw new ArgumentNullException(nameof(transaction));
            if (entityId <= 0) throw new GameEngineException($"{nameof(entityId)} '{entityId}' must be greater than 0");
            if (paymentMethodId <= 0) throw new GameEngineException($"{nameof(paymentMethodId)} '{paymentMethodId}' must be greater than 0");
            if (coinId < 0) throw new GameEngineException($"{nameof(coinId)} '{coinId}' must be greater than 0");

            var result = this.processors.Where(processor => processor.Visible && 
                                                            processor.Transactions.Contains(transaction) &&
                                                            processor.ProcessorCoin.Id == coinId &&
                                                            processor.Group.Id == paymentMethodId &&
                                                            processor.Entity.Id == entityId &&
                                                            processor.Driver is ProcessorDriver);

            if (result == null || !result.Any())
            {
                var entityText = WholePaymentProcessor.Instance().Company.System.Entities.Find(entityId).Name;
                throw new GameEngineException($"There is no {nameof(PaymentProcessor)} configured for {nameof(entityId)} {entityId}({entityText}), {nameof(paymentMethodId)} {paymentMethodId}, {nameof(transaction)} {transaction.Name} and {nameof(coinId)} {coinId}.");
            }

            return result;
        }

        internal PaymentProcessor SearchBySpecificClass(TransactionType transactionType, string iso4217Code, Type processorDriverType)
        {
            return SearchBy(transactionType, iso4217Code, processorDriverType);
        }

        internal override PaymentProcessor SearchBy(TransactionType transactionType, string iso4217Code, Type processorDriverType)
        {

            Coin coin = Coinage.Coin(iso4217Code);

            var result = this.processors.FirstOrDefault(x => x.UseDriver(processorDriverType) && x.Visible && x.Enabled &&
                                                            x.Transactions.MatchWith(transactionType.ToString(), processorDriverType) &&
                                                            x.Coin == coin);

            if (result == null) throw new GameEngineException($"There is no {nameof(PaymentProcessor)} configured  for {transactionType.ToString()} and {iso4217Code}.");

            return result;
        }

        internal override IEnumerable<PaymentProcessor> SearchBy(TransactionType transactionType, int entityId, int paymentMethodId, int coinId)
        {
            if (entityId <= 0) throw new GameEngineException($"{nameof(entityId)} '{entityId}' must be greater than 0");
            if (paymentMethodId <= 0) throw new GameEngineException($"{nameof(paymentMethodId)} '{paymentMethodId}' must be greater than 0");
            if (coinId < 0) throw new GameEngineException($"{nameof(coinId)} '{coinId}' must be greater than 0");

            var result = this.processors.Where(processor => processor.Visible &&
                                                            processor.Entity.Id == entityId &&
                                                            processor.Group.Id == paymentMethodId &&
                                                            processor.Transactions.FirstOrDefault(x => x.Name == transactionType.ToString()) != null &&
                                                            processor.ProcessorCoin.Id == coinId &&
                                                            processor.Driver is ProcessorDriver);

            if (result == null || !result.Any())
            {
                var entityText = WholePaymentProcessor.Instance().Company.System.Entities.Find(entityId).Name;
                throw new GameEngineException($"There is no {nameof(PaymentProcessor)} configured for {transactionType}, {nameof(entityId)} {entityId}({entityText}), {nameof(paymentMethodId)} {paymentMethodId} and {nameof(coinId)} {coinId}.");
            }

            return result;
        }

        internal PaymentProcessor SearchBySpecificClass(Tenant_Actions actions)
        {
            return SearchBy(actions);
        }

        internal override PaymentProcessor SearchBy(Tenant_Actions action)
        {
            var result = this.processors.FirstOrDefault(x => x.Driver is TenantDriver && (x.Driver as TenantDriver).TenantAction == action && x.Visible && x.Enabled);

            if (result == null) throw new GameEngineException($"There is no {nameof(PaymentProcessor)} configured for {action}");

            return result;
        }

        internal override IEnumerable<PaymentProcessor> SearchProcessorsBy(Tenant_Actions action)
        {
            var result = this.processors.Where(x => x.Driver is TenantDriver && (x.Driver as TenantDriver).TenantAction == action && x.Visible && x.Enabled);

            if (result == null) throw new GameEngineException($"There is no {nameof(PaymentProcessor)} configured for {action}");

            return result;
        }

        internal override PaymentProcessor SearchBy(Type processorDriverType)
        {
            var result = this.processors.FirstOrDefault(x => x.UseDriver(processorDriverType) && x.Visible && x.Enabled);

            if (result == null) throw new GameEngineException($"There is no {nameof(PaymentProcessor)} configured for {processorDriverType}");

            return result;
        }

        internal override PaymentProcessor FirstOne()
        {
            var result = this.processors.FirstOrDefault(x => x.Visible && x.Enabled);

            if (result == null) throw new GameEngineException($"There is no {nameof(PaymentProcessor)}.");

            return result;
        }

        internal bool ThereIsOnlyOne()
        {
            return processors.Count == 1;
        }

        internal override PaymentProcessor SearchBy(Func<PaymentProcessor, bool> filter)
        {
            foreach (var procesor in this.processors)
            {
                var result = filter(procesor);
                if (result) return procesor;
            }

            throw new GameEngineException($"There is no {nameof(PaymentProcessor)} configured for that filter.");
        }

        internal void Clear()
        {
            processors.Clear();
        }
    }
}
