﻿using Connectors.town.connectors.driver.transactions;
using GamesEngine.Settings;
using Newtonsoft.Json;
using System;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using town.connectors.commons;
using static town.connectors.drivers.Result;

namespace town.connectors.drivers.fiero
{
    public abstract class Authorization : FierroProcessorDriver, IDriverUserProperties
    {
		public const int FAKE_TICKET_NUMBER = -1;
		private const float VERSION = 1.0F;

		public string CashierUrl { get; private set; }

		public string DriverUserName { get; set; }
		public string DriverPassword { get; set; }

		public override string Description => $"Fiero {nameof(Authorization)} driver {VERSION}";

		public static TokenDriver CashierToken { get; set; }


		public Authorization(string currencyCode) : base(TransactionType.Authorization, VERSION, currencyCode)
        {
        }

		public override async Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet)
		{
            bool userPassHasChange = DriverUserName != CustomSettings.Get(now, "CashierDriver.userName").AsString || DriverPassword != CustomSettings.Get(now, "CashierDriver.password").AsString;
            if (userPassHasChange)
            {
                DriverUserName = CustomSettings.Get(now, "CashierDriver.userName").AsString;
                DriverPassword = CustomSettings.Get(now, "CashierDriver.password").AsString;
            }

			bool itsSecuritySchemeConfigured = SecurityConfiguration.WasConfigure() && SecurityConfiguration.ItsSecuritySchemeConfigured();

             bool hasExpired = CashierToken == null || SecurityConfiguration.ItsSecurityTokenExpired(CashierToken.access_token, now);

            bool needToChangeToken = (CashierToken == null || userPassHasChange || hasExpired) && itsSecuritySchemeConfigured;
            if (needToChangeToken) CashierToken = await TokenDriver.GetTokenAsync(DriverUserName, DriverPassword);

            var result = await AuthorzationAsync(now, recordSet);
			AuthorizationTransaction auth;
			if (result > 0)
			{
				auth= new AuthorizationTransaction(result, TransactionStatus.APPROVED, Entity, PaymentMethod, CurrencyIsoCodes);
				return (T)Convert.ChangeType(auth, typeof(T));
			}
			auth= new AuthorizationTransaction(result, TransactionStatus.DENIED, Entity, PaymentMethod, CurrencyIsoCodes);
			return (T)Convert.ChangeType(auth, typeof(T));
		}

		public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet)
		{
			throw new NotImplementedException();
		}

        public override void Prepare(DateTime now)
		{
			CustomSettings.AddVariableParameter("storeId");
			CustomSettings.AddVariableParameter("purchaseTotal");
			CustomSettings.AddVariableParameter("concept");
			CustomSettings.AddVariableParameter("reference");
			CustomSettings.AddVariableParameter("accountNumber");
			CustomSettings.AddVariableParameter("processorId");
			CustomSettings.AddVariableParameter("who");
			CustomSettings.AddVariableParameter("fragmentInformation");

            CustomSettings.AddVariableParameter("atAddress");
            CustomSettings.AddVariableParameter("useless");

			CashierUrl = CustomSettings.Get(now, "CashierDriver.url").AsString;
			DriverUserName = CustomSettings.Get(now, "CashierDriver.userName").AsString;
			DriverPassword = CustomSettings.Get(now, "CashierDriver.password").AsString;
        }
		private async Task<int> AuthorzationAsync(DateTime now, CustomSettings.RecordSet recordSet)
		{
			var storeId = recordSet.Mappings["storeId"].AsInt;
			var purchaseTotal = recordSet.Mappings["purchaseTotal"].AsDecimal;
			var concept = recordSet.Mappings["concept"].AsString;
			
			var processorId = recordSet.Mappings["processorId"].AsInt;
			var who = recordSet.Mappings["who"].AsString;

			var atAddress = recordSet.Mappings["atAddress"].AsString;
			var useless = recordSet.Mappings["useless"].AsDateTime;

			var lockBalanceData = new LockBalanceData();
			lockBalanceData.StoreId = storeId;
            lockBalanceData.PurchaseTotal = purchaseTotal;
            lockBalanceData.Concept = concept;
            if (recordSet.ContainsKeyName("reference")) lockBalanceData.Reference = recordSet.Mappings["reference"].AsString; 
            lockBalanceData.AccountNumber = string.Empty;
			lockBalanceData.ProcessorId = processorId;
			lockBalanceData.Who = who;
			if (recordSet.ContainsKeyName("fragmentInformation")) lockBalanceData.FragmentInformation = recordSet.Mappings["fragmentInformation"].As<FragmentInformation>();

            lockBalanceData.AtAddress = atAddress;
            lockBalanceData.Useless = useless;

			GamesEngine.Finance.LockBalanceResponse lockBalanceResponse;
			try
			{
				var url = $"{CashierUrl}api/customers/{atAddress}/balance/lock";

                HttpClient client = new HttpClient();

                if (SecurityConfiguration.WasConfigure() && SecurityConfiguration.ItsSecuritySchemeConfigured()) client.DefaultRequestHeaders.Add("Authorization", $"Bearer {CashierToken.access_token}"); 

                var jsonString = JsonConvert.SerializeObject(lockBalanceData);
                var httpContent = new StringContent(jsonString, Encoding.UTF8, "application/json");
                HttpResponseMessage resultFromCashier = await client.PostAsync(url, httpContent);


                //if (!(resultFromCashier is OkObjectResult))
                if (resultFromCashier.StatusCode != System.Net.HttpStatusCode.OK)
				{
					string body = string.Empty;
					//if (resultFromCashier is ContentResult)
					if (resultFromCashier.Content != null)
					{
						body = $@"error:{resultFromCashier.Content} \n url:{url} \n atAddress:{atAddress} \n purchaseTotal:{purchaseTotal}";
					}
					else
					{
						body = $@"url:{url} \n atAddress:{atAddress} \n purchaseTotal:{purchaseTotal}";
					}
					ErrorsSender.Send(body, $@"Lock amount to atAddress: {atAddress} fails.");
					return FAKE_TICKET_NUMBER;
				}

				//string resp = ((OkObjectResult)resultFromCashier).Value.ToString();
				string resp = resultFromCashier.Content.ToString();
				lockBalanceResponse = JsonConvert.DeserializeObject<GamesEngine.Finance.LockBalanceResponse>(resp);
			}
			catch (Exception e)
			{
				ErrorsSender.Send(e);
				return FAKE_TICKET_NUMBER;
			}

			return lockBalanceResponse.AuthorizationNumber;
		}

     
	}
}
