﻿using Puppeteer.EventSourcing;
using RestSharp;
using System;
using System.Threading.Tasks;
using town.connectors;
using town.connectors.commons;
using town.connectors.drivers;
using town.connectors.drivers.artemis;

namespace Connectors.town.connectors.drivers.artemis
{
    public class BalanceInfo : DGSTenantDriver
	{
        private RestClient _getBalanceClient;

        public BalanceInfo() : base(Tenant_Actions.Balance, TransactionType.RetrieveBalance, "USD")
        {
        }

        public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet)
        {
            throw new NotImplementedException();
        }
        public override void Prepare(DateTime now)
        {
            CustomSettings.AddVariableParameter("atAddress");

            SystemId = CustomSettings.Get(now, "TokenSystemId").AsString;
            Password = CustomSettings.Get(now, "TokenSystemPassword").AsSecret.ToString();
            ServicesUrl = CustomSettings.Get(now, "CompanyBaseUrlServices").AsString;

            if (_getBalanceClient == null)
            {
                if (string.IsNullOrWhiteSpace(SystemId)) throw new ArgumentNullException("Custom variable 'TokenSystemId' is required");
                if (string.IsNullOrWhiteSpace(Password)) throw new ArgumentNullException("Custom variable 'TokenSystemPassword' is required");
                if (string.IsNullOrWhiteSpace(ServicesUrl)) throw new ArgumentNullException("Custom variable 'CompanyBaseUrlServices' is required");

                if (string.IsNullOrWhiteSpace(DGSProcessorDriver.AppToken))
                {
                    _ = Task.Run(async () => { 
                        await DGSTenantDriver.RequestAppTokenAsync(ServicesUrl, SystemId, Password);
                    });
                }

                _getBalanceClient = new RestClient(ServicesUrl);
            }
        }
        public override async Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet)
        {
            if (CustomSettings.ThereArePendingChanges)
            {
                bool changeApplied = false;
                ServicesUrl = CustomSettings.Get(now, "CompanyBaseUrlServices", out changeApplied).AsString;
                SystemId = CustomSettings.Get(now, "TokenSystemId").AsString;
                Password = CustomSettings.Get(now, "TokenSystemPassword").AsSecret.ToString();

                if (changeApplied)
                {
                    if (string.IsNullOrWhiteSpace(DGSProcessorDriver.AppToken))
                    {
                        await DGSTenantDriver.RequestAppTokenAsync(ServicesUrl, SystemId, Password);
                    }

                    _getBalanceClient = new RestClient(ServicesUrl);
                }
            }
            var result = await AvailableBalanceAsync(now, recordSet);

            if (typeof(T) == typeof(decimal))
            {
                decimal resultDecimal = result.realAvailBalance;
                return (T)Convert.ChangeType(resultDecimal, typeof(T));
            }
            else if (typeof(T) == typeof(PlayerBalanceBody))
            {
                return (T)Convert.ChangeType(result, typeof(T));
            }
            else
            {
                throw new Exception($"Unsupported type {typeof(T)} for {nameof(ExecuteAsync)}");
            }
		}
        private async Task<PlayerBalanceBody> AvailableBalanceAsync(DateTime now, CustomSettings.RecordSet recordSet)
        {
            if (string.IsNullOrWhiteSpace(DGSProcessorDriver.AppToken)) throw new Exception($"{nameof(DGSProcessorDriver.AppToken)} is empty");

            var CustomerId = recordSet.Mappings["atAddress"];
            string customerId = CustomerId.AsString;

            if (string.IsNullOrWhiteSpace(customerId)) throw new ArgumentNullException(nameof(customerId));

            string url = $"/GetPlayerBalance";

            string responseString = "";
            int retryNumber = 0;
            var result = new PlayerBalanceBody();
            while (true)
            {
                try
                {
                    var request = new RestRequest(url, Method.Get);
                    request.AddHeader("Authorization", $"Bearer {DGSProcessorDriver.AppToken}");
                    request.AddParameter("id", customerId);
                    var response = await _getBalanceClient.ExecuteAsync(request);
                    responseString = response.Content;
                    if ((int)response.StatusCode != 200)
                    {
                        Loggers.GetIntance().AccountingServicesInfo.Debug($"{nameof(AvailableBalanceAsync)}\nUrl:{url}\nResponse: {responseString}");
                    }
                    break;
                }
                catch (Exception e)
                {
                    Loggers.GetIntance().AccountingServicesInfo.Error($@"url:{url} type:{e.GetType()} error:{e.Message}", e);

                    retryNumber++;

                    await Task.Delay(5000);
                    if (retryNumber == MAX_RETRIES) return result;
                }
            }

            if (string.IsNullOrWhiteSpace(responseString))
            {
                NotifyWarn(nameof(AvailableBalanceAsync), $"Url:{url}\nResponse: {responseString}", $"Response can not be empty");
                return result;
            }
            else
            {
                var errorResponse = Commons.FromJson<ErrorResponse>(responseString);
                if (errorResponse != null && !string.IsNullOrWhiteSpace(errorResponse.Message))
                {
                    Loggers.GetIntance().AccountingServicesInfo.Debug($"{nameof(AvailableBalanceAsync)}\nUrl:{url}\nResponse: {responseString}");
                }
                else
                {
                    try
                    {
                        var responseObj = Commons.FromJson<PlayerBalanceBody>(responseString);
                        result = responseObj;
                    }
                    catch (Exception e)
                    {
                        NotifyWarn(nameof(AvailableBalanceAsync), $"Url:{url}\nResponse: {responseString}", $"{e.Message}\r{nameof(responseString)} {responseString} is not valid");
                    }
                }
                return result;
            }
        }
    }
}
