﻿using Connectors.town.connectors.driver.transactions;
using GamesEngine.Business;
using GamesEngine.Settings;
using Newtonsoft.Json;
using RestSharp;
using System;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using static town.connectors.drivers.Result;

namespace town.connectors.drivers.fiero
{

    internal abstract class Deposit : FierroProcessorDriver, IDriverUserProperties
    {
        public static TokenDriver CashierToken { get; set; }
        private RestClient _postUpdateWagersClient;

        public string DriverUserName { get; set; }
        public string DriverPassword { get; set; }

        private const float VERSION = 1.1F;
		public override string Description => $"Fiero {nameof(Deposit)} driver {VERSION}";

		public Deposit(string currencyIsoCode)
			: base(town.connectors.drivers.TransactionType.Deposit, VERSION, currencyIsoCode)
        {
        }
		public override async Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet)
		{
            string cahiserUrl = CustomSettings.Get(now, "CashierDriver.url").AsString;
            bool userPassHasChange =
                          DriverUserName != CustomSettings.Get(now, "CashierDriver.userName").AsString ||
                          DriverPassword != CustomSettings.Get(now, "CashierDriver.password").AsString;
            if (userPassHasChange)
            {
                DriverUserName = CustomSettings.Get(now, "CashierDriver.userName").AsString;
                DriverPassword = CustomSettings.Get(now, "CashierDriver.password").AsString;
            }

            bool itsSecuritySchemeConfigured = SecurityConfiguration.ItsSecuritySchemeConfigured();

            bool hasExpired = CashierToken == null || SecurityConfiguration.ItsSecurityTokenExpired(CashierToken.access_token, now);

            bool needToChangeToken = (CashierToken == null || userPassHasChange || hasExpired) && itsSecuritySchemeConfigured;
            if (needToChangeToken) CashierToken = await TokenDriver.GetTokenAsync(DriverUserName, DriverPassword);

            if (_postUpdateWagersClient == null || _postUpdateWagersClient.Options.BaseUrl.ToString() != cahiserUrl)
            {
                _postUpdateWagersClient = new RestClient(cahiserUrl);
            }

            string url = "/api/deposit";
            TransactionBody transactionBody = new TransactionBody
            {
                Now = now,
                Amount = recordSet.Mappings["amount"].AsDecimal,
                AccountNumber = recordSet.Mappings["accountNumber"].AsString,
                Currency = recordSet.Mappings["currency"].AsString,
                AtAddress = recordSet.Mappings["atAddress"].AsString,
                Domain = recordSet.Mappings["domain"].AsString,
                SourceNumber = recordSet.Mappings["sourceNumber"].AsInt,
                SourceName = recordSet.Mappings["sourceName"].AsString,
                Who = recordSet.Mappings["who"].AsString,
                Agent = recordSet.Mappings["agent"].AsInt,
                StoreId = recordSet.Mappings["storeId"].AsInt,
                ProcessorId = recordSet.Mappings["processorId"].AsInt,
                Description = recordSet.Mappings["description"].AsString,
                Reference = recordSet.Mappings["reference"].AsString,

            };

            var result = await AddAmountAsync(url, transactionBody);
            DepositTransaction auth;
            if (result > 0)
            {
                auth = new DepositTransaction(result, TransactionStatus.APPROVED, Entity, PaymentMethod, CurrencyIsoCodes);
            }
            else
            {
                auth = new DepositTransaction(result, TransactionStatus.DENIED, Entity, PaymentMethod, CurrencyIsoCodes);
            }
            return (T)Convert.ChangeType(auth, typeof(T));
        }

		public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet)
		{
            string cahiserUrl = CustomSettings.Get(now, "CashierDriver.url").AsString;

            bool userPassHasChange =
                          DriverUserName != CustomSettings.Get(now, "CashierDriver.userName").AsString ||
                          DriverPassword != CustomSettings.Get(now, "CashierDriver.password").AsString;
            if (userPassHasChange)
            {
                DriverUserName = CustomSettings.Get(now, "CashierDriver.userName").AsString;
                DriverPassword = CustomSettings.Get(now, "CashierDriver.password").AsString;
            }

            bool itsSecuritySchemeConfigured = SecurityConfiguration.WasConfigure() && SecurityConfiguration.ItsSecuritySchemeConfigured();

            bool hasExpired = CashierToken == null || SecurityConfiguration.ItsSecurityTokenExpired(CashierToken.access_token, now);

            bool needToChangeToken = (CashierToken == null || userPassHasChange || hasExpired) && itsSecuritySchemeConfigured;
            if (needToChangeToken) CashierToken = TokenDriver.GetToken(DriverUserName, DriverPassword);

            if (_postUpdateWagersClient == null || _postUpdateWagersClient.Options.BaseUrl.ToString() != cahiserUrl)
            {
                _postUpdateWagersClient = new RestClient(cahiserUrl);
            }

            string url = "/api/deposit";
            TransactionBody transactionBody = new TransactionBody
            {
                Now = now,
                Amount = recordSet.Mappings["amount"].AsDecimal,
                AccountNumber = recordSet.Mappings["accountNumber"].AsString,
                Currency = recordSet.Mappings["currency"].AsString,
                AtAddress = recordSet.Mappings["atAddress"].AsString,
                Domain = recordSet.Mappings["domain"].AsString,
                SourceNumber = recordSet.Mappings["sourceNumber"].AsInt,
                SourceName = recordSet.Mappings["sourceName"].AsString,
                Who = recordSet.Mappings["who"].AsString,
                Agent = recordSet.Mappings["agent"].AsInt,
                StoreId = recordSet.Mappings["storeId"].AsInt,
                ProcessorId = recordSet.Mappings["processorId"].AsInt,
                Description = recordSet.Mappings["description"].AsString,
                Reference = recordSet.Mappings["reference"].AsString,

            };

            var result = AddAmount(url, transactionBody);
            DepositTransaction auth;
            if (result > 0)
            {
                auth = new DepositTransaction(result, TransactionStatus.APPROVED, Entity, PaymentMethod, CurrencyIsoCodes);
            }
            else
            {
                auth = new DepositTransaction(result, TransactionStatus.DENIED, Entity, PaymentMethod, CurrencyIsoCodes);
            }
            return (T)Convert.ChangeType(auth, typeof(T));
        }

		public override void Prepare(DateTime now)
		{
            CustomSettings.AddVariableParameter("amount");
            CustomSettings.AddVariableParameter("accountNumber");
            CustomSettings.AddVariableParameter("currency");
            CustomSettings.AddVariableParameter("atAddress");
            CustomSettings.AddVariableParameter("domain");
            CustomSettings.AddVariableParameter("sourceNumber");
            CustomSettings.AddVariableParameter("sourceName");
            CustomSettings.AddVariableParameter("who");
            CustomSettings.AddVariableParameter("agent");
            CustomSettings.AddVariableParameter("storeId");
            CustomSettings.AddVariableParameter("processorId");
            CustomSettings.AddVariableParameter("description");
            CustomSettings.AddVariableParameter("reference");

            DriverUserName = CustomSettings.Get(now, "CashierDriver.userName").AsString;
            DriverPassword = CustomSettings.Get(now, "CashierDriver.password").AsString;
        }

        protected const int MAX_RETRIES = 5;
        private int AddAmount(string url, TransactionBody transactionBody)
		{
            int authorization = 0;
            int retryNumber = 0;

            //var jsonString = Commons.ToJson(transactionBody);
            var jsonString = JsonConvert.SerializeObject(transactionBody);

            string responseString = string.Empty;
            while (true)
            {
                try
                {
                    var request = new RestRequest(url, Method.Post);
                    if (SecurityConfiguration.WasConfigure() && SecurityConfiguration.ItsSecuritySchemeConfigured())
                    {
                        request.AddHeader("Authorization", $"Bearer {CashierToken.access_token}");
                    }
                    request.AddHeader("Content-Type", "application/json");
                    request.AddParameter("application/json", jsonString, ParameterType.RequestBody);

                    var response = _postUpdateWagersClient.Execute(request);
                    
                    responseString = response.Content;
                    if (response.StatusCode == HttpStatusCode.OK)
                    {
                            var requesResult = JsonConvert.DeserializeObject<TransactionResponse>(responseString);
                            authorization = requesResult.Authorization;
                    }
                    else
                    {
                        authorization = 0;//NO AUTHORIZATION
                    }
                    break;
                }
                catch (Exception e)
                {
                    retryNumber++;
                    var extraErrorMessage = string.Empty;
                    if (e.InnerException is HttpRequestException) extraErrorMessage = "There was a problem getting the response from server.";
                    InternalOnError(nameof(AddAmount), retryNumber, e, $"Url:{url}", $"Request: {jsonString}", $"Response: {responseString}");

                    Thread.Sleep(5000);
                    if (retryNumber == MAX_RETRIES)
                    {
                        authorization = 0;//NO AUTHORIZATION
                        break;
                    }
                }
            }

            return authorization;
        }

        private async Task<int> AddAmountAsync(string url, TransactionBody transactionBody)
        {
            int authorization = 0;
            int retryNumber = 0;

            //var jsonString = Commons.ToJson(transactionBody);
            var jsonString = JsonConvert.SerializeObject(transactionBody);

            string responseString = string.Empty;
            while (true)
            {
                try
                {
                    var request = new RestRequest(url, Method.Post);
                    if (SecurityConfiguration.WasConfigure() && SecurityConfiguration.ItsSecuritySchemeConfigured())
                    {
                        request.AddHeader("Authorization", $"Bearer {CashierToken.access_token}");
                    }
                    request.AddHeader("Content-Type", "application/json");
                    request.AddParameter("application/json", jsonString, ParameterType.RequestBody);

                    var response = await _postUpdateWagersClient.ExecuteAsync(request);

                    responseString = response.Content;
                    if (response.StatusCode == HttpStatusCode.OK)
                    {
                        var requesResult = JsonConvert.DeserializeObject<TransactionResponse>(responseString);
                        authorization = requesResult.Authorization;
                    }
                    else
                    {
                        authorization = 0;//NO AUTHORIZATION
                    }
                    break;
                }
                catch (Exception e)
                {
                    retryNumber++;
                    var extraErrorMessage = string.Empty;
                    if (e.InnerException is HttpRequestException) extraErrorMessage = "There was a problem getting the response from server.";
                    InternalOnError(nameof(AddAmount), retryNumber, e, $"Url:{url}", $"Request: {jsonString}", $"Response: {responseString}");

                    Thread.Sleep(5000);
                    if (retryNumber == MAX_RETRIES)
                    {
                        authorization = 0;//NO AUTHORIZATION
                        break;
                    }
                }
            }

            return authorization;
        }

    }
}
