﻿using GamesEngine.Bets;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GamesEngine.Games.Lotto
{
    internal class ReportsTriz : Objeto
    {
        private readonly TrizLotteryGame game;

        internal ReportsTriz(TrizLotteryGame lotteries)
        {
            this.game = lotteries;
        }

        internal PendingDraws PendingDrawings(DateTime startDate, DateTime endDate, string uniqueDrawingId, string accountNumber, string gameType, string ticketNumber, string domainIds)
        {
            if (startDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (endDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (string.IsNullOrWhiteSpace(uniqueDrawingId)) throw new ArgumentNullException(nameof(uniqueDrawingId));
            if (string.IsNullOrWhiteSpace(domainIds)) throw new ArgumentNullException(nameof(domainIds));

            var result = game.GetLottery().PendingDrawings(startDate, endDate, uniqueDrawingId, accountNumber, gameType, ticketNumber, domainIds);
            return result;
        }

        internal TicketsPerPlayers TicketsPerPlayersInPendingDrawing(DateTime drawDate, string uniqueDrawingId, string accountNumber, string gameType, string ticketNumber, string domainIds)
        {
            if (drawDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (string.IsNullOrWhiteSpace(uniqueDrawingId)) throw new ArgumentNullException(nameof(uniqueDrawingId));
            if (string.IsNullOrWhiteSpace(domainIds)) throw new ArgumentNullException(nameof(domainIds));

            var result = game.GetLottery().TicketsPerPlayersInPendingDrawing(drawDate, uniqueDrawingId, accountNumber, gameType, ticketNumber, domainIds);
            return result;
        }

        internal TicketsPerPlayers TicketsPerPlayersInPendingDrawings(DateTime startDate, DateTime endDate, string uniqueDrawingId, string accountNumber, string gameType, string ticketNumber, string domainIds)
        {
            if (startDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (endDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (string.IsNullOrWhiteSpace(uniqueDrawingId)) throw new ArgumentNullException(nameof(uniqueDrawingId));
            if (string.IsNullOrWhiteSpace(domainIds)) throw new ArgumentNullException(nameof(domainIds));

            var result = game.GetLottery().TicketsPerPlayersInPendingDrawings(startDate, endDate, uniqueDrawingId, accountNumber, gameType, ticketNumber, domainIds);
            return result;
        }

        internal WagersPerPlayerInPendingDraw WagersPerPlayerInPendingDrawing(Player player, DateTime drawDate, string drawingId, string gameType, string ticketNumber, string domainIds)
        {
            if (player == null) throw new ArgumentNullException(nameof(player));
            if (drawDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (string.IsNullOrWhiteSpace(drawingId)) throw new ArgumentNullException(nameof(drawingId));
            if (string.IsNullOrWhiteSpace(domainIds)) throw new ArgumentNullException(nameof(domainIds));

            var result = game.GetLottery().WagersPerPlayerInPendingDrawing(player, drawDate, drawingId, gameType, ticketNumber, domainIds);
            return result;
        }

        internal WagersPerPlayerInPendingDraw WagersPerPlayerInPendingDrawing(DateTime startDate, DateTime endDate, string drawingId, string accountNumber, string gameType, string ticketNumber, string domainIds)
        {
            if (startDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (endDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (string.IsNullOrWhiteSpace(drawingId)) throw new ArgumentNullException(nameof(drawingId));
            if (string.IsNullOrWhiteSpace(domainIds)) throw new ArgumentNullException(nameof(domainIds));

            var result = game.GetLottery().WagersPerPlayerInPendingDrawing(startDate, endDate, drawingId, accountNumber, gameType, ticketNumber, domainIds);
            return result;
        }

        internal IEnumerable<GradingPicksRecord> Grading(DateTime startDate, DateTime endDate, string uniqueDrawingId, string gameType)
        {
            if (startDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (endDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");

            var result = game.GetLottery().Grading(startDate, endDate, uniqueDrawingId, gameType);
            return result;
        }
    }
}
