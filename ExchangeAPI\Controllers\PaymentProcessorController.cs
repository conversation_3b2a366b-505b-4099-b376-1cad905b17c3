﻿using GamesEngine;
using GamesEngine.Business;
using GamesEngine.Exchange;
using GamesEngine.Finance;
using GamesEngine.Settings;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;
using town.connectors.drivers;
using static town.connectors.CustomSettings.CustomSetting;

namespace ExchangeAPI.Controllers
{
	public class PaymentProcessorController : Controller
	{
		[HttpGet("api/settings/processors")]
        [Authorize(Roles = "devops")]//VERIFY_ROLES
        public async Task<IActionResult> CatalogsAsync()
		{
			var result = await ExchangeManagerAPI.ExchangeManager.PerformQryAsync(HttpContext, $@"
            {{
				for (entitiesList:company.System.Entities)
                {{
                    entity = entitiesList;
					print entity.Id id;
                    print entity.Name name;
                }}
				for (paymentMethods:company.System.PaymentMethods)
                {{
                    paymentMethod = paymentMethods;
					print paymentMethod.Id id;
                    print paymentMethod.Name name;
                }}
                for (transactionTypes:company.System.TransactionTypes)
                {{
                    type = transactionTypes;
					print type.Id id;
                    print type.Name name;
                }}
				for (coins:company.System.Coins)
                {{
                    coin = coins;
					print coin.Id id;
                    print coin.Name name;
					print coin.Iso4217Code currencyCode;
                }}

				processorsEntities = company.System.PaymentProcessor.Entities();
				for (entities:processorsEntities)
                {{
                    entity = entities;
					print entity.Id id;
                    print entity.Name entity;
					processorsMethods = company.System.PaymentProcessor.PaymentMethods(entity);
					for (methods:processorsMethods)
					{{
						paymentMethod = methods;
						print paymentMethod.Id id;
						print paymentMethod.Name method;
						processorsTransactions = company.System.PaymentProcessor.TransactionTypes(entity, paymentMethod);
						for (types:processorsTransactions)
						{{
							type = types;
							print type.Id id;
							print type.Name type;
							processorsCoins = company.System.PaymentProcessor.Coins(entity, paymentMethod, type);
							for (coins:processorsCoins)
							{{
								coin = coins;
								print coin.Id id;
								print coin.Name name;
								print coin.Iso4217Code currencyCode;
							}}
						}}
					}}
                }}
            }}
			");
			return result;
		}

		[HttpPut("api/settings/processors/alias")]
        [Authorize(Roles = "devops")]//VERIFY_ROLES
        public async Task<IActionResult> EnableProcessorAsync([FromBody] ProcessorAliasUpdateBody body)
		{
			if (body == null) return BadRequest($"Body is required");
			if (body.EntityId <= 0) return BadRequest($"{nameof(body.EntityId)} must be greater than 0");
			if (body.MethodId <= 0) return BadRequest($"{nameof(body.MethodId)} must be greater than 0");
			if (body.TypeId <= 0) return BadRequest($"{nameof(body.TypeId)} must be greater than 0");
			if (body.CoinId < 0) return BadRequest($"{nameof(body.CoinId)} must be greater than 0");
			if (string.IsNullOrWhiteSpace(body.Alias)) return BadRequest($"{nameof(body.Alias)} is required");

			var result = await ExchangeManagerAPI.ExchangeManager.PerformChkThenCmdAsync(HttpContext, $@"
				exists = company.System.Entities.Exists({body.EntityId});
				Check(exists) Error 'Entity {body.EntityId} does not exist';
				exists = company.System.PaymentMethods.Exists({body.MethodId});
				Check(exists) Error 'Payment method {body.MethodId} does not exist';
				exists = company.System.TransactionTypes.Exists({body.TypeId});
				Check(exists) Error 'Transaction type {body.TypeId} does not exist';
				exists = company.System.Coins.Exists({body.CoinId});
				Check(exists) Error 'Coin {body.CoinId} does not exist';
			",
			$@"
            {{
				company.System.PaymentProcessor.UpdateAlias({body.EntityId}, {body.MethodId}, {body.TypeId}, {body.CoinId}, '{body.Alias}');
            }}
			");
			return result;
		}

		[HttpGet("api/settings/processor")]
        [Authorize(Roles = "devops")]//VERIFY_ROLES
        public async Task<IActionResult> ProcessorAsync(int entityId, int methodId, int typeId, int coinId)
		{
			if (entityId <= 0) return BadRequest($"{nameof(entityId)} must be greater than 0");
			if (methodId <= 0) return BadRequest($"{nameof(methodId)} must be greater than 0");
			if (typeId <= 0) return BadRequest($"{nameof(typeId)} must be greater than 0");
			if (coinId < 0) return BadRequest($"{nameof(coinId)} must be greater than 0");

			var result = await ExchangeManagerAPI.ExchangeManager.PerformQryAsync(HttpContext, $@"
            {{
				processorsList = company.System.PaymentProcessor.SearchProcessors({entityId}, {methodId}, {typeId}, {coinId});
				for (processors:processorsList)
				{{
					processor = processors;
					print processor.Id processorId;
					print processor.Entity.Name entity;
					print processor.Group.Name method;
					print processor.CurrencyIso4217Code currencyCode;
					print processor.Alias alias;
					print processor.Description description;
					print processor.Version version;
					print processor.Enabled enabled;
					for (types:processor.Transactions)
					{{
						print types.Name type;
					}}
				}}
            }}
			");
			return result;
		}

		[HttpGet("api/settings/processors/customVariables")]
        [Authorize(Roles = "devops")]//VERIFY_ROLES
        public async Task<IActionResult> CustomVariablesForProcessorAsync(int entityId, int methodId, int typeId, int coinId)
		{
			if (entityId <= 0) return BadRequest($"{nameof(entityId)} must be greater than 0");
			if (methodId <= 0) return BadRequest($"{nameof(methodId)} must be greater than 0");
			if (typeId <= 0) return BadRequest($"{nameof(typeId)} must be greater than 0");
			if (coinId < 0) return BadRequest($"{nameof(coinId)} must be greater than 0");

			var result = await ExchangeManagerAPI.ExchangeManager.PerformQryAsync(HttpContext, $@"
            {{
				processor = company.System.PaymentProcessor.SearchProcessorWithHigherVersion({entityId}, {methodId}, {typeId}, {coinId});
				for (settings:processor.CustomSettings.CustomSettings)
                {{
                    setting = settings;
					isFixed = ! setting.IsVariable;
					print isFixed isFixed;
					if (isFixed)
					{{
						customSettings.Get(Now, setting.Key);
					}}
					
					if (setting.Type == '{nameof(Secret)}')
					{{
						print '***' value;
					}}
					else 
					{{
						print setting.AsString value;
					}}
					
                    print setting.Key variable;
					print setting.Type type;
					if (isFixed)
					{{
						print setting.StartDate from;
						print setting.NextDateToChange to;
					}}
					print setting.Description description;
					print setting.Enabled enabled;
					if (setting.HasScheduledChange)
					{{
						print setting.NextValue newValue;
					}}

					lastEntries = setting.Log.LastEntries(5);
					for (log:lastEntries)
					{{
						print log.DateFormattedAsText date;
						print log.Who who;
						print log.Message message;
					}}
                }}
            }}
            ");
			return result;
		}

		[HttpPut("api/settings/processors/customVariables")]
        [Authorize(Roles = "devops")]//VERIFY_ROLES
        public async Task<IActionResult> UpdateCustomVariablesForProcessorAsync(int entityId, int methodId, int typeId, int coinId, [FromBody] NewCustomVariablesBody body)
		{
			if (body == null) return BadRequest($"Body is required");
			if (entityId <= 0) return BadRequest($"{nameof(entityId)} must be greater than 0");
			if (methodId <= 0) return BadRequest($"{nameof(methodId)} must be greater than 0");
			if (typeId <= 0) return BadRequest($"{nameof(typeId)} must be greater than 0");
			if (coinId < 0) return BadRequest($"{nameof(coinId)} must be greater than 0");

			string employeeName = Security.UserName(HttpContext);
			var scriptToValidateVariables = new StringBuilder();
			var scriptToUpdateVariables = new StringBuilder();
			foreach (var customVariable in body.CustomVariables)
            {
				if (string.IsNullOrWhiteSpace(customVariable.Key)) return BadRequest($"{nameof(customVariable.Key)} is required");
				if (string.IsNullOrWhiteSpace(customVariable.Type)) return BadRequest($"{nameof(customVariable.Type)} is required");
				if (!customVariable.Enabled.HasValue && string.IsNullOrWhiteSpace(customVariable.NewValue) && string.IsNullOrWhiteSpace(customVariable.Description)) return BadRequest($"{nameof(customVariable.NewValue)} or {nameof(customVariable.Description)} or {nameof(customVariable.Enabled)} is required");

				var key = Validator.StringEscape(customVariable.Key);
				var newValue = string.IsNullOrWhiteSpace(customVariable.NewValue) ? string.Empty : Validator.StringEscape(customVariable.NewValue);
				var description = string.IsNullOrWhiteSpace(customVariable.Description) ? string.Empty : Validator.StringEscape(customVariable.Description);
				var fromAsText = string.Empty;
				if (customVariable.NewFrom.HasValue)
				{
					var date = customVariable.NewFrom.Value;
					fromAsText = $"{date.Month}/{date.Day}/{date.Year} {date.Hour}:{date.Minute}:{date.Second}";
				}
				if (!string.IsNullOrWhiteSpace(newValue))
                {
					scriptToValidateVariables.AppendLine($"processor = company.System.PaymentProcessor.SearchProcessorWithHigherVersion({entityId}, {methodId}, {typeId}, {coinId});");
					scriptToValidateVariables.AppendLine($"Check(processor.CustomSettings.IsValidNextDateToChange(Now, {fromAsText})) Error 'Current date time is greater than {fromAsText}.';");
					if (string.Equals(customVariable.Type, $"{nameof(String)}", StringComparison.OrdinalIgnoreCase))
					{
                        //Driver: TODO ver la sobrecarga ChangeValueStartingOn para Now
                        scriptToUpdateVariables.AppendLine($"cs = processor.CustomSettings.ChangeValueStartingOn(itIsThePresent, {fromAsText}, '{key}', '{newValue}', '{employeeName}');");
						scriptToUpdateVariables.AppendLine($"cs.AddAnnotation('a new value', '{employeeName}', Now, {fromAsText});");
					}
					else if (string.Equals(customVariable.Type, $"{nameof(Secret)}", StringComparison.OrdinalIgnoreCase))
					{
						scriptToUpdateVariables.AppendLine($"cs = processor.CustomSettings.ChangeSecretStartingOn(itIsThePresent, {fromAsText}, '{key}', '{newValue}', '{employeeName}');");
						scriptToUpdateVariables.AppendLine($"cs.AddAnnotation('a new value', '{employeeName}', Now, {fromAsText});");
					}
					else
					{
                        //Driver: TODO ver la sobrecarga ChangeValueStartingOn para Now
						scriptToUpdateVariables.AppendLine($@"cs = processor.CustomSettings.ChangeValueStartingOn(itIsThePresent, {fromAsText}, '{key}', {newValue}, '{employeeName}');");
						scriptToUpdateVariables.AppendLine($"cs.AddAnnotation('a new value', '{employeeName}', Now, {fromAsText});");
					}
				}

				if (!string.IsNullOrWhiteSpace(description))
				{
					scriptToUpdateVariables.AppendLine($"cs = processor.CustomSettings.UpdateDescription(itIsThePresent, '{key}', '{description}');");
					scriptToUpdateVariables.AppendLine($"cs.AddAnnotation('a new description', '{employeeName}', Now);");
				}
				if (customVariable.Enabled.HasValue)
				{
					if (customVariable.Enabled.Value)
                    {
						scriptToUpdateVariables.AppendLine($"cs = processor.CustomSettings.Enable(itIsThePresent, '{key}');");
						scriptToUpdateVariables.AppendLine($"cs.AddAnnotation('as enabled', '{employeeName}', Now);");
					}
                    else
                    {
						scriptToUpdateVariables.AppendLine($"cs = processor.CustomSettings.Disable(itIsThePresent, '{key}');");
						scriptToUpdateVariables.AppendLine($"cs.AddAnnotation('as disabled', '{employeeName}', Now);");
					}
				}
			}
			
			var result = await ExchangeManagerAPI.ExchangeManager.PerformChkThenCmdAsync(HttpContext, $@"
				exists = company.System.Entities.Exists({entityId});
				Check(exists) Error 'Entity {entityId} does not exist';
				exists = company.System.PaymentMethods.Exists({methodId});
				Check(exists) Error 'Payment method {methodId} does not exist';
				exists = company.System.TransactionTypes.Exists({typeId});
				Check(exists) Error 'Transaction type {typeId} does not exist';
				exists = company.System.Coins.Exists({coinId});
				Check(exists) Error 'Coin {coinId} does not exist';
				{scriptToValidateVariables}
			", $@"
            {{
				processor = company.System.PaymentProcessor.SearchProcessorWithHigherVersion({entityId}, {methodId}, {typeId}, {coinId});
				{scriptToUpdateVariables}
            }}
            ");

			return result;
		}

		[HttpGet("api/settings/processors/prioritizations")]
        [Authorize(Roles = "devops")]//VERIFY_ROLES
        public async Task<IActionResult> PrioritizationsInProcessorsByCoinAsync()
		{
			var result = await ExchangeManagerAPI.ExchangeManager.PerformQryAsync(HttpContext, $@"
            {{
				for (coins:company.System.PaymentProcessor.Coins())
                {{
                    coin = coins;
					print coin.Id id;
                    print coin.Name currencyName;
					print coin.Iso4217Code currencyCode;
					for (entitiesForCredit:company.System.PaymentProcessor.EntitiesPerformingDeposit(coin))
					{{
						entity = entitiesForCredit;
						print 1 transactionTypeId;
						print entity.Id id;
						print entity.Name name;
					}}
					for (entitiesForDebit:company.System.PaymentProcessor.EntitiesPerformingWithdrawal(coin))
					{{
						entity = entitiesForDebit;
						print 2 transactionTypeId;
						print entity.Id id;
						print entity.Name name;
					}}
                }}
            }}
            ");
			return result;
		}

		[HttpGet("api/settings/processors/cascade")]
        [Authorize(Roles = "devops")]//VERIFY_ROLES
        public async Task<IActionResult> EntitiesInCascadeAsync(int typeId, int coinId)
		{
			var result = await ExchangeManagerAPI.ExchangeManager.PerformQryAsync(HttpContext, $@"
            {{
				transactionType = company.System.TransactionTypes.Find({typeId});
				coin = company.System.Coins.Find({coinId});
				for (entities:company.System.PaymentProcessor.Entities(transactionType, coin))
				{{
					entity = entities;
					print entity.Id id;
					print entity.Name name;
				}}
            }}
            ");
			return result;
		}

		[HttpPost("api/settings/processors/cascade")]
        [Authorize(Roles = "devops")]//VERIFY_ROLES
        public async Task<IActionResult> ReorderEntitiesInCascadeAsync([FromBody] EntitiesToReorderBody body)
		{
			if (body == null) return BadRequest($"Body is required");
			if (body.TypeId <= 0) return BadRequest($"{nameof(body.TypeId)} must be greater than 0");
			if (body.CoinId < 0) return BadRequest($"{nameof(body.CoinId)} must be greater than 0");

			var entityIdsToReorder = string.Join(',', body.EntityIds);
			var result = await ExchangeManagerAPI.ExchangeManager.PerformCmdAsync(HttpContext, $@"
            {{
				transactionType = company.System.TransactionTypes.Find({body.TypeId});
				coin = company.System.Coins.Find({body.CoinId});
				company.System.PaymentProcessor.ReorderEntities(transactionType, coin, {{{entityIdsToReorder}}});
            }}
            ");
			return result;
		}

		[HttpPost("api/coin")]
        [Authorize(Roles = "devops")]//VERIFY_ROLES
        public async Task<IActionResult> CreateCoinAsync([FromBody] CoinBody body)
		{
			if (body == null) return BadRequest($"Body is required");
			if (string.IsNullOrWhiteSpace(body.Iso4217Code)) return BadRequest($"{nameof(body.Iso4217Code)} is required");
			if (string.IsNullOrWhiteSpace(body.Sign)) return BadRequest($"{nameof(body.Sign)} is required");
			if (string.IsNullOrWhiteSpace(body.Unicode)) return BadRequest($"{nameof(body.Unicode)} is required");
			if (string.IsNullOrWhiteSpace(body.Name)) return BadRequest($"{nameof(body.Name)} is required");
			if (string.IsNullOrWhiteSpace(body.Type)) return BadRequest($"{nameof(body.Type)} is required");
			if (!Enum.TryParse(body.Type, out CoinType type)) return BadRequest($"{nameof(body.Type)} is not a {nameof(CoinType)}");
			if (body.DecimalPrecision < 0) return BadRequest($"{nameof(body.DecimalPrecision)} must be greater than 0");

			var result = await ExchangeManagerAPI.ExchangeManager.PerformChkThenCmdAsync(HttpContext, $@"
				Check(!company.System.Coins.Exists('{body.Name}')) Error 'There is already a coin using name {body.Name}';
				Check(!company.System.Coins.ExistsIsoCode('{body.Iso4217Code}')) Error 'There is already a coin using code {body.Iso4217Code}';
			",
			$@"
            {{
				Eval('coinId = ' + company.System.Coins.NextConsecutive() + ';');
                company.System.Coins.Add(itIsThePresent, coinId, '{body.Iso4217Code}', '{body.Sign}', {body.DecimalPrecision}, '{body.Unicode}', '{body.Name}', {body.Type});
            }}
            ");
			return result;
		}

		[HttpGet("api/coins")]
        [Authorize(Roles = "devops")]//VERIFY_ROLES
        public async Task<IActionResult> CoinsByTransactionTypeAsync(string transactionType)
		{
			if (string.IsNullOrWhiteSpace(transactionType)) return BadRequest($"{nameof(transactionType)} is required");

			var result = await ExchangeManagerAPI.ExchangeManager.PerformQryAsync(HttpContext, $@"
            {{
				transactionType = company.System.TransactionTypes.Find('{transactionType}');
				processorsCoins = company.System.PaymentProcessor.Coins(transactionType);
				for (coins:processorsCoins)
				{{
					coin = coins;
					print coin.Id id;
					print coin.Name name;
					print coin.Iso4217Code currencyCode;
				}}
            }}
            ");
			return result;
		}

		[HttpPost("api/entity")]
        [Authorize(Roles = "devops")]//VERIFY_ROLES
        public async Task<IActionResult> CreateEntityAsync([FromBody] EntityBody body)
		{
			if (body == null) return BadRequest($"Body is required");

			var result = await ExchangeManagerAPI.ExchangeManager.PerformCmdAsync(HttpContext, $@"
            {{
                company.System.Entities.Add(itIsThePresent, '{body.Name}');
            }}
            ");
			return result;
		}

		[HttpPost("api/paymentMethod")]
        [Authorize(Roles = "devops")]//VERIFY_ROLES
        public async Task<IActionResult> CreatePaymentMethodAsync([FromBody] PaymentMethodBody body)
		{
			if (body == null) return BadRequest($"Body is required");

			var result = await ExchangeManagerAPI.ExchangeManager.PerformCmdAsync(HttpContext, $@"
            {{
                company.System.PaymentMethods.Add(itIsThePresent, '{body.Name}');
            }}
            ");
			return result;
		}

		[HttpGet("api/paymentMethods/deposit")]
        [Authorize(Roles = "devops")]//VERIFY_ROLES
        public async Task<IActionResult> PaymentMethodsPerDomainAndDepositAsync()
		{
			var transactionType = TransactionType.Deposit.ToString();
			return await PaymentMethodsPerDomainWithProvidersAsync(transactionType);
		}
		[HttpGet("api/paymentMethods/withdrawal")]
        [Authorize(Roles = "devops")]//VERIFY_ROLES
        public async Task<IActionResult> PaymentMethodsPerDomainAndWithdrawalAsync()
		{
			var transactionType = TransactionType.Withdrawal.ToString();
			return await PaymentMethodsPerDomainWithProvidersAsync(transactionType);
		}

		async Task<IActionResult> PaymentMethodsPerDomainWithProvidersAsync(string transactionType)
		{
			if (string.IsNullOrWhiteSpace(transactionType)) return BadRequest($"{nameof(transactionType)} is required");

			var result = await ExchangeManagerAPI.ExchangeManager.PerformQryAsync(HttpContext, $@"
            {{
				transactionType = company.System.TransactionTypes.Find('{transactionType}');
				processorsEntities = company.System.PaymentProcessor.EntitiesWithProviders(transactionType);
				for (entities:processorsEntities)
                {{
                    entity = entities;
					print entity.Id id;
                    print entity.Name entity;
					processorsMethods = company.System.PaymentProcessor.PaymentMethodsWithProviders(entity, transactionType);
					for (methods:processorsMethods)
					{{
						paymentMethod = methods;
						print paymentMethod.Id id;
						print paymentMethod.Name method;
						if (paymentMethod.HasProviders())
						{{
							for (providers:paymentMethod.Providers)
							{{
								provider = providers;
								print provider.Id id;
								print provider.Name name;
					
							}}
						}}
						processorsCoins = company.System.PaymentProcessor.Coins(entity, paymentMethod, transactionType);
						for (coins:processorsCoins)
						{{
							coin = coins;
							print coin.Id id;
							print coin.Name name;
							print coin.Iso4217Code currencyCode;
							print coin.DecimalPrecision decimalPrecision;
						}}
					}}
				}}
            }}
            ");
			return result;
		}

		[HttpGet("api/window/paymentMethods/creditNote")]
        [Authorize(Roles = "devops")]//VERIFY_ROLES
        public async Task<IActionResult> PaymentMethodsInCreditNoteForWindowAsync()
		{
			var transactionType = TransactionType.CreditNote.ToString();
			return await PaymentMethodsForWindowAsync(transactionType);
		}
		[HttpGet("api/window/paymentMethods/debitNote")]
        [Authorize(Roles = "devops")]//VERIFY_ROLES
        public async Task<IActionResult> PaymentMethodsInDebitNoteForWindowAsync()
		{
			var transactionType = TransactionType.DebitNote.ToString();
			return await PaymentMethodsForWindowAsync(transactionType);
		}

		[HttpGet("api/window/paymentMethods/deposit")]
        [Authorize(Roles = "devops")]//VERIFY_ROLES
        public async Task<IActionResult> PaymentMethodsInDepositForWindowAsync()
		{
			var transactionType = TransactionType.Deposit.ToString();
			return await PaymentMethodsForWindowAsync(transactionType);
		}
		[HttpGet("api/window/paymentMethods/withdrawal")]
        [Authorize(Roles = "devops")]//VERIFY_ROLES
        public async Task<IActionResult> PaymentMethodsInWithdrawalForWindowAsync()
		{
			var transactionType = TransactionType.Withdrawal.ToString();
			return await PaymentMethodsForWindowAsync(transactionType);
		}
		[HttpGet("api/window/paymentMethods/transfer")]
        [Authorize(Roles = "devops")]//VERIFY_ROLES
        public async Task<IActionResult> PaymentMethodsInTransferForWindowAsync()
		{
			var transactionType = TransactionType.Transfer.ToString();
			return await PaymentMethodsForWindowAsync(transactionType);
		}

		async Task<IActionResult> PaymentMethodsForWindowAsync(string transactionType)
		{
			if (string.IsNullOrWhiteSpace(transactionType)) return BadRequest($"{nameof(transactionType)} is required");

			var result = await ExchangeManagerAPI.ExchangeManager.PerformQryAsync(HttpContext, $@"
            {{
				transactionType = company.System.TransactionTypes.Find('{transactionType}');
				processorsEntities = company.System.PaymentProcessor.EntitiesForCashAndCreditCard(transactionType);
				for (entities:processorsEntities)
                {{
                    entity = entities;
					print entity.Id id;
                    print entity.Name entity;
					processorsMethods = company.System.PaymentProcessor.PaymentMethodsForCashAndCreditCard(entity, transactionType);
					for (methods:processorsMethods)
					{{
						paymentMethod = methods;
						print paymentMethod.Id id;
						print paymentMethod.Name method;
						processorsCoins = company.System.PaymentProcessor.Coins(entity, paymentMethod, transactionType);
						for (coins:processorsCoins)
						{{
							coin = coins;
							print coin.Id id;
							print coin.Name name;
							print coin.Iso4217Code currencyCode;
							print coin.DecimalPrecision decimalPrecision;
						}}
					}}
				}}
            }}
            ");
			return result;
		}

		[HttpPost("api/transactionType")]
        [Authorize(Roles = "devops")]//VERIFY_ROLES
        public async Task<IActionResult> CreateTransactionTypeAsync([FromBody] TransactionTypeBody body)
		{
			if (body == null) return BadRequest($"Body is required");

			var result = await ExchangeManagerAPI.ExchangeManager.PerformCmdAsync(HttpContext, $@"
            {{
                transactionType = company.System.TransactionTypes.Add(itIsThePresent, '{body.Name}');
				transactionType.Description = '{body.Description}';
            }}
            ");
			return result;
		}

		[HttpGet("api/transactionTypes")]
        [Authorize(Roles = "devops")]//VERIFY_ROLES
        public async Task<IActionResult> TransactionTypesAsync()
		{
			var result = await ExchangeManagerAPI.ExchangeManager.PerformQryAsync(HttpContext, $@"
            {{
                for (transactionTypes:company.System.TransactionTypes)
                {{
                    type = transactionTypes;
					print type.Id id;
                    print type.Name name;
					print type.Description description;
                }}
            }}
            ");
			return result;
		}

		[HttpGet("api/tenants")]
        [Authorize(Roles = "devops")]//VERIFY_ROLES
        public async Task<IActionResult> TenantsAsync()
		{
			var result = await ExchangeManagerAPI.ExchangeManager.PerformQryAsync(HttpContext, $@"
            {{
                for (tenants:company.System.Tenants)
                {{
                    tenant = tenants;
					print tenant.Id id;
                    print tenant.Name name;
                }}
            }}
            ");
			return result;
		}

	}
	
	[DataContract(Name = "NewCustomVariables")]
	public class NewCustomVariablesBody
	{
		[DataMember(Name = "customVariables")]
		public List<NewCustomVariable> CustomVariables { get; set; }
	}

	[DataContract(Name = "NewCustomVariable")]
	public class NewCustomVariable
	{
		[DataMember(Name = "key")]
		public string Key { get; set; }
		[DataMember(Name = "newValue")]
		public string NewValue { get; set; }
		[DataMember(Name = "type")]
		public string Type { get; set; }
		[DataMember(Name = "newFrom")]
		public DateTime? NewFrom { get; set; }
		[DataMember(Name = "description")]
		public string Description { get; set; }
		[DataMember(Name = "enabled")]
		public bool? Enabled { get; set; }
	}

	[DataContract(Name = "EntitiesToReorderBody")]
	public class EntitiesToReorderBody
	{
		[DataMember(Name = "entityIds")]
		public List<int> EntityIds { get; set; }
		[DataMember(Name = "typeId")]
		public int TypeId { get; set; }
		[DataMember(Name = "coinId")]
		public int CoinId { get; set; }
	}

	[DataContract(Name = "CatalogBody")]
	public abstract class CatalogBody
	{
		[DataMember(Name = "name")]
		public string Name { get; set; }
	}

	[DataContract(Name = "CoinBody")]
	public class CoinBody : CatalogBody
	{
		[DataMember(Name = "decimalPrecision")]
		public int DecimalPrecision { get; set; }
		[DataMember(Name = "sign")]
		public string Sign { get; set; }
		[DataMember(Name = "isoCode")]
		public string Iso4217Code { get; set; }
		[DataMember(Name = "unicode")]
		public string Unicode { get; set; }
		[DataMember(Name = "type")]
		public string Type { get; set; }
	}

	[DataContract(Name = "EntityBody")]
	public class EntityBody : CatalogBody
	{
	}

	[DataContract(Name = "PaymentMethodBody")]
	public class PaymentMethodBody : CatalogBody
	{
	}

	[DataContract(Name = "TransactionTypeBody")]
	public class TransactionTypeBody : CatalogBody
	{
		[DataMember(Name = "description")]
		public string Description { get; set; }
	}

	[DataContract]
	public class ProcessorAliasUpdateBody
	{
		[DataMember(Name = "entityId")]
		public int EntityId { get; set; }

		[DataMember(Name = "methodId")]
		public int MethodId { get; set; }

		[DataMember(Name = "typeId")]
		public int TypeId { get; set; }

		[DataMember(Name = "coinId")]
		public int CoinId { get; set; }

		[DataMember(Name = "alias")]
		public string Alias { get; set; }
	}
}