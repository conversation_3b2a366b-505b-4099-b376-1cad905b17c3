﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using GamesEngine.Business.Liquidity.Transactions;
using GamesEngine.Finance;
using GamesEngine.RealTime;
using GamesEngine.RealTime.Events;
using GamesEngine.Settings;
using static GamesEngine.Business.Liquidity.Containers.Tank;

namespace GamesEngine.Business.Liquidity.Containers
{
    public abstract class Tank : Container
    {
        protected IEnumerable<ProposedDeposit> deposits;
        private Jar jar;

        public Tank(int id, string name, DateTime createdAt, Jar jar, string kind, IEnumerable<ProposedDeposit> depositos) : base(kind)
        {
            if (jar == null) throw new GameEngineException("The Jar is null.");
            if (depositos == null || !depositos.Any()) throw new ArgumentException("Deposits cannot be null or empty.");
            if (string.IsNullOrEmpty(name)) throw new GameEngineException("The name cannot be null or empty.");
            if(createdAt == DateTime.MinValue) throw new GameEngineException("The createdAt cannot be MinValue.");

            this.Id = id;
            this.Name = name;
            this.jar = jar;
            this.deposits = depositos;
            this.CreatedAt = createdAt;

            foreach (var deposit in deposits)
            {
                if (deposit == null) throw new GameEngineException("The Deposit is null.");
                if (deposit.Amount <= 0) throw new GameEngineException("The Deposit amount is zero or negative.");
                Amount += deposit.Amount;
            }

        }

        public string Type => this.GetType().Name;

        public Source Source => this.jar.Source;

        public int Id { get; private set; }

        public string Name { get; set; }

        public int Version => this.jar.Version;

        public DateTime CreatedAt { get; set; }

        public IEnumerable<ProposedDeposit> Deposits => deposits;
       

        public class TankReady : Tank
        {
            public TankReady(int id, string name, DateTime createdAt, Jar jar, string kind, IEnumerable<ProposedDeposit> deposits) : base(id, name, createdAt, jar, kind, deposits) { }

            public TankReady MergeWith(int draftTankId, DateTime createdAt, TankReady otherTank)
            {
                if (otherTank == null) throw new ArgumentException("Cannot merge with null tank.");
                if (otherTank == this) throw new ArgumentException("Cannot merge with the same tank.");
                if (otherTank.Deposits.Count() == 0) throw new ArgumentException("Cannot merge with an empty tank.");

                string name = "Merged Tank " + draftTankId;
                IEnumerable<ProposedDeposit> mergedDeposits = new List<ProposedDeposit>(this.Deposits)
                    .Concat(otherTank.Deposits)
                    .Distinct()
                    .ToList();
                var result = new TankReady(draftTankId, name, createdAt, this.jar, Kind, mergedDeposits);//Rubicon Todo: Revisar que jar version debe darle origen al merged tank
                if (result.Amount != this.Amount + otherTank.Amount) throw new GameEngineException("The amount of the merged tank is not correct.");
                Source.AddOrUpdateTank(result);

                bool itIsThePresent = true; //Rubicon: Revisar si es necesario el now o se puede usar el createdAt
                if (Integration.UseKafka)
                {

                    using (KafkaMessagesBuffer buffer = new KafkaMessagesBuffer(itIsThePresent, $"{Integration.Kafka.TopicForContainerEvents}"))
                    {
                        TankMergedMessage tankMergedMessage = new TankMergedMessage(
                            tankId: result.Id,
                            kind: result.Kind,
                            description: $"Created Merged Tank {result.Name} - Jar version ${result.Version}",
                            jarVersion: result.Version,
                            createdAt: createdAt,
                            depositIds: result.Deposits.Select(d => d.Id).ToList()
                        );
                        buffer.Send(tankMergedMessage);
                    }
                }

                if (itIsThePresent)
                {
                    CreatedTankEvent createdTankEvent = new CreatedTankEvent(createdAt, result.Id, result.Name);
                    PlatformMonitor.GetInstance().WhenNewEvent(createdTankEvent);
                }

                TankDiscarded thisTankDiscarded = new TankDiscarded(this);
                Source.AddOrUpdateTank(thisTankDiscarded);
                TankDiscarded tankDiscarded = new TankDiscarded(otherTank);
                Source.AddOrUpdateTank(tankDiscarded);
                return result;
            }

            public TankReady MergeWith(TankReady tankReady)
            {
                if (tankReady == null) throw new ArgumentException("Cannot merge with null tank.");
                if (tankReady.Deposits.Count() == 0) throw new ArgumentException("Cannot merge with an empty tank.");

                IEnumerable<ProposedDeposit> mergedDeposits = new List<ProposedDeposit>(this.Deposits)
                .Concat(tankReady.Deposits)
                .Distinct()
                .ToList();

                this.deposits = mergedDeposits;

                decimal newAmount = 0;
                foreach (var deposit in this.deposits)
                {
                    if (deposit == null) throw new GameEngineException("The Deposit is null.");
                    if (deposit.Amount <= 0) throw new GameEngineException("The Deposit amount is zero or negative.");
                    newAmount += deposit.Amount;
                }

                decimal actualMergeAmount = this.Amount + tankReady.Amount;
                if (newAmount != actualMergeAmount) throw new GameEngineException("The amount of the merged tank is not correct.");

                Amount = newAmount;

                TankDiscarded tankDiscarded = new TankDiscarded(tankReady);
                Source.AddOrUpdateTank(tankDiscarded);
                return this;
            }
        }

        public class TankDiscarded : Tank
        {
            public TankDiscarded(TankReady tankReady) : base(tankReady.Id, tankReady.Name, tankReady.CreatedAt, tankReady.jar, tankReady.Kind, tankReady.Deposits) { }
        }

        public class TankDispatched : Tank
        {
            public TankDispatched(TankReady thankReady) : base(thankReady.Id, thankReady.Name, thankReady.CreatedAt, thankReady.jar, thankReady.Kind, thankReady.Deposits) { }

        }
    }

    //public abstract class Tank : Container
    //{
    //    private readonly List<Deposit> _deposits;

    //    protected Tank(string kind, IEnumerable<Deposit> deposits) : base(kind)
    //    {
    //        if (deposits == null || !deposits.Any())
    //            throw new ArgumentException("Deposits cannot be null or empty.");

    //        _deposits = deposits.ToList();
    //    }

    //    public int Id { get; } = new Random().Next(1, int.MaxValue); // Unique identifier
    //    public decimal Amount => _deposits.Sum(d => d.Amount);
    //    public bool IsSealed { get; private set; } = false;

    //    public Deposit GetDepositById(int id)
    //    {
    //        return _deposits.FirstOrDefault(d => d.Id == id) ?? throw new InvalidOperationException("Deposit not found.");
    //    }

    //    public decimal AvailableAmount()
    //    {
    //        return _deposits.Sum(d => d.Amount); // Assuming all deposits are available
    //    }

    //    public decimal BlockedAmount()
    //    {
    //        // Assuming blocked deposits are marked in some way
    //        return _deposits.Where(d => d.IsBlocked).Sum(d => d.Amount);
    //    }

    //    public void Seal(string txn, string txtDoc, DateTime dateTime)
    //    {
    //        if (_isSealed)
    //            throw new InvalidOperationException("Tank is already sealed.");

    //        if (string.IsNullOrWhiteSpace(txn) || string.IsNullOrWhiteSpace(txtDoc))
    //            throw new ArgumentException("Transaction details cannot be null or empty.");

    //        _isSealed = true;
    //        // Logic to update the associated Jar and create a LegacyJar
    //    }

    //    public abstract Tank Dispatch(string targetAddress, TimeSpan allowedTime);

    //    public Tank MergeWith(Tank otherTank)
    //    {
    //        if (otherTank == null || otherTank == this)
    //            throw new ArgumentException("Cannot merge with null or the same tank.");

    //        if (this.IsSealed || otherTank.IsSealed)
    //            throw new InvalidOperationException("Cannot merge sealed tanks.");

    //        var mergedDeposits = _deposits.Concat(otherTank._deposits).ToList();
    //        return new TankReady(this.Kind, mergedDeposits);
    //    }
    //}

    //internal class TankReady : Tank
    //{
    //    public TankReady(string kind, IEnumerable<Deposit> deposits) : base(kind, deposits) { }

    //    public override Tank Dispatch(string targetAddress, TimeSpan allowedTime)
    //    {
    //        if (string.IsNullOrWhiteSpace(targetAddress))
    //            throw new ArgumentException("Target address cannot be null or empty.");

    //        if (allowedTime <= TimeSpan.Zero)
    //            throw new ArgumentException("Allowed time must be greater than zero.");

    //        // Logic to calculate fees and prepare the dispatch
    //        return new TankDispatched(this.Kind, _deposits, targetAddress);
    //    }
    //}

    //internal class TankDispatched : Tank
    //{
    //    public string TargetAddress { get; }
    //    public DateTime DispatchedAt { get; }

    //    public TankDispatched(string kind, IEnumerable<Deposit> deposits, string targetAddress)
    //        : base(kind, deposits)
    //    {
    //        TargetAddress = targetAddress ?? throw new ArgumentException("Target address cannot be null.");
    //        DispatchedAt = DateTime.Now;
    //    }

    //    public override Tank Dispatch(string targetAddress, TimeSpan allowedTime)
    //    {
    //        throw new InvalidOperationException("Tank is already dispatched.");
    //    }
    //}
}
