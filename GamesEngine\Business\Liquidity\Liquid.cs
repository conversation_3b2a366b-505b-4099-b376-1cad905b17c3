﻿using GamesEngine.Business.Liquidity.Containers;
using GamesEngine.Business.Liquidity.Transactions;
using GamesEngine.RealTime;
using GamesEngine.RealTime.Events;
using GamesEngine.Settings;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;

namespace GamesEngine.Business.Liquidity
{
    public class Liquid : Objeto
    {
        private readonly List<Source> sources = new();
        private readonly Dictionary<int, Dispenser> _dispensers = new Dictionary<int, Dispenser>();

        internal Liquid(string kind, LiquidFlow parentFlow)
        {
            Kind = kind;
            sources.Add(new Source(this));

            ParentFlow = parentFlow;
        }

        internal string Kind { get; private set; } // Tipo de líquido/moneda
        internal LiquidFlow ParentFlow { get; private set; }

        internal IEnumerable<Source> Sources => sources;

        internal IEnumerable<Dispenser> Dispensers => _dispensers.Values;

        internal decimal Amount => sources.Sum(s => s.Amount);

        internal decimal AmountTo(DateTime moment)
        {
            throw new NotImplementedException();
        }

        public bool ExistXpub(string xpub)
        {
            if (string.IsNullOrWhiteSpace(xpub)) throw new GameEngineException(nameof(xpub));

            Source source = sources.FirstOrDefault(s => s.Kind == this.Kind);
            if (source == null) throw new ArgumentNullException(nameof(source));

            return source.ExsitXpub(xpub);
        }

        public Source AddSource(Xpub xpub)
        {
            if (xpub == null) throw new ArgumentNullException(nameof(xpub));

            bool existInOtherLiquid = ParentFlow.ExistXpub(xpub.Value);
            if (existInOtherLiquid) throw new GameEngineException($"The xpub: {xpub.Value} is already associated with an existing source in another liquid.");

            //Rubicon: Todo Verificar que ya no existe en todo el liquid system
            var source = sources.FirstOrDefault(s => s.Kind == this.Kind);
            if (source == null) throw new ArgumentNullException(nameof(source));

            source.AddXpub(xpub);
            return source;
        }

        public void RemoveSource(Xpub xpub)
        {
            throw new NotImplementedException();
            //var source = _sources.FirstOrDefault(s => s.Xpub.Value == xpub.Value);
            //if (source != null) _sources.Remove(source);
        }

        public PendingDeposit GenerateDeposit(bool itIsThePresent, DateTime now,int depositId)
        {
            var source = sources.FirstOrDefault(s => s.Kind == this.Kind);
            if (source == null) throw new ArgumentNullException(nameof(source));
            if (now == DateTime.MinValue) throw new ArgumentNullException(nameof(now), "Date cannot be empty.");
            if(depositId <= 0) throw new ArgumentException("Deposit ID must be greater than zero.", nameof(depositId));

            PendingDeposit deposit = source.RequestDeposit(itIsThePresent, now,depositId);
            return deposit;
        }

        public DispenserReady CreateDispenser(bool itIsThePresent, DateTime createdAt, int id, string name, string address, decimal amount, DateTime startDate)
        {
            if (createdAt == DateTime.MinValue) throw new GameEngineException("The CreatedAt date is invalid.");
            if (id <= 0) throw new GameEngineException("The Id is invalid.");
            if (string.IsNullOrWhiteSpace(name)) throw new GameEngineException("The Name is null or empty.");
            if (string.IsNullOrWhiteSpace(address)) throw new GameEngineException("The Address is null or empty.");
            if (amount <= 0) throw new GameEngineException("The Amount is invalid.");
            if (startDate == DateTime.MinValue) throw new GameEngineException("The startDate is invalid.");

            if (amount > Amount) throw new GameEngineException("The amount is greater than the available amount.");

            var dispenser = new DispenserReady(id, name, this, this.Kind, address, amount, startDate);
            AddOrUpdateDispenser(dispenser);

            if (Integration.UseKafka)
            {

                using (KafkaMessagesBuffer buffer = new KafkaMessagesBuffer(itIsThePresent, $"{Integration.Kafka.TopicForContainerEvents}"))
                {
                    var createdDispenserMessage = new CreatedDispenserMessage(id, this.Kind, address, amount, createdAt);
                    buffer.Send(createdDispenserMessage);
                }
            }

            if (itIsThePresent)
            {
                CreatedDispenserEvent createdDispenserEvent = new CreatedDispenserEvent(createdAt, dispenser.Id, dispenser.Name);
                PlatformMonitor.GetInstance().WhenNewEvent(createdDispenserEvent);
            }
            return dispenser;
        }

        private int withdrawalConsecutive = 0;
        internal int NextWithdrawalId()
        {
            return withdrawalConsecutive + 1;
        }

        private int dispenserConsecutive = 0;
        internal int NextDispenserId()
        {
            return dispenserConsecutive + 1;
        }

        internal void AddOrUpdateDispenser(Dispenser dispenser)
        {
            if (dispenser == null) throw new ArgumentNullException(nameof(dispenser));
            if (_dispensers.TryGetValue(dispenser.Id, out Dispenser foundDispenser))
            {
                _dispensers[dispenser.Id] = dispenser;
            }
            else
            {
                _dispensers.Add(dispenser.Id, dispenser);
                dispenserConsecutive = dispenser.Id;
            }
        }
    }

}
