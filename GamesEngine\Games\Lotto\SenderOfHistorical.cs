﻿using GamesEngine.Business;
using GamesEngine.Finance;
using GamesEngine.Gameboards;
using GamesEngine.Gameboards.Lotto;
using GamesEngine.Location;
using GamesEngine.MessageQueuing;
using GamesEngine.Settings;
using GamesEngine.Time;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static GamesEngine.Bets.PlayerLottoReports;

namespace GamesEngine.Games.Lotto
{
    abstract class SenderOfHistorical : IDisposable
    {
        protected readonly Lottery lottery;
        protected readonly bool itIsThePresent;
        protected MessagesBuffer<TypedMessage> grading;

        internal SenderOfHistorical(Lottery lottery, bool itIsThePresent)
        {
            if (lottery == null) throw new ArgumentNullException(nameof(lottery));

            this.lottery = lottery;
            this.itIsThePresent = itIsThePresent;
        }

        abstract internal void WriteLoserData(DateTime date, Ticket ticket);

        abstract internal void WriteWinnerData(DateTime date, Ticket ticket);

        abstract internal void WriteNoActionData(DateTime date, Ticket ticket);

        public void Dispose()
        {
            this.grading.Flush();
        }
    }

    public enum GradeTicketType { LOSER_TICKET, WINNER_TICKET, NOACTION_TICKET, WINNER_RESULTS, DRAWINGS, DOMAINS, STARTING_TICKET, ENDING_TICKET, TEMP_TICKET };
    
    public sealed class TicketsStreamStartingMessage : GradeMessage
    {
        public string StateAbb { get; private set; }
        public DateTime DrawDate { get; private set; }
        public int DrawingId { get; private set; }
        public string GradedBy { get; private set; }
        public string Draw { get; private set; }
        internal int Fireball { get; private set; }
        public IdOfLottery IdPick { get; private set; }

        public TicketsStreamStartingMessage(GradeTicketType gradeType, string stateAbb, DateTime drawDate, IdOfLottery idOfLottery, int drawingId, string gradedBy, string draw, int fireball) : base(gradeType)
        {
            StateAbb = stateAbb;
            DrawDate = drawDate;
            IdPick = idOfLottery;
            DrawingId = drawingId;
            GradedBy = gradedBy;
            Draw = draw;
            Fireball = fireball;
        }

        public TicketsStreamStartingMessage(string message) : base(message)
        {

        }

        protected override void Deserialize(string[] serializatedMessage, out int fieldOrder)
        {
            base.Deserialize(serializatedMessage, out fieldOrder);
            StateAbb = serializatedMessage[fieldOrder++];
            var year = int.Parse(serializatedMessage[fieldOrder++]);
            var month = int.Parse(serializatedMessage[fieldOrder++]);
            var day = int.Parse(serializatedMessage[fieldOrder++]);
            var hour = int.Parse(serializatedMessage[fieldOrder++]);
            var minute = int.Parse(serializatedMessage[fieldOrder++]);
            var second = int.Parse(serializatedMessage[fieldOrder++]);
            DrawDate = new DateTime(year, month, day, hour, minute, second);
            if (!Enum.TryParse(serializatedMessage[fieldOrder++], out IdOfLottery idPick)) throw new GameEngineException($"{nameof(IdOfLottery)} '{serializatedMessage[fieldOrder]}' is not valid");
            IdPick = idPick;
            DrawingId = int.Parse(serializatedMessage[fieldOrder++]);
            GradedBy = serializatedMessage[fieldOrder++];
            Draw = serializatedMessage[fieldOrder++];
            Fireball = int.Parse(serializatedMessage[fieldOrder++]);
        }

        protected override void InternalSerialize()
        {
            base.InternalSerialize();
            AddProperty(StateAbb).
            AddProperty(DrawDate).
            AddProperty(IdPick.ToString()).
            AddProperty(DrawingId).
            AddProperty(GradedBy).
            AddProperty(Draw).
            AddProperty(Fireball);
        }
    }

    public sealed class TicketsStreamEndingMessage : GradeMessage
    {
        public string StateAbb { get; private set; }
        public DateTime DrawDate { get; private set; }
        public IdOfLottery IdPick { get; private set; }

        public TicketsStreamEndingMessage(GradeTicketType gradeType, string stateAbb, DateTime drawDate, IdOfLottery idOfLottery) : base(gradeType)
        {
            StateAbb = stateAbb;
            DrawDate = drawDate;
            IdPick = idOfLottery;
        }

        public TicketsStreamEndingMessage(string message) : base(message)
        {

        }

        protected override void Deserialize(string[] serializatedMessage, out int fieldOrder)
        {
            base.Deserialize(serializatedMessage, out fieldOrder);
            StateAbb = serializatedMessage[fieldOrder++];
            var year = int.Parse(serializatedMessage[fieldOrder++]);
            var month = int.Parse(serializatedMessage[fieldOrder++]);
            var day = int.Parse(serializatedMessage[fieldOrder++]);
            var hour = int.Parse(serializatedMessage[fieldOrder++]);
            var minute = int.Parse(serializatedMessage[fieldOrder++]);
            var second = int.Parse(serializatedMessage[fieldOrder++]);
            DrawDate = new DateTime(year, month, day, hour, minute, second);
            if (!Enum.TryParse(serializatedMessage[fieldOrder++], out IdOfLottery idPick)) throw new GameEngineException($"{nameof(IdOfLottery)} '{serializatedMessage[fieldOrder]}' is not valid");
            IdPick = idPick;
        }

        protected override void InternalSerialize()
        {
            base.InternalSerialize();
            AddProperty(StateAbb).
            AddProperty(DrawDate).
            AddProperty(IdPick.ToString());
        }
    }

    public abstract class TicketMessage : GradeMessage
    {
        internal int Hour { get; private protected set; }
        internal int Minute { get; private protected set; }
        internal int Year { get; private protected set; }
        internal int Month { get; private protected set; }
        internal int Day { get; private protected set; }
        internal string AccountNumber { get; private protected set; }
        internal string Ticket { get; private protected set; }
        internal decimal Amount { get; private protected set; }
        internal string AmountFormatted
        {
            get
            {
                var result = Currency.Factory(Coinage.GetById(CurrencyId), Amount).ToDisplayFormat();
                return result;
            }
        }
        internal GameboardStatus Action { get; private protected set; }
        internal int DrawingId { get; private protected set; }
        internal string GetIdOfLottery()
        {
            int mask = 1 << 27;
            if ((DrawingId & mask) != 0) return IdOfLottery.TZ.ToString();

            switch (TicketType)
            {
                case TicketType.P2S:
                case TicketType.P2B:
                    return IdOfLottery.P2.ToString();
                case TicketType.P3S:
                case TicketType.P3B:
                    return IdOfLottery.P3.ToString();
                case TicketType.P4S:
                case TicketType.P4B:
                    return IdOfLottery.P4.ToString();
                case TicketType.P5S:
                case TicketType.P5B:
                    return IdOfLottery.P5.ToString();
                default:
                    throw new GameEngineException($"Ticket type {TicketType} does not have {nameof(IdOfLottery)}");
            }
        }
        internal int UniqueDrawingId { get; private protected set; }
        internal TypeNumberSequence Position { get; private protected set; }
        internal string TypeNumberSequenceAsText() => Position.ToString();
        internal DateTime Creation { get; private protected set; }
        internal int OrderNumber { get; private protected set; }
        internal int TicketNumber { get; private protected set; }
        internal int PrizesVersion { get; private protected set; }
        internal int CurrencyId { get; set; }

        internal int DomainId { get; set; }
        internal string DomainUrl { get; private protected set; }


        internal Company Company { get; private protected set; }

        internal TicketMessage(GradeTicketType gradeType) : base(gradeType)
        {
        }

        public TicketMessage(string message) : base(message)
        {

        }

        internal TicketMessage(int hour, int minute, int year, int month, int day, string accountNumber, string ticket, decimal amount, GameboardStatus action, int drawingId, DateTime creation, int orderNumber, int ticketNumber, 
            int prizesVersion, int domainId, string domainUrl, int currencyId, GradeTicketType gradeType)
            : base(gradeType)
        {
            this.Hour = hour;
            this.Minute = minute;
            this.Year = year;
            this.Month = month;
            this.Day = day;
            AccountNumber = accountNumber;
            this.Ticket = ticket;
            this.Amount = amount;
            this.Action = action;
            this.DrawingId = drawingId;
            this.Creation = creation;
            OrderNumber = orderNumber;
            this.TicketNumber = ticketNumber;
            this.PrizesVersion = prizesVersion;
            DomainId = domainId;
            DomainUrl = domainUrl;
            CurrencyId = currencyId;
        }

        internal TicketType TicketType
        {
            get
            {
                var type = Ticket.Substring(0, 3);
                var converted = Enum.TryParse(type, out TicketType idOfType);
                if (!converted) throw new GameEngineException($"There is no type {type} for ticket");
                return idOfType;
            }
        }

        internal TicketTypeForReports TicketTypeForReports
        {
            get
            {
                var type = Ticket.Substring(0, 3);
                var converted = Enum.TryParse(type, out TicketTypeForReports idOfType);
                if (!converted) throw new GameEngineException($"There is no type {type} for ticket");
                return idOfType;
            }
        }

        abstract internal string GameType();

        abstract internal string GameTypeLottery();

        public DateTime DrawDate()
        {
            var date = new DateTime(Year, Month, Day, Hour, Minute, 0);
            return date;
        }

        abstract internal decimal TicketAmount();

        internal string GradingAsString()
        {
            return Action.ToString();
        }

        internal virtual void SetValuesFromTicket(Ticket ticket)
        {
            AccountNumber = ticket.Player.AccountNumber;
            Ticket = ticket.AsString();
            Amount = ticket.BetAmount();
            Action = ticket.Grading;
            Creation = ticket.CreationDate;
            OrderNumber = ticket.Order.Number;
            TicketNumber = ticket.TicketNumber;
            PrizesVersion = ticket.Prizes.VersionNumber;
            DomainId = ticket.DomainId;
        }

        internal void SetDate(int hour, int minute, int year, int month, int day)
        {
            Hour = hour;
            Minute = minute;
            Year = year;
            Month = month;
            Day = day;
        }
    }

    public class WinnerResultsInfo : GradeMessage
    {
        private string draw;
        private int drawingId;
        private int hour;
        private int minute;
        private int year;
        private int month;
        private int day;

        public WinnerResultsInfo(GradeTicketType gradeType, string draw, int fireball, int drawingId, int year, int month, int day, int hour, int minute) : base(gradeType)
        {
            this.draw = draw;
            Fireball = fireball;
            this.drawingId = drawingId;
            this.year = year;
            this.month = month;
            this.day = day;
            this.hour = hour;
            this.minute = minute;
        }

        public WinnerResultsInfo(string message) : base(message)
        {

        }

        public string Draw
        {
            get
            {
                return this.draw;
            }
        }

        internal int Fireball { get; private set; }

        public int DrawingId
        {
            get
            {
                return this.drawingId;
            }
        }

        public int Hour
        {
            get
            {
                return this.hour;
            }
        }

        public int Minute
        {
            get
            {
                return this.minute;
            }
        }

        public int Year
        {
            get
            {
                return this.year;
            }
        }

        public int Month
        {
            get
            {
                return this.month;
            }
        }

        public int Day
        {
            get
            {
                return this.day;
            }
        }

        protected override void InternalSerialize()
        {
            base.InternalSerialize();
            AddProperty(this.draw).
            AddProperty(Fireball).
            AddProperty(this.drawingId).
            AddProperty(this.Year).
            AddProperty(this.Month).
            AddProperty(this.Day).
            AddProperty(this.Hour).
            AddProperty(this.Minute);
        }

        protected override void Deserialize(string[] message, out int fieldOrder)
        {
            base.Deserialize(message, out fieldOrder);
            this.draw = message[fieldOrder++];
            Fireball = int.Parse(message[fieldOrder++]);
            this.drawingId = int.Parse(message[fieldOrder++]);
            this.year = int.Parse(message[fieldOrder++]);
            this.month = int.Parse(message[fieldOrder++]);
            this.day = int.Parse(message[fieldOrder++]);
            this.hour = int.Parse(message[fieldOrder++]);
            this.minute = int.Parse(message[fieldOrder++]);
        }
    }

	public sealed class DayToAccumulateMsg : KafkaMessage
    {
        public DateTime Day { get; private set; }

        public DayToAccumulateMsg(DateTime day)
        {
            Day = day;
        }

        public DayToAccumulateMsg(string message) : base(message)
        {
        }

        protected override void InternalSerialize()
        {
            base.InternalSerialize();
            AddProperty(Day);
        }

        protected override void Deserialize(string[] message, out int fieldOrder)
        {
            base.Deserialize(message, out fieldOrder);
            var year = int.Parse(message[fieldOrder++]);
            var month = int.Parse(message[fieldOrder++]);
            var day = int.Parse(message[fieldOrder++]);
            var hour = int.Parse(message[fieldOrder++]);
            var minute = int.Parse(message[fieldOrder++]);
            var second = int.Parse(message[fieldOrder++]);
            Day = new DateTime(year, month, day, hour, minute, second);
        }
    }

    public abstract class GradeMessage : TypedMessage
    {
        internal GradeMessage(GradeTicketType gradeType) : base((char)(int)gradeType)
        {
        }

        public GradeMessage(string message) : base(message)
        {

        }

        internal GradeTicketType GradeType
        {
            get
            {
                return (GradeTicketType)(int)base.MessageTypeSelector;
            }
        }

        protected override void InternalSerialize()
        {
            base.InternalSerialize();
        }

        protected override void Deserialize(string[] message, out int fieldOrder)
        {
            base.Deserialize(message, out fieldOrder);
        }
    }
}
