﻿using GamesEngine.RealTime.Events;
using GamesEngine.RealTime;
using GamesEngine.Settings;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using static GamesEngine.Business.Liquidity.Containers.Tank;
using GamesEngine.Business.Liquidity.Persistence;

namespace GamesEngine.Business.Liquidity.Containers
{
    public abstract class Tanker : Container
    {
        public Tanker(string kind, Source source) : base(kind)
        {
            if (source == null) throw new GameEngineException("The Source is null.");
            if (source.Jar == null) throw new GameEngineException("The Jar is null.");
            this.Source = source;
        }

        public Source Source { get; private set; }
        public string Type => this.GetType().Name;

        public int Id { get; protected set; }
        public string Name { get; set; }

        public abstract IEnumerable<Tank> Tanks { get; }

    }


    public class TankerPending : Tanker
    {
        private readonly IEnumerable<TankReady> tanksReady;

        public TankerPending(int tankerId, string name, string kind, Source source, IEnumerable<TankReady> tanksReady) : base(kind, source)
        {
            this.tanksReady = tanksReady;
            this.Name = name;
            this.Id = tankerId;

            foreach (var tankReady in this.tanksReady)
            {
                Amount += tankReady.Amount;
            }
        }

        public override IEnumerable<TankReady> Tanks =>  tanksReady;

        public void Add(TankReady tankReady)
        {
            if (tankReady == null) throw new GameEngineException("The TankReady is null.");
            if(tanksReady.Contains(tankReady)) throw new GameEngineException("The TankReady already exists in the TankerPending.");

            tanksReady.Append(tankReady);
            Amount += tankReady.Amount;
        }

        public TankerSealed Sealed()
        {
            if (tanksReady == null || !tanksReady.Any()) throw new GameEngineException("The TankerPending has no tanks ready.");

            var result = new TankerSealed(this);
            Source.AddOrUpdateTanker(result);
            return result;
        }

    }


    public class TankerSealed : Tanker
    {
        private TankerPending tankerPending;
        private readonly IEnumerable<TankDiscarded> tanksDiscarded;

        public TankerSealed(TankerPending tankerPending) : base(tankerPending.Kind, tankerPending.Source)
        {
            this.tankerPending = tankerPending;
            this.tanksDiscarded = new List<TankDiscarded>();
            this.Name = tankerPending.Name;
            this.Id = tankerPending.Id;
            this.Amount = tankerPending.Amount;
        }

        public override IEnumerable<TankDiscarded> Tanks => tanksDiscarded;

        public TankerDispatched Dispatched()
        {
            var result = new TankerDispatched(this);
            foreach (var tankReady in this.tankerPending.Tanks)
            {
                var tankDiscarded = new TankDiscarded(tankReady);
                Source.AddOrUpdateTank(tankDiscarded);
                this.tanksDiscarded.Append(tankDiscarded);
            }
            Source.AddOrUpdateTanker(result);
            
            return result;
        }
    }

    public class TankerDispatched : Tanker
    {
        private TankerSealed tankerSealed;


        public TankerDispatched(TankerSealed tankerSealed) : base(tankerSealed.Kind, tankerSealed.Source)
        {
            this.tankerSealed = tankerSealed;
        }

        public override IEnumerable<Tank> Tanks => tankerSealed.Tanks;

        public TankerArchived Archived()
        {
            var result = new TankerArchived(this);
            Source.AddOrUpdateTanker(result);
            return result;
        }
    }

    public class TankerArchived : Tanker
    {
        private TankerDispatched tankerDispached;

        public TankerArchived(TankerDispatched tankerDispached) : base(tankerDispached.Kind, tankerDispached.Source)
        {
            this.tankerDispached = tankerDispached;
        }
        public override IEnumerable<Tank> Tanks => tankerDispached.Tanks;

    }

}
