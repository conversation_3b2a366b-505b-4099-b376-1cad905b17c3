using Elastic.Clients.Elasticsearch;
using Elastic.Clients.Elasticsearch.Mapping;
using Elastic.Clients.Elasticsearch.QueryDsl;
using Elastic.Transport;
using System;
using System.Collections.Generic;
using System.Linq;
using static GamesEngine.Business.Liquidity.Persistence.DataModel;

namespace GamesEngine.Business.Liquidity.Persistence
{
    public class ElasticsearchStorage : IStorage
    {
        private readonly ElasticsearchClient _client;

        private const string BaseDepositTableName = "deposit";
        private const string BaseJarTableName = "jar";
        private const string BaseTankTableName = "tank";
        private const string BaseTankerTableName = "tanker";
        private const string BaseJarDetailTableName = "jardetail";
        private const string BaseTankDetailTableName = "tankdetail";
        private const string BaseTankerDetailTableName = "tankerdetail";
        private const string BaseWithdrawalTableName = "withdrawal";
        private const string BaseBottleTableName = "bottle";
        private const string BaseDispenserTableName = "dispenser";
        private const string BaseBottleDetailTableName = "bottledetail";
        private const string BaseDispenserDetailTableName = "dispenserdetail";

        public ElasticsearchStorage(string elasticsearchUri)
        {
            var settings = new ElasticsearchClientSettings(new Uri(elasticsearchUri))
                .ThrowExceptions();
            _client = new ElasticsearchClient(settings);
        }

        private string GetDynamicIndexName(string baseName, string kind)
        {
            var sanitizedKind = new string(kind.Where(c => char.IsLetterOrDigit(c) || c == '_').ToArray()).ToLowerInvariant();
            return $"{baseName}_{sanitizedKind}";
        }

        public void CreateTablesIfNotExists(string kind)
        {
            var mappings = new Dictionary<string, TypeMapping>
            {
                { BaseDepositTableName, new TypeMapping { Properties = new Properties {
                    { "Id", new LongNumberProperty() },
                    { "DocumentNumber", new KeywordProperty() },
                    { "Amount", new KeywordProperty() }, 
                    { "Date", new DateProperty() },
                    { "StoreId", new IntegerNumberProperty() },
                    { "AccountNumber", new KeywordProperty() },
                    { "DomainId", new IntegerNumberProperty() },
                    { "Address", new KeywordProperty() },
                    { "Created", new DateProperty() }
                } } },
                { BaseJarTableName, new TypeMapping { Properties = new Properties {
                    { "Version", new LongNumberProperty() },
                    { "Description", new KeywordProperty() },
                    { "Created", new DateProperty() }
                } } },
                { BaseJarDetailTableName, new TypeMapping { Properties = new Properties {
                    { "JarVersion", new LongNumberProperty() },
                    { "DepositId", new LongNumberProperty() },
                    { "Created", new DateProperty() }
                } } },
                { BaseTankTableName, new TypeMapping { Properties = new Properties {
                    { "Id", new LongNumberProperty() },
                    { "Description", new KeywordProperty() },
                    { "Created", new DateProperty() }
                } } },
                { BaseTankDetailTableName, new TypeMapping { Properties = new Properties {
                    { "TankId", new LongNumberProperty() },
                    { "DepositId", new LongNumberProperty() },
                    { "Created", new DateProperty() }
                } } },
                { BaseTankerTableName, new TypeMapping { Properties = new Properties {
                    { "Id", new LongNumberProperty() },
                    { "Description", new KeywordProperty() },
                    { "Created", new DateProperty() }
                } } },
                { BaseTankerDetailTableName, new TypeMapping { Properties = new Properties {
                    { "TankerId", new LongNumberProperty() },
                    { "DepositId", new LongNumberProperty() },
                    { "Created", new DateProperty() }
                } } },
                { BaseWithdrawalTableName, new TypeMapping { Properties = new Properties {
                    { "Id", new LongNumberProperty() },
                    { "DocumentNumber", new KeywordProperty() },
                    { "Amount", new KeywordProperty() }, 
                    { "Date", new DateProperty() },
                    { "StoreId", new IntegerNumberProperty() },
                    { "AccountNumber", new KeywordProperty() },
                    { "DomainId", new IntegerNumberProperty() },
                    { "Address", new KeywordProperty() },
                    { "Created", new DateProperty() }
                } } },
                { BaseBottleTableName, new TypeMapping { Properties = new Properties {
                    { "Id", new LongNumberProperty() },
                    { "Description", new KeywordProperty() },
                    { "Created", new DateProperty() }
                } } },
                { BaseDispenserTableName, new TypeMapping { Properties = new Properties {
                    { "Id", new LongNumberProperty() },
                    { "Description", new KeywordProperty() },
                    { "Created", new DateProperty() }
                } } },
                { BaseBottleDetailTableName, new TypeMapping { Properties = new Properties {
                    { "WithdrawalId", new LongNumberProperty() },
                    { "BottleId", new LongNumberProperty() },
                    { "Created", new DateProperty() }
                } } },
                { BaseDispenserDetailTableName, new TypeMapping { Properties = new Properties {
                    { "WithdrawalId", new LongNumberProperty() },
                    { "DispenserId", new LongNumberProperty() },
                    { "Created", new DateProperty() }
                } } }
            };

            foreach (var baseName in mappings.Keys)
            {
                string indexName = GetDynamicIndexName(baseName, kind);
                if (!_client.Indices.Exists(indexName).Exists)
                {
                    var createResponse = _client.Indices.Create(indexName, c => c
                        .Mappings(m => m.Properties(mappings[baseName].Properties)));
                    if (!createResponse.IsSuccess())
                    {
                        throw new Exception($"Failed to create index {indexName}: {createResponse.DebugInformation}");
                    }
                }
            }
        }

        public List<Deposit> DepositsInNewestJar(string kind, DateTime startDate, DateTime endDate, string accountNumber = null)
        {
            string jarIndex = GetDynamicIndexName(BaseJarTableName, kind);
            string jarDetailIndex = GetDynamicIndexName(BaseJarDetailTableName, kind);
            string depositIndex = GetDynamicIndexName(BaseDepositTableName, kind);

            var searchJarResponse = _client.Search<EsJar>(s => s
                .Index(jarIndex)
                .Sort(so => so.Field(f => f.Version, new FieldSort { Order = SortOrder.Desc }))
                .Size(1));

            if (!searchJarResponse.IsSuccess() || !searchJarResponse.Documents.Any())
            {
                return new List<Deposit>();
            }

            long newestJarVersion = searchJarResponse.Documents.First().Version;

            var searchJarDetailResponse = _client.Search<EsJarDetail>(s => s
                .Index(jarDetailIndex)
                .Query(q => q.Term(t => t.Field(f => f.JarVersion).Value(newestJarVersion)))
                .Size(10000));

            if (!searchJarDetailResponse.IsSuccess())
            {
                throw new Exception($"Failed to search jar details: {searchJarDetailResponse.DebugInformation}");
            }

            var depositIds = searchJarDetailResponse.Documents.Select(d => d.DepositId).ToList();
            if (!depositIds.Any())
            {
                return new List<Deposit>();
            }

            var searchDepositResponse = _client.Search<EsDeposit>(s => s
                .Index(depositIndex)
                .Query(q => q
                    .Bool(b => b
                        .Must(
                            m => m.Terms(t => t.Field(f => f.Id).Terms(new TermsQueryField(depositIds.Select(id => FieldValue.Long(id)).ToArray()))),
                            m => m.Range(r => r.DateRange(dr => dr.Field(f => f.Date).Gte(startDate).Lte(endDate))))
                        .Filter(string.IsNullOrEmpty(accountNumber) ?
                            new Query[] { new MatchAllQuery() } :
                            new Query[] { new TermQuery(new Field("AccountNumber")) { Value = accountNumber } })))
                .Size(10000));

            if (!searchDepositResponse.IsSuccess())
            {
                throw new Exception($"Failed to search deposits: {searchDepositResponse.DebugInformation}");
            }

            return searchDepositResponse.Documents.Select(d => new Deposit
            {
                Id = d.Id,
                DocumentNumber = d.DocumentNumber,
                Amount = decimal.Parse(d.Amount),
                Date = d.Date,
                StoreId = d.StoreId,
                AccountNumber = d.AccountNumber,
                DomainId = d.DomainId,
                Address = d.Address,
                Created = d.Created
            }).ToList();
        }

        public TankWithDeposits TankAndAllItsDeposits(string kind, long tankId)
        {
            string tankIndex = GetDynamicIndexName(BaseTankTableName, kind);
            string tankDetailIndex = GetDynamicIndexName(BaseTankDetailTableName, kind);
            string depositIndex = GetDynamicIndexName(BaseDepositTableName, kind);

            var getTankResponse = _client.Get<EsTank>(tankId, g => g.Index(tankIndex));
            if (!getTankResponse.Found)
            {
                return null;
            }

            var tank = getTankResponse.Source;

            var searchTankDetailResponse = _client.Search<EsTankDetail>(s => s
                .Index(tankDetailIndex)
                .Query(q => q.Term(t => t.Field(f => f.TankId).Value(tankId)))
                .Size(10000));

            if (!searchTankDetailResponse.IsSuccess())
            {
                throw new Exception($"Failed to search tank details: {searchTankDetailResponse.DebugInformation}");
            }

            var depositIds = searchTankDetailResponse.Documents.Select(d => d.DepositId).ToList();

            var searchDepositResponse = _client.Search<EsDeposit>(s => s
                .Index(depositIndex)
                .Query(q => q.Terms(t => t.Field(f => f.Id).Terms(new TermsQueryField(depositIds.Select(id => FieldValue.Long(id)).ToArray()))))
                .Size(depositIds.Count));

            if (!searchDepositResponse.IsSuccess())
            {
                throw new Exception($"Failed to search deposits: {searchDepositResponse.DebugInformation}");
            }

            return new TankWithDeposits
            {
                TankInfo = new Tank { Id = tank.Id, Description = tank.Description, Created = tank.Created },
                Deposits = searchDepositResponse.Documents.Select(d => new Deposit
                {
                    Id = d.Id,
                    DocumentNumber = d.DocumentNumber,
                    Amount = decimal.Parse(d.Amount),
                    Date = d.Date,
                    StoreId = d.StoreId,
                    AccountNumber = d.AccountNumber,
                    DomainId = d.DomainId,
                    Address = d.Address,
                    Created = d.Created
                }).ToList()
            };
        }

        public TankerWithDeposits TankerAndAllItsDeposits(string kind, long tankerId)
        {
            string tankerIndex = GetDynamicIndexName(BaseTankerTableName, kind);
            string tankerDetailIndex = GetDynamicIndexName(BaseTankerDetailTableName, kind);
            string depositIndex = GetDynamicIndexName(BaseDepositTableName, kind);

            var getTankerResponse = _client.Get<EsTanker>(tankerId, g => g.Index(tankerIndex));
            if (!getTankerResponse.Found)
            {
                return null;
            }

            var tanker = getTankerResponse.Source;

            var searchTankerDetailResponse = _client.Search<EsTankerDetail>(s => s
                .Index(tankerDetailIndex)
                .Query(q => q.Term(t => t.Field(f => f.TankerId).Value(tankerId)))
                .Size(10000));

            if (!searchTankerDetailResponse.IsSuccess())
            {
                throw new Exception($"Failed to search tanker details: {searchTankerDetailResponse.DebugInformation}");
            }

            var depositIds = searchTankerDetailResponse.Documents.Select(d => d.DepositId).ToList();

            var searchDepositResponse = _client.Search<EsDeposit>(s => s
                .Index(depositIndex)
                .Query(q => q.Terms(t => t.Field(f => f.Id).Terms(new TermsQueryField(depositIds.Select(id => FieldValue.Long(id)).ToArray()))))
                .Size(depositIds.Count));

            if (!searchDepositResponse.IsSuccess())
            {
                throw new Exception($"Failed to search deposits: {searchDepositResponse.DebugInformation}");
            }

            return new TankerWithDeposits
            {
                TankerInfo = new Tanker { Id = tanker.Id, Description = tanker.Description, Created = tanker.Created },
                Deposits = searchDepositResponse.Documents.Select(d => new Deposit
                {
                    Id = d.Id,
                    DocumentNumber = d.DocumentNumber,
                    Amount = decimal.Parse(d.Amount),
                    Date = d.Date,
                    StoreId = d.StoreId,
                    AccountNumber = d.AccountNumber,
                    DomainId = d.DomainId,
                    Address = d.Address,
                    Created = d.Created
                }).ToList()
            };
        }

        public DispenserWithWithdrawals DispenserAndAllItsWithdrawals(string kind, long dispenserId)
        {
            string dispenserIndex = GetDynamicIndexName(BaseDispenserTableName, kind);
            string dispenserDetailIndex = GetDynamicIndexName(BaseDispenserDetailTableName, kind);
            string withdrawalIndex = GetDynamicIndexName(BaseWithdrawalTableName, kind);

            var getDispenserResponse = _client.Get<EsDispenser>(dispenserId, g => g.Index(dispenserIndex));
            if (!getDispenserResponse.Found)
            {
                return null;
            }

            var dispenser = getDispenserResponse.Source;

            var searchDispenserDetailResponse = _client.Search<EsDispenserDetail>(s => s
                .Index(dispenserDetailIndex)
                .Query(q => q.Term(t => t.Field(f => f.DispenserId).Value(dispenserId)))
                .Size(10000));

            if (!searchDispenserDetailResponse.IsSuccess())
            {
                throw new Exception($"Failed to search dispenser details: {searchDispenserDetailResponse.DebugInformation}");
            }

            var withdrawalIds = searchDispenserDetailResponse.Documents.Select(d => d.WithdrawalId).ToList();

            var searchWithdrawalResponse = _client.Search<EsWithdrawal>(s => s
                .Index(withdrawalIndex)
                .Query(q => q.Terms(t => t.Field(f => f.Id).Terms(new TermsQueryField(withdrawalIds.Select(id => FieldValue.Long(id)).ToArray()))))
                .Size(withdrawalIds.Count));

            if (!searchWithdrawalResponse.IsSuccess())
            {
                throw new Exception($"Failed to search withdrawals: {searchWithdrawalResponse.DebugInformation}");
            }

            return new DispenserWithWithdrawals
            {
                DispenserInfo = new Dispenser { Id = dispenser.Id, Description = dispenser.Description, Created = dispenser.Created },
                Withdrawals = searchWithdrawalResponse.Documents.Select(w => new Withdrawal
                {
                    Id = w.Id,
                    DocumentNumber = w.DocumentNumber,
                    Amount = decimal.Parse(w.Amount),
                    Date = w.Date,
                    StoreId = w.StoreId,
                    AccountNumber = w.AccountNumber,
                    DomainId = w.DomainId,
                    Address = w.Address,
                    Created = w.Created
                }).ToList()
            };
        }

        public void CreateDeposit(string kind, Deposit deposit)
        {
            string depositIndex = GetDynamicIndexName(BaseDepositTableName, kind);
            var esDeposit = new EsDeposit
            {
                Id = deposit.Id,
                DocumentNumber = deposit.DocumentNumber,
                Amount = deposit.Amount.ToString("F8"),
                Date = deposit.Date,
                StoreId = deposit.StoreId,
                AccountNumber = deposit.AccountNumber,
                DomainId = deposit.DomainId,
                Address = deposit.Address,
                Created = deposit.Created
            };
            var indexResponse = _client.Index(esDeposit, i => i.Index(depositIndex).Id(deposit.Id));
            if (!indexResponse.IsSuccess())
            {
                throw new Exception($"Failed to index deposit: {indexResponse.DebugInformation}");
            }
        }

        public void CreateJar(string kind, long version, string description, DateTime created)
        {
            string jarIndex = GetDynamicIndexName(BaseJarTableName, kind);
            var esJar = new EsJar
            {
                Version = version,
                Description = description,
                Created = created
            };
            var indexResponse = _client.Index(esJar, i => i.Index(jarIndex).Id(version));
            if (!indexResponse.IsSuccess())
            {
                throw new Exception($"Failed to index jar: {indexResponse.DebugInformation}");
            }
        }

        public void CreateTank(string kind, Tank tank)
        {
            string tankIndex = GetDynamicIndexName(BaseTankTableName, kind);
            var esTank = new EsTank
            {
                Id = tank.Id,
                Description = tank.Description,
                Created = tank.Created
            };
            var indexResponse = _client.Index(esTank, i => i.Index(tankIndex).Id(tank.Id));
            if (!indexResponse.IsSuccess())
            {
                throw new Exception($"Failed to index tank: {indexResponse.DebugInformation}");
            }
        }

        public void CreateTanker(string kind, Tanker tanker)
        {
            string tankerIndex = GetDynamicIndexName(BaseTankerTableName, kind);
            var esTanker = new EsTanker
            {
                Id = tanker.Id,
                Description = tanker.Description,
                Created = tanker.Created
            };
            var indexResponse = _client.Index(esTanker, i => i.Index(tankerIndex).Id(tanker.Id));
            if (!indexResponse.IsSuccess())
            {
                throw new Exception($"Failed to index tanker: {indexResponse.DebugInformation}");
            }
        }

        public void CreateJarDetailIfNotExists(string kind, long jarVersion, long depositId, DateTime created)
        {
            string jarDetailIndex = GetDynamicIndexName(BaseJarDetailTableName, kind);
            string docId = $"{jarVersion}_{depositId}";
            var jarDetail = new EsJarDetail
            {
                JarVersion = jarVersion,
                DepositId = depositId,
                Created = created
            };
            try
            {
                var indexResponse = _client.Index(jarDetail, i => i.Index(jarDetailIndex).Id(docId).OpType(OpType.Create));
                if (!indexResponse.IsSuccess())
                {
                    throw new Exception($"Failed to create jar detail: {indexResponse.DebugInformation}");
                }
            }
            catch (Exception ex) when (ex.Message.Contains("409") || ex.Message.Contains("version_conflict"))
            {
                // Already exists, do nothing
            }
        }

        public void CreateTankDetailIfNotExists(string kind, long tankId, long depositId, DateTime created)
        {
            string tankDetailIndex = GetDynamicIndexName(BaseTankDetailTableName, kind);
            string docId = $"{tankId}_{depositId}";
            var tankDetail = new EsTankDetail
            {
                TankId = tankId,
                DepositId = depositId,
                Created = created
            };
            try
            {
                var indexResponse = _client.Index(tankDetail, i => i.Index(tankDetailIndex).Id(docId).OpType(OpType.Create));
                if (!indexResponse.IsSuccess())
                {
                    throw new Exception($"Failed to create tank detail: {indexResponse.DebugInformation}");
                }
            }
            catch (Exception ex) when (ex.Message.Contains("409") || ex.Message.Contains("version_conflict"))
            {
                // Already exists, do nothing
            }
        }

        public void CreateTankDetails(string kind, long tankId, IEnumerable<int> depositIds, DateTime created)
        {
            string tankDetailIndex = GetDynamicIndexName(BaseTankDetailTableName, kind);

            var bulkResponse = _client.Bulk(b =>
            {
                foreach (var depositId in depositIds)
                {
                    string docId = $"{tankId}_{depositId}";
                    var tankDetail = new EsTankDetail
                    {
                        TankId = tankId,
                        DepositId = depositId,
                        Created = created
                    };
                    b.Index<EsTankDetail>(tankDetail, i => i
                        .Index(tankDetailIndex)
                        .Id(docId));
                }
            });

            if (!bulkResponse.IsSuccess())
            {
                foreach (var item in bulkResponse.ItemsWithErrors)
                {
                    Console.WriteLine($"Failed to create tank detail for tankId={tankId}, depositId={item.Id}: {item.Error?.Reason}");
                }
            }
        }

        public void CreateTankerDetailIfNotExists(string kind, long tankerId, long depositId, DateTime created)
        {
            string tankerDetailIndex = GetDynamicIndexName(BaseTankerDetailTableName, kind);
            string docId = $"{tankerId}_{depositId}";
            var tankerDetail = new EsTankerDetail
            {
                TankerId = tankerId,
                DepositId = depositId,
                Created = created
            };
            try
            {
                var indexResponse = _client.Index(tankerDetail, i => i.Index(tankerDetailIndex).Id(docId).OpType(OpType.Create));
                if (!indexResponse.IsSuccess())
                {
                    throw new Exception($"Failed to create tanker detail: {indexResponse.DebugInformation}");
                }
            }
            catch (Exception ex) when (ex.Message.Contains("409") || ex.Message.Contains("version_conflict"))
            {
                // Already exists, do nothing
            }
        }

        public void CreateWithdrawal(string kind, Withdrawal withdrawal)
        {
            string withdrawalIndex = GetDynamicIndexName(BaseWithdrawalTableName, kind);
            var esWithdrawal = new EsWithdrawal
            {
                Id = withdrawal.Id,
                DocumentNumber = withdrawal.DocumentNumber,
                Amount = withdrawal.Amount.ToString("F8"),
                Date = withdrawal.Date,
                StoreId = withdrawal.StoreId,
                AccountNumber = withdrawal.AccountNumber,
                DomainId = withdrawal.DomainId,
                Address = withdrawal.Address,
                Created = withdrawal.Created
            };
            var indexResponse = _client.Index(esWithdrawal, i => i.Index(withdrawalIndex).Id(withdrawal.Id));
            if (!indexResponse.IsSuccess())
            {
                throw new Exception($"Failed to index withdrawal: {indexResponse.DebugInformation}");
            }
        }

        public void CreateBottle(string kind, Bottle bottle)
        {
            string bottleIndex = GetDynamicIndexName(BaseBottleTableName, kind);
            var esBottle = new EsBottle
            {
                Id = bottle.Id,
                Description = bottle.Description,
                Created = bottle.Created
            };
            var indexResponse = _client.Index(esBottle, i => i.Index(bottleIndex).Id(bottle.Id));
            if (!indexResponse.IsSuccess())
            {
                throw new Exception($"Failed to index bottle: {indexResponse.DebugInformation}");
            }
        }

        public void CreateDispenser(string kind, Dispenser dispenser)
        {
            string dispenserIndex = GetDynamicIndexName(BaseDispenserTableName, kind);
            var esDispenser = new EsDispenser
            {
                Id = dispenser.Id,
                Description = dispenser.Description,
                Created = dispenser.Created
            };
            var indexResponse = _client.Index(esDispenser, i => i.Index(dispenserIndex).Id(dispenser.Id));
            if (!indexResponse.IsSuccess())
            {
                throw new Exception($"Failed to index dispenser: {indexResponse.DebugInformation}");
            }
        }

        public void CreateBottleDetailIfNotExists(string kind, long withdrawalId, long bottleId, DateTime created)
        {
            string bottleDetailIndex = GetDynamicIndexName(BaseBottleDetailTableName, kind);
            string docId = $"{withdrawalId}_{bottleId}";
            var bottleDetail = new EsBottleDetail
            {
                WithdrawalId = withdrawalId,
                BottleId = bottleId,
                Created = created
            };
            try
            {
                var indexResponse = _client.Index(bottleDetail, i => i.Index(bottleDetailIndex).Id(docId).OpType(OpType.Create));
                if (!indexResponse.IsSuccess())
                {
                    throw new Exception($"Failed to create bottle detail: {indexResponse.DebugInformation}");
                }
            }
            catch (Exception ex) when (ex.Message.Contains("409") || ex.Message.Contains("version_conflict"))
            {
                // Already exists, do nothing
            }
        }

        public void CreateDispenserDetailIfNotExists(string kind, long withdrawalId, long dispenserId, DateTime created)
        {
            string dispenserDetailIndex = GetDynamicIndexName(BaseDispenserDetailTableName, kind);
            string docId = $"{withdrawalId}_{dispenserId}";
            var dispenserDetail = new EsDispenserDetail
            {
                WithdrawalId = withdrawalId,
                DispenserId = dispenserId,
                Created = created
            };
            try
            {
                var indexResponse = _client.Index(dispenserDetail, i => i.Index(dispenserDetailIndex).Id(docId).OpType(OpType.Create));
                if (!indexResponse.IsSuccess())
                {
                    throw new Exception($"Failed to create dispenser detail: {indexResponse.DebugInformation}");
                }
            }
            catch (Exception ex) when (ex.Message.Contains("409") || ex.Message.Contains("version_conflict"))
            {
                // Already exists, do nothing
            }
        }

        #region Document Classes
        public class EsDeposit
        {
            public long Id { get; set; }
            public string DocumentNumber { get; set; }
            public string Amount { get; set; } 
            public DateTime Date { get; set; }
            public int StoreId { get; set; }
            public string AccountNumber { get; set; }
            public int DomainId { get; set; }
            public string Address { get; set; }
            public DateTime Created { get; set; }
        }

        public class EsJar
        {
            public long Version { get; set; }
            public string Description { get; set; }
            public DateTime Created { get; set; }
        }

        public class EsJarDetail
        {
            public long JarVersion { get; set; }
            public long DepositId { get; set; }
            public DateTime Created { get; set; }
        }

        public class EsTank
        {
            public long Id { get; set; }
            public string Description { get; set; }
            public DateTime Created { get; set; }
        }

        public class EsTankDetail
        {
            public long TankId { get; set; }
            public long DepositId { get; set; }
            public DateTime Created { get; set; }
        }

        public class EsTanker
        {
            public long Id { get; set; }
            public string Description { get; set; }
            public DateTime Created { get; set; }
        }

        public class EsTankerDetail
        {
            public long TankerId { get; set; }
            public long DepositId { get; set; }
            public DateTime Created { get; set; }
        }

        public class EsWithdrawal
        {
            public long Id { get; set; }
            public string DocumentNumber { get; set; }
            public string Amount { get; set; } 
            public DateTime Date { get; set; }
            public int StoreId { get; set; }
            public string AccountNumber { get; set; }
            public int DomainId { get; set; }
            public string Address { get; set; }
            public DateTime Created { get; set; }
        }

        public class EsBottle
        {
            public long Id { get; set; }
            public string Description { get; set; }
            public DateTime Created { get; set; }
        }

        public class EsDispenser
        {
            public long Id { get; set; }
            public string Description { get; set; }
            public DateTime Created { get; set; }
        }

        public class EsBottleDetail
        {
            public long WithdrawalId { get; set; }
            public long BottleId { get; set; }
            public DateTime Created { get; set; }
        }

        public class EsDispenserDetail
        {
            public long WithdrawalId { get; set; }
            public long DispenserId { get; set; }
            public DateTime Created { get; set; }
        }
        #endregion
    }
}