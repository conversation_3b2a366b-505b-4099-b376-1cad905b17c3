﻿using GamesEngine.MessageQueuing;
using GamesEngine.Settings;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GamesEngine.Business.Liquidity
{
    public class ConfirmDepositMessage : TypedMessage
    {
        public int DepositId { get; internal set; }
        public string Address { get; internal set; }
        public decimal Amount { get; internal set; }
        public DateTime CreatedAt { get; internal set; }
        public string TransactionId { get; internal set; }
        public string Kind { get; internal set; }
        public int JarVersion { get; internal set; }
        public string Account { get; internal set; }
        public int DomainId { get; internal set; }

        public ConfirmDepositMessage(int depositId, string address, decimal amount, DateTime createdAt, string transactionId, string kind, int jarVersion, string account, int domainId) 
            : base((char)(int)LiquidityMessageType.DepositConfirmed)
        {
            DepositId = depositId;
            Address = address;
            Amount = amount;
            CreatedAt = createdAt;
            TransactionId = transactionId;
            Kind = kind;
            JarVersion = jarVersion;
            Account = account;
            DomainId = domainId;
        }

        public ConfirmDepositMessage(string serializedMessage) : base(serializedMessage) { }

        protected override void Deserialize(string[] serializedMessage, out int fieldOrder)
        {
            if (serializedMessage.Length <= 0) throw new GameEngineException("Invalid format to make a deposit");
            base.Deserialize(serializedMessage, out fieldOrder);
            DepositId = int.Parse(serializedMessage[fieldOrder++]);
            Address = serializedMessage[fieldOrder++];
            Amount = decimal.Parse(serializedMessage[fieldOrder++]);

            int year = int.Parse(serializedMessage[fieldOrder++]);
            int month = int.Parse(serializedMessage[fieldOrder++]);
            int day = int.Parse(serializedMessage[fieldOrder++]);
            int hour = int.Parse(serializedMessage[fieldOrder++]);
            int minute = int.Parse(serializedMessage[fieldOrder++]);
            int second = int.Parse(serializedMessage[fieldOrder++]);
            CreatedAt = new DateTime(year, month, day, hour, minute, second);

            TransactionId = serializedMessage[fieldOrder++];
            Kind = serializedMessage[fieldOrder++];
            JarVersion = int.Parse(serializedMessage[fieldOrder++]);
            Account = serializedMessage[fieldOrder++];
            DomainId = int.Parse(serializedMessage[fieldOrder++]);
        }

        protected override void InternalSerialize()
        {
            base.InternalSerialize();
            AddProperty(DepositId).
            AddProperty(Address).
            AddProperty(Amount).
            AddProperty(CreatedAt).
            AddProperty(TransactionId).
            AddProperty(Kind).
            AddProperty(JarVersion).
            AddProperty(Account).
            AddProperty(DomainId);
        }

    }

    public class CreatedJarMessage : TypedMessage
    {
        public long Version { get; internal set; }
        public string Kind { get; internal set; }
        public string Description { get; internal set; }
        public DateTime CreatedAt { get; internal set; }

        public CreatedJarMessage(long version, string kind, string description, DateTime createdAt) : base((char)(int)LiquidityMessageType.JarCreated)
        {
            Version = version;
            Kind = kind;
            Description = description;
            CreatedAt = createdAt;
        }

        public CreatedJarMessage(string serializedMessage) : base(serializedMessage) { }

        protected override void Deserialize(string[] serializedMessage, out int fieldOrder)
        {
            if (serializedMessage.Length <= 0) throw new GameEngineException("Invalid format for CreatedJarMessage");
            base.Deserialize(serializedMessage, out fieldOrder);
            Version = long.Parse(serializedMessage[fieldOrder++]);
            Kind = serializedMessage[fieldOrder++];
            Description = serializedMessage[fieldOrder++];

            int year = int.Parse(serializedMessage[fieldOrder++]);
            int month = int.Parse(serializedMessage[fieldOrder++]);
            int day = int.Parse(serializedMessage[fieldOrder++]);
            int hour = int.Parse(serializedMessage[fieldOrder++]);
            int minute = int.Parse(serializedMessage[fieldOrder++]);
            int second = int.Parse(serializedMessage[fieldOrder++]);
            CreatedAt = new DateTime(year, month, day, hour, minute, second);
        }

        protected override void InternalSerialize()
        {
            base.InternalSerialize();
            AddProperty(Version).
            AddProperty(Kind).
            AddProperty(Description).
            AddProperty(CreatedAt);
        }
    }

    public class CreatedTankMessage : TypedMessage
    {
        public int TankId { get; internal set; }
        public string Kind { get; internal set; }
        public string Description { get; internal set; }
        public DateTime CreatedAt { get; internal set; }
        public int JarVersion { get; internal set; }
        public IEnumerable<int> DepositIds { get; internal set; }

        public CreatedTankMessage(int tankId, string kind, string description, int jarVersion, DateTime createdAt, IEnumerable<int> depositIds) : base((char)(int)LiquidityMessageType.TankCreated)
        {
            TankId = tankId;
            Kind = kind;
            Description = description;
            JarVersion = jarVersion;
            CreatedAt = createdAt;
            DepositIds = depositIds;
        }

        public CreatedTankMessage(string serializedMessage) : base(serializedMessage) { }

        protected override void Deserialize(string[] serializedMessage, out int fieldOrder)
        {
            if (serializedMessage.Length <= 0) throw new GameEngineException("Invalid format to make a deposit");
            base.Deserialize(serializedMessage, out fieldOrder);
            TankId = int.Parse(serializedMessage[fieldOrder++]);
            Kind = serializedMessage[fieldOrder++];
            Description = serializedMessage[fieldOrder++];
            JarVersion = int.Parse(serializedMessage[fieldOrder++]);

            int year = int.Parse(serializedMessage[fieldOrder++]);
            int month = int.Parse(serializedMessage[fieldOrder++]);
            int day = int.Parse(serializedMessage[fieldOrder++]);
            int hour = int.Parse(serializedMessage[fieldOrder++]);
            int minute = int.Parse(serializedMessage[fieldOrder++]);
            int second = int.Parse(serializedMessage[fieldOrder++]);
            CreatedAt = new DateTime(year, month, day, hour, minute, second);

            DepositIds = Enumerable.Empty<int>();
            for (int i = fieldOrder; i < serializedMessage.Length; i++)
            {
                DepositIds = DepositIds.Append(int.Parse(serializedMessage[i]));
            }

        }

        protected override void InternalSerialize()
        {
            base.InternalSerialize();
            AddProperty(TankId).
            AddProperty(Kind).
            AddProperty(Description).
            AddProperty(JarVersion).
            AddProperty(CreatedAt);
            foreach (var depositId in DepositIds)
            {
                AddProperty(depositId);
            }
        }
    }

    public class CreatedDispenserMessage : TypedMessage
    {
        public int Id { get; internal set; }
        public string Kind { get; internal set; }
        public string Address { get; internal set; }
        public decimal Amount { get; internal set; }
        public DateTime StartDate { get; internal set; }

        public CreatedDispenserMessage(int id, string kind, string address, decimal amount, DateTime dispenserStartDate) : base((char)(int)LiquidityMessageType.DispenserCreated)
        {
            Id = id;
            Kind = kind;
            Address = address;
            Amount = amount;
            StartDate = dispenserStartDate;
        }

        public CreatedDispenserMessage(string serializedMessage) : base(serializedMessage) { }

        protected override void Deserialize(string[] serializedMessage, out int fieldOrder)
        {
            if (serializedMessage.Length <= 0) throw new GameEngineException("Invalid format for CreatedDispenserMessage");
            base.Deserialize(serializedMessage, out fieldOrder);
            Id = int.Parse(serializedMessage[fieldOrder++]);
            Kind = serializedMessage[fieldOrder++];
            Address = serializedMessage[fieldOrder++];
            Amount = decimal.Parse(serializedMessage[fieldOrder++]);

            int year = int.Parse(serializedMessage[fieldOrder++]);
            int month = int.Parse(serializedMessage[fieldOrder++]);
            int day = int.Parse(serializedMessage[fieldOrder++]);
            int hour = int.Parse(serializedMessage[fieldOrder++]);
            int minute = int.Parse(serializedMessage[fieldOrder++]);
            int second = int.Parse(serializedMessage[fieldOrder++]);
            StartDate = new DateTime(year, month, day, hour, minute, second);
        }

        protected override void InternalSerialize()
        {
            base.InternalSerialize();
            AddProperty(Id).
            AddProperty(Kind).
            AddProperty(Address).
            AddProperty(Amount).
            AddProperty(StartDate);
        }
    }

    public class CreatedLiquidMessage : TypedMessage
    {
        public string Kind { get; internal set; }
        public DateTime CreatedAt { get; internal set; }

        public CreatedLiquidMessage(string kind, DateTime createdAt) : base((char)(int)LiquidityMessageType.LiquidCreated)
        {
            Kind = kind;
            CreatedAt = createdAt;
        }

        public CreatedLiquidMessage(string serializedMessage) : base(serializedMessage) { }

        protected override void Deserialize(string[] serializedMessage, out int fieldOrder)
        {
            if (serializedMessage.Length <= 0) throw new GameEngineException("Invalid format for CreatedLiquidMessage");
            base.Deserialize(serializedMessage, out fieldOrder);
            Kind = serializedMessage[fieldOrder++];

            int year = int.Parse(serializedMessage[fieldOrder++]);
            int month = int.Parse(serializedMessage[fieldOrder++]);
            int day = int.Parse(serializedMessage[fieldOrder++]);
            int hour = int.Parse(serializedMessage[fieldOrder++]);
            int minute = int.Parse(serializedMessage[fieldOrder++]);
            int second = int.Parse(serializedMessage[fieldOrder++]);
            CreatedAt = new DateTime(year, month, day, hour, minute, second);
        }

        protected override void InternalSerialize()
        {
            base.InternalSerialize();
            AddProperty(Kind).
            AddProperty(CreatedAt);
        }
    }

    public class TankMergedMessage : TypedMessage 
    {
        public int TankId { get; internal set; }
        public string Kind { get; internal set; }
        public string Description { get; internal set; }
        public DateTime CreatedAt { get; internal set; }
        public int JarVersion { get; internal set; }
        public IEnumerable<int> DepositIds { get; internal set; }

        public TankMergedMessage(int tankId, string kind, string description, int jarVersion, DateTime createdAt, IEnumerable<int> depositIds) : base((char)(int)LiquidityMessageType.TankMerged)
        {
            TankId = tankId;
            Kind = kind;
            Description = description;
            JarVersion = jarVersion;
            CreatedAt = createdAt;
            DepositIds = depositIds;
        }

        public TankMergedMessage(string serializedMessage) : base(serializedMessage) { }

        protected override void Deserialize(string[] serializedMessage, out int fieldOrder)
        {
            if (serializedMessage.Length <= 0) throw new GameEngineException("Invalid format for TankMergedMessage");
            base.Deserialize(serializedMessage, out fieldOrder);
            TankId = int.Parse(serializedMessage[fieldOrder++]);
            Kind = serializedMessage[fieldOrder++];
            Description = serializedMessage[fieldOrder++];
            JarVersion = int.Parse(serializedMessage[fieldOrder++]);

            int year = int.Parse(serializedMessage[fieldOrder++]);
            int month = int.Parse(serializedMessage[fieldOrder++]);
            int day = int.Parse(serializedMessage[fieldOrder++]);
            int hour = int.Parse(serializedMessage[fieldOrder++]);
            int minute = int.Parse(serializedMessage[fieldOrder++]);
            int second = int.Parse(serializedMessage[fieldOrder++]);
            CreatedAt = new DateTime(year, month, day, hour, minute, second);

            DepositIds = Enumerable.Empty<int>();
            for (int i = fieldOrder; i < serializedMessage.Length; i++)
            {
                DepositIds = DepositIds.Append(int.Parse(serializedMessage[i]));
            }
        }

        protected override void InternalSerialize()
        {
            base.InternalSerialize();
            AddProperty(TankId).
            AddProperty(Kind).
            AddProperty(Description).
            AddProperty(JarVersion).
            AddProperty(CreatedAt);
            foreach (var depositId in DepositIds)
            {
                AddProperty(depositId);
            }
        }
    }

    public class CreatedTankerMessage : TypedMessage
    {
        public int TankerId { get; internal set; }
        public string Kind { get; internal set; }
        public string Description { get; internal set; }
        public DateTime CreatedAt { get; internal set; }
        public int Version { get; internal set; }

        public CreatedTankerMessage(int tankerId, string kind, string description, int version, DateTime createdAt) : base((char)(int)LiquidityMessageType.TankerCreated)
        {
            TankerId = tankerId;
            Kind = kind;
            Description = description;
            Version = version;
            CreatedAt = createdAt;
        }

        public CreatedTankerMessage(string serializedMessage) : base(serializedMessage) { }

        protected override void Deserialize(string[] serializedMessage, out int fieldOrder)
        {
            if (serializedMessage.Length <= 0) throw new GameEngineException("Invalid format to make a deposit");
            base.Deserialize(serializedMessage, out fieldOrder);
            TankerId = int.Parse(serializedMessage[fieldOrder++]);
            Kind = serializedMessage[fieldOrder++];
            Description = serializedMessage[fieldOrder++];
            Version = int.Parse(serializedMessage[fieldOrder++]);

            int year = int.Parse(serializedMessage[fieldOrder++]);
            int month = int.Parse(serializedMessage[fieldOrder++]);
            int day = int.Parse(serializedMessage[fieldOrder++]);
            int hour = int.Parse(serializedMessage[fieldOrder++]);
            int minute = int.Parse(serializedMessage[fieldOrder++]);
            int second = int.Parse(serializedMessage[fieldOrder++]);
            CreatedAt = new DateTime(year, month, day, hour, minute, second);


        }

        protected override void InternalSerialize()
        {
            base.InternalSerialize();
            AddProperty(TankerId).
            AddProperty(Kind).
            AddProperty(Description).
            AddProperty(Version).
            AddProperty(CreatedAt);
        }

    }

    public enum LiquidityMessageType
    {
        DepositConfirmed,
        LiquidCreated,
        JarCreated,
        TankCreated,
        TankMerged,
        TankerCreated,
        DispenserCreated,
        BottleCreated
    }
}
