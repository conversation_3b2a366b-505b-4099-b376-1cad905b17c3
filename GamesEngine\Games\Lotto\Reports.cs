﻿using GamesEngine.Bets;
using GamesEngine.Business;
using GamesEngine.Custodian;
using GamesEngine.Domains;
using GamesEngine.Exchange;
using GamesEngine.Finance;
using GamesEngine.Gameboards.Lotto;
using GamesEngine.Location;
using GamesEngine.PurchaseOrders;
using GamesEngine.Settings;
using GamesEngine.Time;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;

namespace GamesEngine.Games.Lotto
{
    public class Reports : Objeto
    {
        private readonly PicksLotteryGame lotteries;
        private readonly Dictionary<State, LotteryPick<Pick2>> pick2 = new Dictionary<State, LotteryPick<Pick2>>();
        private readonly Dictionary<State, LotteryPick<Pick3>> pick3 = new Dictionary<State, LotteryPick<Pick3>>();
        private readonly Dictionary<State, LotteryPick<Pick4>> pick4 = new Dictionary<State, LotteryPick<Pick4>>();
        private readonly Dictionary<State, LotteryPick<Pick5>> pick5 = new Dictionary<State, LotteryPick<Pick5>>();
		internal static TopPrizes topPrizes = new TopPrizes(TOP_FIVE);

		private const string GAME_TYPE_PICK2 = "pick2";
        private const string GAME_TYPE_PICK3 = "pick3";
        private const string GAME_TYPE_PICK4 = "pick4";
        private const string GAME_TYPE_PICK5 = "pick5";
        private const string GAME_TYPE_POWERBALL = "powerball";
        private const string GAME_TYPE_LOTTERY_PICK2 = "lotterypick2";
        private const string GAME_TYPE_LOTTERY_PICK3 = "lotterypick3";
        private const string GAME_TYPE_LOTTERY_PICK4 = "lotterypick4";
        private const string GAME_TYPE_LOTTERY_PICK5 = "lotterypick5";
        private const string GAME_TYPE_LOTTERY_POWERBALL = "lotterypowerball";
        private const string GAME_TYPE_ALL = "all";
        public const string DOMAIN_ID_ALL = "all";
        const string SELECTION_ALL = "all";

        private const int DEFAULT_HOUR = 0;
        private const int DEFAULT_MINUTE = 0;
        private const int TOP_FIVE = 5;

        private QueryMakerOfHistoricalPicks queryMaker;
        internal QueryMakerOfHistoricalPicks QueryMaker 
        { 
            get 
            {
                if (queryMaker == null) queryMaker = new QueryMakerOfHistoricalPicks(this.lotteries.Company);
                return queryMaker; 
            } 
        }

        internal Reports(PicksLotteryGame lotteries, Dictionary<State, LotteryPick<Pick2>> pick2, Dictionary<State, LotteryPick<Pick3>> pick3, Dictionary<State, LotteryPick<Pick4>> pick4, Dictionary<State, LotteryPick<Pick5>> pick5)
        {
            if (lotteries == null) throw new ArgumentException(nameof(lotteries));

            this.lotteries = lotteries;
            this.pick2 = pick2;
            this.pick3 = pick3;
            this.pick4 = pick4;
            this.pick5 = pick5;
		}

        private string GameTypeForQuery(string gameType)
        {
            if (String.IsNullOrWhiteSpace(gameType))
            {
                throw new ArgumentNullException(nameof(gameType));
            }

            gameType = gameType.ToLower();
            string type = "";

            switch (gameType)
            {
                case GAME_TYPE_PICK2:
                    type = QueryMakerOfHistoricalPicks.ID_PICK2;
                    break;
                case GAME_TYPE_PICK3:
                    type = QueryMakerOfHistoricalPicks.ID_PICK3;
                    break;
                case GAME_TYPE_PICK4:
                    type = QueryMakerOfHistoricalPicks.ID_PICK4;
                    break;
                case GAME_TYPE_PICK5:
                    type = QueryMakerOfHistoricalPicks.ID_PICK5;
                    break;
                case GAME_TYPE_POWERBALL:
                    type = QueryMakerOfHistoricalPicks.ID_POWERBALL;
                    break;
                case GAME_TYPE_LOTTERY_PICK2:
                    type = QueryMakerOfHistoricalPicks.ID_PICK2;
                    break;
                case GAME_TYPE_LOTTERY_PICK3:
                    type = QueryMakerOfHistoricalPicks.ID_PICK3;
                    break;
                case GAME_TYPE_LOTTERY_PICK4:
                    type = QueryMakerOfHistoricalPicks.ID_PICK4;
                    break;
                case GAME_TYPE_LOTTERY_PICK5:
                    type = QueryMakerOfHistoricalPicks.ID_PICK5;
                    break;
                case GAME_TYPE_LOTTERY_POWERBALL:
                    type = QueryMakerOfHistoricalPicks.ID_POWERBALL;
                    break;
                case GAME_TYPE_ALL:
                    type = QueryMakerOfHistoricalPicks.ALL;
                    break;
                default:
                    throw new Exception($"There is no game type id for {gameType}");
            }

            return type;
        }

        internal CompletedPicksDraws CompletedDrawings(DateTime startDate, DateTime endDate, string drawingId, string accountNumber, string gameType, string ticketNumber, string domainIds)
        {
            if (startDate == default(DateTime)) throw new GameEngineException($"{nameof(startDate)} cannot have default value");
            if (endDate == default(DateTime)) throw new GameEngineException($"{nameof(endDate)} cannot have default value");
            if (startDate > endDate) throw new GameEngineException($"{nameof(startDate)} cannot be greater than {nameof(endDate)}");

            string type = GameTypeForQuery(gameType);
            var domainIdsToSearch = domainIds == DOMAIN_ID_ALL ? string.Empty : domainIds;
            var drawingIdToSearch = drawingId == SELECTION_ALL ? string.Empty : drawingId;
            var queryMaker = new QueryMakerOfHistoricalPicks(lotteries.Company);
            var result = queryMaker.GenerateDrawingsReport(startDate, endDate, drawingIdToSearch, accountNumber, type, ticketNumber, domainIdsToSearch);
            return result;
        }

        internal CompletedKenoDraws CompletedKenoDrawings(DateTime startDate, DateTime endDate, string accountNumber, string ticketNumber, string domainIds)
        {
            if (startDate == default(DateTime)) throw new GameEngineException($"{nameof(startDate)} cannot have default value");
            if (endDate == default(DateTime)) throw new GameEngineException($"{nameof(endDate)} cannot have default value");
            if (startDate > endDate) throw new GameEngineException($"{nameof(startDate)} cannot be greater than {nameof(endDate)}");

            var domainIdsToSearch = domainIds == DOMAIN_ID_ALL ? string.Empty : domainIds;
            var queryMaker = new QueryMakerOfHistoricalKeno(lotteries.Company);
            var result = queryMaker.GenerateDrawingsReport(startDate, endDate, accountNumber, ticketNumber, domainIdsToSearch);
            return result;
        }

        internal TicketsPerPlayersInCompletedPicksDraws TicketsPerPlayersInCompletedDrawings(DateTime startDate, DateTime endDate, string drawingId, string accountNumber, string gameType, string ticketNumber, string domainIds)
        {
            if (startDate == default(DateTime)) throw new GameEngineException($"{nameof(startDate)} cannot have default value");
            if (endDate == default(DateTime)) throw new GameEngineException($"{nameof(endDate)} cannot have default value");
            if (startDate > endDate) throw new GameEngineException($"{nameof(startDate)} cannot be greater than {nameof(endDate)}");

            string type = GameTypeForQuery(gameType);
            var domainIdsToSearch = domainIds == DOMAIN_ID_ALL ? string.Empty : domainIds;
            var drawingIdToSearch = drawingId == SELECTION_ALL ? string.Empty : drawingId;
            var queryMaker = new QueryMakerOfHistoricalPicks(this.lotteries.Company);
            var result = queryMaker.GenerateTicketsPerPlayersInDrawingReport(startDate, endDate, drawingIdToSearch, accountNumber, type, ticketNumber, domainIdsToSearch);
            return result;
        }

        internal TicketsPerPlayersInCompletedKenoDraws TicketsPerPlayersInCompletedKenoDrawings(DateTime startDate, DateTime endDate, string accountNumber, string ticketNumber, string domainIds)
        {
            if (startDate == default(DateTime)) throw new GameEngineException($"{nameof(startDate)} cannot have default value");
            if (endDate == default(DateTime)) throw new GameEngineException($"{nameof(endDate)} cannot have default value");
            if (startDate > endDate) throw new GameEngineException($"{nameof(startDate)} cannot be greater than {nameof(endDate)}");

            var domainIdsToSearch = domainIds == DOMAIN_ID_ALL ? string.Empty : domainIds;
            var queryMaker = new QueryMakerOfHistoricalKeno(this.lotteries.Company);
            var result = queryMaker.GenerateTicketsPerPlayersInDrawingReport(startDate, endDate, accountNumber, ticketNumber, domainIdsToSearch);
            return result;
        }

        internal WagersPerPlayerInCompletedDraw WagersPerPlayerInCompletedDrawing(DateTime startDate, DateTime endDate, string drawingId, string accountNumber, string gameType, string fullTicketNumber, string domainIds)
        {
            if (startDate == default(DateTime)) throw new GameEngineException($"{nameof(startDate)} cannot have default value");
            if (endDate == default(DateTime)) throw new GameEngineException($"{nameof(endDate)} cannot have default value");
            if (startDate > endDate) throw new GameEngineException($"{nameof(startDate)} cannot be greater than {nameof(endDate)}");
            if (string.IsNullOrWhiteSpace(drawingId)) throw new ArgumentNullException(nameof(drawingId));

            string type = GameTypeForQuery(gameType);
            var domainIdsToSearch = domainIds == DOMAIN_ID_ALL ? string.Empty : domainIds;
            var drawingIdToSearch = drawingId == SELECTION_ALL ? string.Empty : drawingId;
            var queryMaker = new QueryMakerOfHistoricalPicks(this.lotteries.Company);
            var result = queryMaker.GenerateWagersPerPlayerInDrawingReport(startDate, endDate, drawingIdToSearch, accountNumber, type, fullTicketNumber, domainIdsToSearch);
            return result;
        }

        internal TicketsPerPlayersInCompletedPicksDraws TicketsPerPlayersInCompletedDrawing(DateTime drawDate, string drawingId, string accountNumber, string gameType, string ticketNumber, string domainIds)
        {
            if (drawDate == default(DateTime)) throw new GameEngineException($"{nameof(drawDate)} cannot have default value");
            if (String.IsNullOrWhiteSpace(gameType)) throw new ArgumentNullException(nameof(gameType));
            if (String.IsNullOrWhiteSpace(drawingId)) throw new ArgumentNullException(nameof(drawingId));

            string type = GameTypeForQuery(gameType);
            var domainIdsToSearch = domainIds == DOMAIN_ID_ALL ? string.Empty : domainIds;
            var drawingIdToSearch = drawingId == SELECTION_ALL ? string.Empty : drawingId;
            var queryMaker = new QueryMakerOfHistoricalPicks(this.lotteries.Company);
            var result = queryMaker.GenerateTicketsPerPlayersInDrawingReport(drawDate, drawDate, drawingIdToSearch, accountNumber, type, ticketNumber, domainIdsToSearch);
            return result;
        }

        internal TicketsPerPlayersInCompletedKenoDraws TicketsPerPlayersInCompletedKenoDrawing(DateTime drawDate, string accountNumber, string ticketNumber, string domainIds)
        {
            if (drawDate == default(DateTime)) throw new GameEngineException($"{nameof(drawDate)} cannot have default value");

            var domainIdsToSearch = domainIds == DOMAIN_ID_ALL ? string.Empty : domainIds;
            var queryMaker = new QueryMakerOfHistoricalKeno(this.lotteries.Company);
            var result = queryMaker.GenerateTicketsPerPlayersInDrawingReport(drawDate, accountNumber, ticketNumber, domainIdsToSearch);
            return result;
        }

        internal WagersPerPlayerInCompletedDraw WagersPerPlayerInCompletedDrawing(DateTime drawDate, string drawingId, string accountNumber, string gameType, string ticketNumber, string domainIds)
        {
            if (drawDate == default(DateTime)) throw new GameEngineException($"{nameof(drawDate)} cannot have default value");
            if (String.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(accountNumber));
            if (String.IsNullOrWhiteSpace(gameType)) throw new ArgumentNullException(nameof(gameType));
            if (string.IsNullOrWhiteSpace(drawingId)) throw new ArgumentNullException(nameof(drawingId));

            string type = GameTypeForQuery(gameType);
            var domainIdsToSearch = domainIds == DOMAIN_ID_ALL ? string.Empty : domainIds;
            var drawingIdToSearch = drawingId == SELECTION_ALL ? string.Empty : drawingId;
            var queryMaker = new QueryMakerOfHistoricalPicks(this.lotteries.Company);
            var result = queryMaker.GenerateWagersPerPlayerInDrawingReport(drawDate, drawDate, drawingIdToSearch, accountNumber, type, ticketNumber, domainIdsToSearch);
            return result;
        }

        internal WinnersReport Winners(DateTime startDate, DateTime endDate, string accountNumber, string domainIds)
        {
            if (startDate == default(DateTime)) throw new GameEngineException($"{nameof(startDate)} cannot have default value");
            if (endDate == default(DateTime)) throw new GameEngineException($"{nameof(endDate)} cannot have default value");
            if (startDate > endDate) throw new GameEngineException($"{nameof(startDate)} cannot be greater than {nameof(endDate)}");

            var domainIdsToSearch = domainIds == DOMAIN_ID_ALL ? string.Empty : domainIds;
            var queryMaker = new QueryMakerOfHistoricalPicks(lotteries.Company);
            var winners = queryMaker.GenerateWinnersReport(startDate, endDate, accountNumber, domainIdsToSearch);
            return new WinnersReport(winners.ToList());
        }

        internal PlayersProfilesReport PlayersProfiles(DateTime startDate, DateTime endDate, string gameType, string domainIds)
        {
            if (startDate == default(DateTime)) throw new GameEngineException($"{nameof(startDate)} cannot have default value");
            if (endDate == default(DateTime)) throw new GameEngineException($"{nameof(endDate)} cannot have default value");
            if (startDate > endDate) throw new GameEngineException($"{nameof(startDate)} cannot be greater than {nameof(endDate)}");

            string type = GameTypeForQuery(gameType);
            var domainIdsToSearch = domainIds == DOMAIN_ID_ALL ? string.Empty : domainIds;
            var report = new PlayersProfilesReport();
            report.GeneratePlayersProfilesReport(startDate, endDate, type, domainIdsToSearch);
            return report;
        }

        internal PlayersProfilesReport PlayersProfiles(DateTime startDate, DateTime endDate, string gameType, string domainIds, int initialIndex, int amountOfRows)
        {
            if (startDate == default(DateTime)) throw new GameEngineException($"{nameof(startDate)} cannot have default value");
            if (endDate == default(DateTime)) throw new GameEngineException($"{nameof(endDate)} cannot have default value");
            if (startDate > endDate) throw new GameEngineException($"{nameof(startDate)} cannot be greater than {nameof(endDate)}");

            string type = GameTypeForQuery(gameType);
            var domainIdsToSearch = domainIds == DOMAIN_ID_ALL ? string.Empty : domainIds;
            var report = new PlayersProfilesReport();
            report.GeneratePlayersProfilesReport(startDate, endDate, type, domainIdsToSearch, initialIndex, amountOfRows);
            return report;
        }

        internal IEnumerable<KenoWinnerRecord> KenoWinners(DateTime startDate, DateTime endDate, string accountNumber, string domainIds)
        {
            if (startDate == default(DateTime)) throw new GameEngineException($"{nameof(startDate)} cannot have default value");
            if (endDate == default(DateTime)) throw new GameEngineException($"{nameof(endDate)} cannot have default value");
            if (startDate > endDate) throw new GameEngineException($"{nameof(startDate)} cannot be greater than {nameof(endDate)}");

            var domainIdsToSearch = domainIds == DOMAIN_ID_ALL ? string.Empty : domainIds;
            var queryMaker = new QueryMakerOfHistoricalKeno(lotteries.Company);
            var result = queryMaker.GenerateWinnersReport(startDate, endDate, accountNumber, domainIdsToSearch);
            return result;
        }

		internal DailyTotalProfitReport GenerateDailyTotalProfit(DateTime startDate, DateTime endDate, DateTime now, string gameType, string domainIds, int currencyId)
		{
			if (startDate == default(DateTime)) throw new GameEngineException($"{nameof(startDate)} cannot have default value");
			if (endDate == default(DateTime)) throw new GameEngineException($"{nameof(endDate)} cannot have default value");
			if (startDate > endDate) throw new GameEngineException($"{nameof(startDate)} cannot be greater than {nameof(endDate)}");

			string type = GameTypeForQuery(gameType);
            var domainIdsToSearch = domainIds == DOMAIN_ID_ALL ? string.Empty : domainIds;
            var queryMaker = new QueryMakerOfHistoricalPicks(lotteries.Company);
			DailyTotalProfitReport result = queryMaker.GenerateDailyTotalProfitReport(startDate, endDate, now, type, domainIdsToSearch, currencyId);
			return result;
		}

        internal DailyTotalProfitReport GenerateDailyTotalProfitKenoReport(DateTime startDate, DateTime endDate, DateTime now, string domainIds)
        {
            if (startDate == default(DateTime)) throw new GameEngineException($"{nameof(startDate)} cannot have default value");
            if (endDate == default(DateTime)) throw new GameEngineException($"{nameof(endDate)} cannot have default value");
            if (startDate > endDate) throw new GameEngineException($"{nameof(startDate)} cannot be greater than {nameof(endDate)}");

            var domainIdsToSearch = domainIds == DOMAIN_ID_ALL ? string.Empty : domainIds;
            var queryMaker = new QueryMakerOfHistoricalKeno();
            DailyTotalProfitReport result = queryMaker.GenerateDailyTotalProfitReport(startDate, endDate, now, domainIdsToSearch);
            return result;
        }

        internal List<AffiliateData> Affiliates()
		{
			var queryMaker = new QueryMakerOfHistoricalPicks();
			List<AffiliateData> result = queryMaker.ListAffiliates();

			return result;
		}

        internal TotalProfitByDrawingReport GenerateTotalProfitByDrawingReport(DateTime startDate, DateTime endDate, DateTime now, string gameType, string uniqueDrawingId, string domainIds)
        {
            if (startDate == default(DateTime)) throw new GameEngineException($"{nameof(startDate)} cannot have default value");
            if (endDate == default(DateTime)) throw new GameEngineException($"{nameof(endDate)} cannot have default value");
            if (startDate > endDate) throw new GameEngineException($"{nameof(startDate)} cannot be greater than {nameof(endDate)}");

            string type = GameTypeForQuery(gameType);
            var domainIdsToSearch = domainIds == DOMAIN_ID_ALL ? string.Empty : domainIds;
            var uniqueDrawingIdToSearch = uniqueDrawingId == SELECTION_ALL ? string.Empty : uniqueDrawingId;
            var queryMaker = new QueryMakerOfHistoricalPicks(lotteries.Company);
            TotalProfitByDrawingReport result = queryMaker.GenerateTotalProfitByDrawingReport(startDate, endDate, now, type, uniqueDrawingIdToSearch, domainIdsToSearch);
            return result;
        }

        internal TotalProfitByDrawingReport GenerateTotalProfitByKenoDrawingReport(DateTime startDate, DateTime endDate, DateTime now, string domainIds)
        {
            if (startDate == default(DateTime)) throw new GameEngineException($"{nameof(startDate)} cannot have default value");
            if (endDate == default(DateTime)) throw new GameEngineException($"{nameof(endDate)} cannot have default value");
            if (startDate > endDate) throw new GameEngineException($"{nameof(startDate)} cannot be greater than {nameof(endDate)}");

            var domainIdsToSearch = domainIds == DOMAIN_ID_ALL ? string.Empty : domainIds;
            var queryMaker = new QueryMakerOfHistoricalKeno(lotteries.Company);
            TotalProfitByDrawingReport result = queryMaker.GenerateTotalProfitByDrawingReport(startDate, endDate, now, domainIdsToSearch);
            return result;
        }

        internal DailyTotalProfitReport ForceGenerationOfDailyTotalProfit(DateTime day)
        {
            if (day == default(DateTime)) throw new GameEngineException($"{nameof(day)} cannot have default value");
            if (day.Hour != 0 || day.Minute != 0) throw new GameEngineException("Day to update table can not have hours or minutes.");

            try
            {
                var queryMaker = new QueryMakerOfHistoricalPicks(lotteries.Company);
                DailyTotalProfitReport result = queryMaker.UpdateDailyTotalProfitAt(day);
                return result;
            }
            catch (Exception e)
            {
                ErrorsSender.Send(e, $"day:{day}");
                throw e;
            }
        }

        internal void AccumulateTotalProfitByDrawing(DateTime startDate, DateTime endDate, DateTime now)
        {
            if (startDate == default(DateTime)) throw new GameEngineException($"{nameof(startDate)} cannot have default value");
            if (endDate == default(DateTime)) throw new GameEngineException($"{nameof(endDate)} cannot have default value");
            if (startDate > endDate) throw new GameEngineException($"{nameof(startDate)} cannot be greater than {nameof(endDate)}");

            var queryMaker = new QueryMakerOfHistoricalPicks(lotteries.Company);
			try
			{
				queryMaker.AccumulateTotalProfitByDrawing(startDate, endDate, now);
			}
			catch (Exception e)
			{
				ErrorsSender.Send(e, $"startDate:{startDate} endDate:{endDate} now:{now}");
			}
		}

        internal TotalProfitByDrawingReport ForceGenerationOfTotalProfitByDrawingAt(DateTime day)
        {
            if (day == default(DateTime)) throw new GameEngineException($"{nameof(day)} cannot have default value");
            if (day.Hour != 0 || day.Minute != 0) throw new GameEngineException("Day to update table can not have hours or minutes.");

            var queryMaker = new QueryMakerOfHistoricalPicks(lotteries.Company);
            try
            {
                var result = queryMaker.UpdateTotalProfitByDrawingAt(day);
                return result;
            }
            catch (GameEngineException e)
            {
                ErrorsSender.Send(e, $"day:{day} ");
                throw e;
            }
        }

        internal TopWinnersOfDrawing TopWinnersForDrawing(DateTime drawDate, string state, string gameType, string domainUrl)
        {
            if (drawDate == default(DateTime)) throw new GameEngineException($"{nameof(drawDate)} cannot have default value");
            if (drawDate.Second != 0 || drawDate.Millisecond != 0) throw new GameEngineException("Date of Lottery drawings can not have seconds");
            if (string.IsNullOrWhiteSpace(state)) throw new ArgumentNullException(nameof(state));
            if (string.IsNullOrWhiteSpace(gameType)) throw new ArgumentNullException(nameof(gameType));

            string type = GameTypeForQuery(gameType);
            var queryMaker = new QueryMakerOfHistoricalPicks(lotteries.Company);
            var winners = queryMaker.TopWinnersForDrawing(drawDate, state, type, domainUrl);
            var result = new TopWinnersOfDrawing(drawDate, state, type, domainUrl);
            if (winners.Any()) result.RankWinners(winners);
            return result;
        }

        public static void UpdateWinnerAfterRegrade(string pickType, string previousWinnerNumber, decimal prize, string drawingName, DateTime drawDate)
		{
            if (string.IsNullOrWhiteSpace(drawingName)) throw new ArgumentNullException(nameof(drawingName));
            
            int pickNumber = Int32.Parse(pickType.Substring(1, 1));            
            Reports.topPrizes.DeleteTop(pickNumber, previousWinnerNumber, prize, drawingName, drawDate);
		}

		public static void ClassifyWinner(string pickType, string winnerNumber, decimal prize, string drawingName, DateTime drawDate)
		{
            if (string.IsNullOrWhiteSpace(drawingName)) throw new ArgumentNullException(nameof(drawingName));

            var queryMaker = new QueryMakerOfHistoricalPicks();
			int pickNumber = Int32.Parse(pickType.Substring(1, 1));
			Reports.topPrizes.InsertTop(pickNumber, winnerNumber, prize, drawingName, drawDate);
		}

		internal IEnumerable<PrizeElement> TopFivePrizesOfMonth()
		{
			return topPrizes.TopOfMonth();
		}

		internal IEnumerable<PrizeElement> TopFivePrizesOfDay()
		{
			return topPrizes.TopOfDay();
		}

        internal IEnumerable<GradingPicksRecord> Grading(DateTime startDate, DateTime endDate, string uniqueDrawingId, string gameType)
        {
            if (startDate == default(DateTime)) throw new GameEngineException($"{nameof(startDate)} cannot have default value");
            if (endDate == default(DateTime)) throw new GameEngineException($"{nameof(endDate)} cannot have default value");
            if (startDate > endDate) throw new GameEngineException($"{nameof(startDate)} cannot be greater than {nameof(endDate)}");

            int numericUniqueDrawingId = 0;
            if (uniqueDrawingId != SELECTION_ALL && !int.TryParse(uniqueDrawingId, out numericUniqueDrawingId)) throw new GameEngineException($"{nameof(uniqueDrawingId)} must be a number");
            var lotteries = uniqueDrawingId == SELECTION_ALL ? LotteryOf(gameType) : LotteryOf(gameType).Where(lottery => lottery.Schedules.Any(schedule => schedule.UniqueId == numericUniqueDrawingId));
            var dates = Enumerable.Range(0, endDate.Subtract(startDate).Days + 1).Select(d => startDate.AddDays(d));

            var drawnLotteries = lotteries.SelectMany(x => x.DrawnLotteriesBetween(dates)).Where(x => !x.IsRegraded);
            var resultDrawnLotteries = drawnLotteries.OrderBy(x => x.Date).
                    GroupBy(x => new
                    {
                        x.State,
                        x.GameType,
                        x.Date,
                        x.IsLotteryPickWithFireBall
                    }).Select(x => new GradingPicksRecord
                    {
                        State = x.Key.State.Abbreviation,
                        DrawingDate = x.First().Date,
                        UniqueDrawingId = x.First().IsLotteryPickWithFireBall
                            ? x.First().Lottery.FindScheduleAt(x.Key.Date).FireballUniqueId
                            : x.First().Lottery.FindScheduleAt(x.Key.Date).UniqueId,
                        IdOfLottery = x.First().Lottery.IdOfLottery.ToString(),
                        DrawingName = x.First().Lottery.FindScheduleAt(x.Key.Date) is WeeklySchedule weeklySchedule && x.Key.IsLotteryPickWithFireBall
                            ? weeklySchedule.FireballDescription()
                            : x.First().Lottery.FindScheduleAt(x.Key.Date).GetDescription(),
                        GradedNumber = x.First().SequenceOfNumbers,
                        RootGradedNumber = x.First().Lottery.RootLottery.SequenceOfNumbersOfDrawAt(x.First().Date, x.First().Lottery.RootLottery.FindScheduleAt(x.Key.Date)),
                        WithFireBall = x.Key.IsLotteryPickWithFireBall,
                        Fireball = x.Key.IsLotteryPickWithFireBall ? x.First().FireBallNumber : LotteryDraw.WITHOUT_FIREBALL,
                        TypeNumberSequenceAsText = x.First().Lottery.TypeNumberSequenceAsText(),
                        GameType = GameTypeForQuery(x.Key.GameType.ToString()),
                        Action = "Graded",
                        GradedBy = x.First().WhoGraded,
                        GradedDate = x.First().LastGradedDate,
                        Log = x.First().Log
                    });

            var drawnLotteriesRegraded = lotteries.SelectMany(x => x.DrawnLotteriesBetween(dates)).Where(x => x.IsRegraded);
            var resultDrawnLotteriesRegraded = drawnLotteriesRegraded.OrderBy(x => x.Date).
                    GroupBy(x => new
                    {
                        x.State,
                        x.GameType,
                        x.Date,
                        x.IsLotteryPickWithFireBall
                    }).Select(x => new GradingPicksRecord
                    {
                        State = x.Key.State.Abbreviation,
                        DrawingDate = x.First().Date,
                        UniqueDrawingId = x.First().IsLotteryPickWithFireBall
                            ? x.First().Lottery.FindScheduleAt(x.Key.Date).FireballUniqueId
                            : x.First().Lottery.FindScheduleAt(x.Key.Date).UniqueId,
                        IdOfLottery = x.First().Lottery.IdOfLottery.ToString(),
                        DrawingName = x.First().Lottery.FindScheduleAt(x.Key.Date) is WeeklySchedule weeklySchedule && x.Key.IsLotteryPickWithFireBall
                            ? weeklySchedule.FireballDescription()
                            : x.First().Lottery.FindScheduleAt(x.Key.Date).GetDescription(),
                        GradedNumber = x.Select(y => y.SequenceOfNumbers).First(),
                        RootGradedNumber = x.First().Lottery.RootLottery.SequenceOfNumbersOfDrawAt(x.First().Date, x.First().Lottery.RootLottery.FindScheduleAt(x.Key.Date)),
                        WithFireBall = x.Key.IsLotteryPickWithFireBall,
                        Fireball = x.Key.IsLotteryPickWithFireBall ? x.First().FireBallNumber : LotteryDraw.WITHOUT_FIREBALL,
                        TypeNumberSequenceAsText = x.First().Lottery.TypeNumberSequenceAsText(),
                        GameType = GameTypeForQuery(x.Key.GameType.ToString()),
                        Action = "Regraded",
                        GradedBy = x.First().WhoRegraded,
                        GradedDate = x.First().LastGradedDate,
                        Log = x.First().Log
                    });

            var drawnLotteriesNoAction = lotteries.SelectMany(x => x.DrawnLotteriesNoActionBetween(dates));
            var resultDrawnLotteriesNoAction = drawnLotteriesNoAction.OrderBy(x => x.Date).
                    GroupBy(x => new
                    {
                        x.State,
                        x.GameType,
                        x.Date
                    }).Select(x => new GradingPicksRecord
                    {
                        State = x.Key.State.Abbreviation,
                        DrawingDate = x.First().Date,
                        UniqueDrawingId = x.First().Lottery.FindScheduleAt(x.Key.Date).Id,
                        IdOfLottery = x.First().Lottery.IdOfLottery.ToString(),
                        DrawingName = x.First().Lottery.FindScheduleAt(x.Key.Date).GetDescription(),
                        GradedNumber = string.Empty,
                        RootGradedNumber = string.Empty,
                        WithFireBall = false,
                        Fireball = LotteryDraw.WITHOUT_FIREBALL,
                        TypeNumberSequenceAsText = x.First().Lottery.TypeNumberSequenceAsText(),
                        GameType = GameTypeForQuery(x.Key.GameType.ToString()),
                        Action = "NoAction",
                        GradedBy = x.First().WhoSetNoAction,
                        GradedDate = x.First().NoActionDate,
                        Log = x.First().Log
                    });
            var result = uniqueDrawingId == SELECTION_ALL ?
                resultDrawnLotteries.Concat(resultDrawnLotteriesRegraded).Concat(resultDrawnLotteriesNoAction).ToList() :
                resultDrawnLotteries.Where(x => x.UniqueDrawingId == numericUniqueDrawingId).Concat(resultDrawnLotteriesRegraded.Where(record => record.UniqueDrawingId == numericUniqueDrawingId)).Concat(resultDrawnLotteriesNoAction.Where(record => record.UniqueDrawingId == numericUniqueDrawingId)).ToList();
            return result;
        }

        internal IEnumerable<GradingKenoRecord> GenerateKenoGradingReport(DateTime startDate, DateTime endDate)
        {
            var dates = Enumerable.Range(0, endDate.Subtract(startDate).Days + 1).Select(d => startDate.AddDays(d));
            var drawnLotteries = lotteries.GetKeno().DrawnLotteriesBetween(dates);
            var drawnLotteriesGraded = drawnLotteries.Where(draw => !draw.IsRegraded);
            var drawnLotteriesRegraded = drawnLotteries.Where(draw => draw.IsRegraded);
            var drawnLotteriesNoAction = lotteries.GetKeno().DrawnLotteriesNoActionBetween(dates);
            var report = new GradingKenoReport(drawnLotteriesGraded, drawnLotteriesRegraded, drawnLotteriesNoAction);
            return report.GetAll;
        }

        internal IEnumerable<string> GameTypes()
        {
            var result = new List<string> { GAME_TYPE_PICK2, GAME_TYPE_PICK3, GAME_TYPE_PICK4, GAME_TYPE_PICK5, GAME_TYPE_POWERBALL };
            return result;
        }

        private IEnumerable<Lottery> LotteryOf(string gameType)
        {
            gameType = gameType.ToLower();
            switch (gameType)
            {
                case GAME_TYPE_PICK2:
                    return pick2.Values;
                case GAME_TYPE_PICK3:
                    return pick3.Values;
                case GAME_TYPE_PICK4:
                    return pick4.Values;
                case GAME_TYPE_PICK5:
                    return pick5.Values;
                case GAME_TYPE_POWERBALL:
                    return new Lottery[] { lotteries.GetPowerball() };
                case GAME_TYPE_ALL:
                    return pick2.Values.Cast<Lottery>().Concat(pick3.Values.Cast<Lottery>().Concat(pick4.Values.Cast<Lottery>().Concat(pick5.Values.Cast<Lottery>())));
                default:
                    throw new Exception($"There is no game type id for {gameType}");
            }
        }

        private string LotteryGameTypeOf(string type)
        {
            switch (type)
            {
                case "LotteryPick2":
                    return QueryMakerOfHistoricalPicks.ID_PICK2;
                case "LotteryPick3":
                    return QueryMakerOfHistoricalPicks.ID_PICK3;
                case "LotteryPick4":
                    return QueryMakerOfHistoricalPicks.ID_PICK4;
                case "LotteryPick5":
                    return QueryMakerOfHistoricalPicks.ID_PICK5;
                case "LotteryPowerball":
                    return QueryMakerOfHistoricalPicks.ID_POWERBALL;
                default:
                    throw new Exception($"There is no game type id for {type}");
            }
        }

        public static string GameTypeOf(string ticketType)
        {
            switch (ticketType)
            {
                case "TicketPick2Straight":
                    return QueryMakerOfHistoricalPicks.ID_PICK2;
                case "TicketPick2Boxed":
                    return QueryMakerOfHistoricalPicks.ID_PICK2;
                case "TicketPick3Straight":
                    return QueryMakerOfHistoricalPicks.ID_PICK3;
                case "TicketPick3Boxed":
                    return QueryMakerOfHistoricalPicks.ID_PICK3;
                case "TicketPick4Straight":
                    return QueryMakerOfHistoricalPicks.ID_PICK4;
                case "TicketPick4Boxed":
                    return QueryMakerOfHistoricalPicks.ID_PICK4;
                case "TicketPick5Straight":
                    return QueryMakerOfHistoricalPicks.ID_PICK5;
                case "TicketPick5Boxed":
                    return QueryMakerOfHistoricalPicks.ID_PICK5;
                case "TicketPowerBallSingle":
                    return QueryMakerOfHistoricalPicks.ID_POWERBALL;
                case "TicketPowerBallPowerPlay":
                    return QueryMakerOfHistoricalPicks.ID_POWERBALL;
                case "TicketKeno10Single":
                    return QueryMakerOfHistoricalKeno.ID_K10_NO_MUL_NO_BULL;
                case "TicketKeno12Single":
                    return QueryMakerOfHistoricalKeno.ID_K12_NO_MUL_NO_BULL;
                case "TicketKeno10Multiplier":
                    return QueryMakerOfHistoricalKeno.ID_K10_MUL_NO_BULL;
                case "TicketKeno12Multiplier":
                    return QueryMakerOfHistoricalKeno.ID_K12_MUL_NO_BULL;
                case "Ticket10KenoBulleye":
                    return QueryMakerOfHistoricalKeno.ID_K10_NO_MUL_BULL;
                case "Ticket12KenoBulleye":
                    return QueryMakerOfHistoricalKeno.ID_K12_NO_MUL_BULL;
                case "TicketKeno10MultiplierAndBulleye":
                    return QueryMakerOfHistoricalKeno.ID_K10_MUL_BULL;
                case "TicketKeno12MultiplierAndBulleye":
                    return QueryMakerOfHistoricalKeno.ID_K12_MUL_BULL;
                default:
                    throw new Exception($"There is no game type id for {ticketType}");
            }
        }

        public static bool IsAValidReportGameType(string type)
        {
            if (QueryMakerOfHistoricalPicks.ID_PICK3 == type || 
                QueryMakerOfHistoricalPicks.ID_PICK4 == type || 
                QueryMakerOfHistoricalPicks.ID_PICK2 == type || 
                QueryMakerOfHistoricalPicks.ID_PICK5 == type ||
                QueryMakerOfHistoricalPicks.ID_POWERBALL == type
                ) 
                return true;
            return false;
        }

        internal PendingDraws PendingDrawings(DateTime startDate, DateTime endDate, string uniqueDrawingId, string accountNumber, string gameType, string ticketNumber, string domainIds)
        {
            if (startDate == default(DateTime)) throw new GameEngineException($"{nameof(startDate)} cannot have default value");
            if (endDate == default(DateTime)) throw new GameEngineException($"{nameof(endDate)} cannot have default value");
            if (startDate > endDate) throw new GameEngineException($"{nameof(startDate)} cannot be greater than {nameof(endDate)}");
            if (string.IsNullOrWhiteSpace(domainIds)) throw new ArgumentNullException(nameof(domainIds));
            if (string.IsNullOrWhiteSpace(uniqueDrawingId)) throw new ArgumentNullException(nameof(uniqueDrawingId));

            const long DEFAULT_TICKET_NUMBER = 0;
            var numericTicketNumber = DEFAULT_TICKET_NUMBER;
            var ticketNumberWithWagerNumber = ticketNumber.Split('-');
            var isTicketNumberWithWagerNumber = ticketNumberWithWagerNumber.Length == 2;
            if (!isTicketNumberWithWagerNumber && !String.IsNullOrWhiteSpace(ticketNumber) && !Int64.TryParse(ticketNumber, out numericTicketNumber)) throw new GameEngineException($"{nameof(ticketNumber)} cannot be greater than {long.MaxValue}");

            var areAllAccountNumbersValid = String.IsNullOrWhiteSpace(accountNumber);
            var areAllTicketNumbersValid = String.IsNullOrWhiteSpace(ticketNumber);
            var areAllGameTypesValid = String.IsNullOrWhiteSpace(gameType) || String.IsNullOrWhiteSpace(GameTypeForQuery(gameType));
            var areAllDrawingIdValid = uniqueDrawingId == SELECTION_ALL;
            int numericUniqueDrawingId = 0;
            if (!areAllDrawingIdValid) if (!int.TryParse(uniqueDrawingId, out numericUniqueDrawingId)) throw new GameEngineException($"{nameof(uniqueDrawingId)} is not a number");
            var allPendingDraws = lotteries.PendingDrawsBy(domainIds);
            var filteredPendingDraws = new List<PendingDraw>();
            foreach (var pendingDraw in allPendingDraws)
            {
                var isDrawBetweenDates = pendingDraw.OnlyDate >= startDate && pendingDraw.OnlyDate <= endDate;
                if (!isDrawBetweenDates) continue;
                var isMatchingDrawingId = areAllDrawingIdValid || pendingDraw.UniqueDrawingId == numericUniqueDrawingId;
                if (!isMatchingDrawingId) continue;
                var isMatchingAccountNumber = areAllAccountNumbersValid || pendingDraw.ContainsOwnerWith(accountNumber);
                if (!isMatchingAccountNumber) continue;

                if(!isTicketNumberWithWagerNumber)
                {
                    var isMatchingTicketNumber = areAllTicketNumbersValid || pendingDraw.ContainsTicketWith(numericTicketNumber);
                    if (!isMatchingTicketNumber) continue;
                }
                else
                {
                    var isMatchingTicketNumber = areAllTicketNumbersValid || pendingDraw.ContainsTicketWith(ticketNumber);
                    if (!isMatchingTicketNumber) continue;
                }
                var isMatchingGameType = areAllGameTypesValid || LotteryGameTypeOf(pendingDraw.GameType) == GameTypeForQuery(gameType);
                if (!isMatchingGameType) continue;
                filteredPendingDraws.Add(pendingDraw);
            }

            var pendingDraws = new PendingDraws(filteredPendingDraws);
            return pendingDraws;
        }

        internal PendingDraws PendingKenoDrawings(DateTime startDate, DateTime endDate, string accountNumber, string ticketNumber, string domainIds)
        {
            if (startDate == default(DateTime)) throw new GameEngineException($"{nameof(startDate)} cannot have default value");
            if (endDate == default(DateTime)) throw new GameEngineException($"{nameof(endDate)} cannot have default value");
            if (startDate > endDate) throw new GameEngineException($"{nameof(startDate)} cannot be greater than {nameof(endDate)}");
            if (string.IsNullOrWhiteSpace(domainIds)) throw new ArgumentNullException(nameof(domainIds));

            const long DEFAULT_TICKET_NUMBER = 0;
            var numericTicketNumber = DEFAULT_TICKET_NUMBER;
            if (!string.IsNullOrWhiteSpace(ticketNumber) && !Int64.TryParse(ticketNumber, out numericTicketNumber)) throw new GameEngineException($"{nameof(ticketNumber)} cannot be greater than {long.MaxValue}");

            var areAllAccountNumbersValid = string.IsNullOrWhiteSpace(accountNumber);
            var areAllTicketNumbersValid = string.IsNullOrWhiteSpace(ticketNumber);
            var allPendingDraws = lotteries.GetKeno().PendingKenoDrawsBy(domainIds);
            var filteredPendingDraws = new List<PendingKenoDraw>();
            foreach (var pendingDraw in allPendingDraws)
            {
                var isDrawBetweenDates = pendingDraw.OnlyDate >= startDate && pendingDraw.OnlyDate <= endDate;
                if (!isDrawBetweenDates) continue;
                var isMatchingAccountNumber = areAllAccountNumbersValid || pendingDraw.ContainsOwnerWith(accountNumber);
                if (!isMatchingAccountNumber) continue;
                var isMatchingTicketNumber = areAllTicketNumbersValid || pendingDraw.ContainsTicketWith(numericTicketNumber);
                if (!isMatchingTicketNumber) continue;
                
                filteredPendingDraws.Add(pendingDraw);
            }

            var pendingDraws = new PendingDraws(filteredPendingDraws);
            return pendingDraws;
        }

        internal TicketsPerPlayers TicketsPerPlayersInPendingDrawing(DateTime drawDate, string uniqueDrawingId, string accountNumber, string gameType, string ticketNumber, string domainIds)
        {
            if (drawDate == default(DateTime)) throw new GameEngineException($"{nameof(drawDate)} cannot have default value");
            if (String.IsNullOrWhiteSpace(gameType)) throw new ArgumentNullException(nameof(gameType));
            if (string.IsNullOrWhiteSpace(uniqueDrawingId)) throw new ArgumentNullException(nameof(uniqueDrawingId));

            const long DEFAULT_TICKET_NUMBER = 0;
            var numericTicketNumber = DEFAULT_TICKET_NUMBER;
            var ticketNumberWithWagerNumber = ticketNumber.Split('-');
            var isTicketNumberWithWagerNumber = ticketNumberWithWagerNumber.Length == 2;
            if (!isTicketNumberWithWagerNumber && !String.IsNullOrWhiteSpace(ticketNumber) && !Int64.TryParse(ticketNumber, out numericTicketNumber)) throw new GameEngineException($"{nameof(ticketNumber)} cannot be greater than {long.MaxValue}");
            if (isTicketNumberWithWagerNumber && !Int64.TryParse(ticketNumberWithWagerNumber[0], out numericTicketNumber)) throw new GameEngineException($"{nameof(ticketNumber)} cannot be greater than {long.MaxValue}");

            var areAllTicketNumbersValid = String.IsNullOrWhiteSpace(ticketNumber);
            var areAllAccountNumbersValid = String.IsNullOrWhiteSpace(accountNumber);
            var areAllGameTypesValid = String.IsNullOrWhiteSpace(GameTypeForQuery(gameType));
            var areAllDrawingIdValid = uniqueDrawingId == SELECTION_ALL;
            int numericUniqueDrawingId = 0;
            if (!areAllDrawingIdValid) if (!int.TryParse(uniqueDrawingId, out numericUniqueDrawingId)) throw new GameEngineException($"{nameof(uniqueDrawingId)} is not a number");
            var allPendingDraws = lotteries.PendingDrawsBy(domainIds);
            PendingDraw pendingDrawSelected = null;
            foreach (var pendingDraw in allPendingDraws)
            {
                var isMatchingDrawDate = pendingDraw.DrawDate == drawDate;
                var isMatchingDrawingId = areAllDrawingIdValid || pendingDraw.UniqueDrawingId == numericUniqueDrawingId;
                var isMatchingAccountNumber = areAllAccountNumbersValid || pendingDraw.ContainsOwnerWith(accountNumber);
                var isMatchingGameType = areAllGameTypesValid || LotteryGameTypeOf(pendingDraw.GameType) == GameTypeForQuery(gameType);
                var isMatchingTicketNumber = areAllTicketNumbersValid || (isTicketNumberWithWagerNumber ? pendingDraw.ContainsTicketWith(ticketNumber) : pendingDraw.ContainsTicketWith(numericTicketNumber));
                var isPendingDrawSelected = isMatchingDrawDate && isMatchingDrawingId && isMatchingAccountNumber && isMatchingGameType && isMatchingTicketNumber;
                if (isPendingDrawSelected)
                {
                    pendingDrawSelected = pendingDraw;
                    break;
                }
            }

            if (pendingDrawSelected == null) return new TicketsPerPlayersEmpty();
            var report = new TicketsPerPlayersInPendingDraws(new PendingDraw[] { pendingDrawSelected }, accountNumber, numericTicketNumber);
            return report;
        }

        internal TicketsPerPlayers TicketsPerPlayersInPendingKenoDrawing(DateTime drawDate, string accountNumber, string ticketNumber, string domainIds)
        {
            if (drawDate == default(DateTime)) throw new GameEngineException($"{nameof(drawDate)} cannot have default value");
            if (string.IsNullOrWhiteSpace(domainIds)) throw new ArgumentNullException(nameof(domainIds));

            const long DEFAULT_TICKET_NUMBER = 0;
            var numericTicketNumber = DEFAULT_TICKET_NUMBER;
            if (!string.IsNullOrWhiteSpace(ticketNumber) && !Int64.TryParse(ticketNumber, out numericTicketNumber)) throw new GameEngineException($"{nameof(ticketNumber)} cannot be greater than {long.MaxValue}");

            var areAllTicketNumbersValid = string.IsNullOrWhiteSpace(ticketNumber);
            var areAllAccountNumbersValid = string.IsNullOrWhiteSpace(accountNumber);
            var allPendingDraws = lotteries.GetKeno().PendingKenoDrawsBy(domainIds);
            PendingKenoDraw pendingDrawSelected = null;
            foreach (var pendingDraw in allPendingDraws)
            {
                var isMatchingDrawDate = pendingDraw.DrawDate == drawDate;
                var isMatchingAccountNumber = areAllAccountNumbersValid || pendingDraw.ContainsOwnerWith(accountNumber);
                var isMatchingTicketNumber = areAllTicketNumbersValid || pendingDraw.ContainsTicketWith(numericTicketNumber);
                var isPendingDrawSelected = isMatchingDrawDate && isMatchingAccountNumber && isMatchingTicketNumber;
                if (isPendingDrawSelected)
                {
                    pendingDrawSelected = pendingDraw;
                    break;
                }
            }

            if (pendingDrawSelected == null) return new TicketsPerPlayersEmpty();
            var report = new TicketsPerPlayersInPendingDraws(new PendingDraw[] { pendingDrawSelected }, accountNumber, numericTicketNumber);
            return report;
        }

        internal TicketsPerPlayers TicketsPerPlayersInPendingDrawings(DateTime startDate, DateTime endDate, string uniqueDrawingId, string accountNumber, string gameType, string ticketNumber, string domainIds)
        {
            if (startDate == default(DateTime)) throw new GameEngineException($"{nameof(startDate)} cannot have default value");
            if (endDate == default(DateTime)) throw new GameEngineException($"{nameof(endDate)} cannot have default value");
            if (startDate > endDate) throw new GameEngineException($"{nameof(startDate)} cannot be greater than {nameof(endDate)}");
            if (String.IsNullOrWhiteSpace(gameType)) throw new ArgumentNullException(nameof(gameType));
            if (string.IsNullOrWhiteSpace(uniqueDrawingId)) throw new ArgumentNullException(nameof(uniqueDrawingId));

            const long DEFAULT_TICKET_NUMBER = 0;
            var numericTicketNumber = DEFAULT_TICKET_NUMBER;
            var ticketNumberWithWagerNumber = ticketNumber.Split('-');
            var isTicketNumberWithWagerNumber = ticketNumberWithWagerNumber.Length == 2;
            if (!isTicketNumberWithWagerNumber && !String.IsNullOrWhiteSpace(ticketNumber) && !Int64.TryParse(ticketNumber, out numericTicketNumber)) throw new GameEngineException($"{nameof(ticketNumber)} cannot be greater than {long.MaxValue}");
            if (isTicketNumberWithWagerNumber && !Int64.TryParse(ticketNumberWithWagerNumber[0], out numericTicketNumber)) throw new GameEngineException($"{nameof(ticketNumber)} cannot be greater than {long.MaxValue}");

            var areAllAccountNumbersValid = String.IsNullOrWhiteSpace(accountNumber);
            var areAllTicketNumbersValid = String.IsNullOrWhiteSpace(ticketNumber);
            var areAllGameTypesValid = String.IsNullOrWhiteSpace(gameType) || String.IsNullOrWhiteSpace(GameTypeForQuery(gameType));
            var areAllDrawingIdValid = uniqueDrawingId == SELECTION_ALL;
            int numericUniqueDrawingId = 0;
            if (!areAllDrawingIdValid) if (!int.TryParse(uniqueDrawingId, out numericUniqueDrawingId)) throw new GameEngineException($"{nameof(uniqueDrawingId)} is not a number");
            var allPendingDraws = lotteries.PendingDrawsBy(domainIds);
            var filteredPendingDraws = new List<PendingDraw>();
            foreach (var pendingDraw in allPendingDraws)
            {
                var isDrawBetweenDates = pendingDraw.OnlyDate >= startDate && pendingDraw.OnlyDate <= endDate;
                if (!isDrawBetweenDates) continue;
                var isMatchingDrawingId = areAllDrawingIdValid || pendingDraw.UniqueDrawingId == numericUniqueDrawingId;
                if (!isMatchingDrawingId) continue;
                var isMatchingAccountNumber = areAllAccountNumbersValid || pendingDraw.ContainsOwnerWith(accountNumber);
                if (!isMatchingAccountNumber) continue;

                if (!isTicketNumberWithWagerNumber)
                {
                    var isMatchingTicketNumber = areAllTicketNumbersValid || pendingDraw.ContainsTicketWith(numericTicketNumber);
                    if (!isMatchingTicketNumber) continue;
                }
                else
                {
                    var isMatchingTicketNumber = areAllTicketNumbersValid || pendingDraw.ContainsTicketWith(ticketNumber);
                    if (!isMatchingTicketNumber) continue;
                }
                var isMatchingGameType = areAllGameTypesValid || LotteryGameTypeOf(pendingDraw.GameType) == GameTypeForQuery(gameType);
                if (!isMatchingGameType) continue;
                filteredPendingDraws.Add(pendingDraw);
            }

            if (filteredPendingDraws.Count == 0) return new TicketsPerPlayersEmpty();
            var report = new TicketsPerPlayersInPendingDraws(filteredPendingDraws, accountNumber, numericTicketNumber);
            return report;
        }

        internal TicketsPerPlayers TicketsPerPlayersInPendingKenoDrawings(DateTime startDate, DateTime endDate, string accountNumber, string ticketNumber, string domainIds)
        {
            if (startDate == default(DateTime)) throw new GameEngineException($"{nameof(startDate)} cannot have default value");
            if (endDate == default(DateTime)) throw new GameEngineException($"{nameof(endDate)} cannot have default value");
            if (startDate > endDate) throw new GameEngineException($"{nameof(startDate)} cannot be greater than {nameof(endDate)}");

            const long DEFAULT_TICKET_NUMBER = 0;
            var numericTicketNumber = DEFAULT_TICKET_NUMBER;
            if (!string.IsNullOrWhiteSpace(ticketNumber) && !Int64.TryParse(ticketNumber, out numericTicketNumber)) throw new GameEngineException($"{nameof(ticketNumber)} cannot be greater than {long.MaxValue}");

            var areAllAccountNumbersValid = string.IsNullOrWhiteSpace(accountNumber);
            var areAllTicketNumbersValid = string.IsNullOrWhiteSpace(ticketNumber);
            var allPendingDraws = lotteries.GetKeno().PendingKenoDrawsBy(domainIds);
            var filteredPendingDraws = new List<PendingKenoDraw>();
            foreach (var pendingDraw in allPendingDraws)
            {
                var isDrawBetweenDates = pendingDraw.OnlyDate >= startDate && pendingDraw.OnlyDate <= endDate;
                if (!isDrawBetweenDates) continue;
                var isMatchingAccountNumber = areAllAccountNumbersValid || pendingDraw.ContainsOwnerWith(accountNumber);
                if (!isMatchingAccountNumber) continue;
                var isMatchingTicketNumber = areAllTicketNumbersValid || pendingDraw.ContainsTicketWith(numericTicketNumber);
                if (!isMatchingTicketNumber) continue;
                filteredPendingDraws.Add(pendingDraw);
            }

            if (filteredPendingDraws.Count == 0) return new TicketsPerPlayersEmpty();
            var report = new TicketsPerPlayersInPendingDraws(filteredPendingDraws, accountNumber, numericTicketNumber);
            return report;
        }

        internal TicketsPerPlayers TicketsPerPlayerInPendingDrawing(Player player, DateTime drawDate, string drawingId, string gameType, string ticketNumber, string domainIds)
        {
            if (player == null) throw new ArgumentNullException(nameof(player));
            if (drawDate == default(DateTime)) throw new GameEngineException($"{nameof(drawDate)} cannot have default value");
            if (String.IsNullOrWhiteSpace(gameType)) throw new ArgumentNullException(nameof(gameType));
            if (string.IsNullOrWhiteSpace(drawingId)) throw new ArgumentNullException(nameof(drawingId));

            const long DEFAULT_TICKET_NUMBER = 0;
            var numericTicketNumber = DEFAULT_TICKET_NUMBER;
            var ticketNumberWithWagerNumber = ticketNumber.Split('-');
            var isTicketNumberWithWagerNumber = ticketNumberWithWagerNumber.Length == 2;
            if (!isTicketNumberWithWagerNumber && !String.IsNullOrWhiteSpace(ticketNumber) && !Int64.TryParse(ticketNumber, out numericTicketNumber)) throw new GameEngineException($"{nameof(ticketNumber)} cannot be greater than {long.MaxValue}");
            if (isTicketNumberWithWagerNumber && !Int64.TryParse(ticketNumberWithWagerNumber[0], out numericTicketNumber)) throw new GameEngineException($"{nameof(ticketNumber)} cannot be greater than {long.MaxValue}");

            var areAllTicketNumbersValid = String.IsNullOrWhiteSpace(ticketNumber);
            var areAllGameTypesValid = String.IsNullOrWhiteSpace(GameTypeForQuery(gameType));
            var areAllDrawingIdValid = drawingId == SELECTION_ALL;
            int numericDrawingId = 0;
            if (!areAllDrawingIdValid) if (!int.TryParse(drawingId, out numericDrawingId)) throw new GameEngineException($"{nameof(drawingId)} is not a number");
            var allPendingDraws = lotteries.PendingDrawsBy(domainIds);
            PendingDraw pendingDrawSelected = null;
            foreach (var pendingDraw in allPendingDraws)
            {
                var isMatchingDrawDate = pendingDraw.DrawDate == drawDate;
                var isMatchingDrawingId = areAllDrawingIdValid || pendingDraw.UniqueDrawingId == numericDrawingId;
                var isMatchingGameType = areAllGameTypesValid || LotteryGameTypeOf(pendingDraw.GameType) == GameTypeForQuery(gameType);
                var isMatchingAccountNumber = pendingDraw.ContainsOwnerWith(player.AccountNumber);
                var isMatchingTicketNumber = areAllTicketNumbersValid || (isTicketNumberWithWagerNumber ? pendingDraw.ContainsTicketWith(ticketNumber) : pendingDraw.ContainsTicketWith(numericTicketNumber));
                var isPendingDrawSelected = isMatchingDrawDate && isMatchingDrawingId && isMatchingGameType && isMatchingAccountNumber && isMatchingTicketNumber;
                if (isPendingDrawSelected)
                {
                    pendingDrawSelected = pendingDraw;
                    break;
                }
            }

            if (pendingDrawSelected == null) return new TicketsPerPlayersEmpty();
            var report = new TicketsPerPlayersInPendingDraws(new PendingDraw[] { pendingDrawSelected }, player.AccountNumber, numericTicketNumber);
            return report;
        }

        internal WagersPerPlayerInPendingDraw WagersPerPlayerInPendingDrawing(Player player, DateTime drawDate, string drawingId, string gameType, string ticketNumber, string domainIds)
        {
            var ticketsPerPlayers = TicketsPerPlayerInPendingDrawing(player, drawDate, drawingId, gameType, ticketNumber, domainIds);
            var report = new WagersPerPlayerInPendingDraw(ticketsPerPlayers, ticketNumber);
            return report;
        }

        internal WagersPerPlayerInPendingDraw WagersPerPlayerInPendingKenoDrawing(Player player, DateTime drawDate, string ticketNumber, string domainIds)
        {
            var ticketsPerPlayers = TicketsPerPlayersInPendingKenoDrawing(drawDate, player.AccountNumber, ticketNumber, domainIds);
            var report = new WagersPerPlayerInPendingDraw(ticketsPerPlayers, ticketNumber);
            return report;
        }

        internal WagersPerPlayerInPendingDraw WagersPerPlayerInPendingDrawing(DateTime startDate, DateTime endDate, string drawingId, string accountNumber, string gameType, string ticketNumber, string domainIds)
        {
            var ticketsPerPlayers = TicketsPerPlayersInPendingDrawings(startDate, endDate, drawingId, accountNumber, gameType, ticketNumber, domainIds);
            var report = new WagersPerPlayerInPendingDraw(ticketsPerPlayers, ticketNumber);
            return report;
        }

        internal WagersPerPlayerInPendingDraw WagersPerPlayerInPendingKenoDrawing(DateTime startDate, DateTime endDate, string accountNumber, string ticketNumber, string domainIds)
        {
            var ticketsPerPlayers = TicketsPerPlayersInPendingKenoDrawings(startDate, endDate, accountNumber, ticketNumber, domainIds);
            var report = new WagersPerPlayerInPendingDraw(ticketsPerPlayers, ticketNumber);
            return report;
        }

        internal IEnumerable<TicketRecord> MoneyInvested(DateTime startDate, DateTime endDate)
        {
            if (startDate == default(DateTime)) throw new GameEngineException($"{nameof(startDate)} cannot have default value");
            if (endDate == default(DateTime)) throw new GameEngineException($"{nameof(endDate)} cannot have default value");
            if (startDate > endDate) throw new GameEngineException($"{nameof(startDate)} cannot be greater than {nameof(endDate)}");

            var queryMaker = new QueryMakerOfHistoricalPicks(lotteries.Company);
            var result = queryMaker.GenerateMoneyInvestedReport(startDate, endDate);
            return result;
        }

        internal CountsOfTicketsPerDayAndGameReport CountsOfTicketsPerDayAndGame(DateTime startDate, DateTime endDate)
        {
            if (startDate == default(DateTime)) throw new GameEngineException($"{nameof(startDate)} cannot have default value");
            if (endDate == default(DateTime)) throw new GameEngineException($"{nameof(endDate)} cannot have default value");
            if (startDate > endDate) throw new GameEngineException($"{nameof(startDate)} cannot be greater than {nameof(endDate)}");

            var queryMaker = new QueryMakerOfHistoricalPicks(lotteries.Company);
            var result = queryMaker.GenerateCountsOfTicketsPerDayAndGameReport(startDate, endDate);
            return result;
        }

        internal SalesAndPrizesPerDayReport SalesAndPrizesPerDay(DateTime startDate, DateTime endDate)
        {
            if (startDate == default(DateTime)) throw new GameEngineException($"{nameof(startDate)} cannot have default value");
            if (endDate == default(DateTime)) throw new GameEngineException($"{nameof(endDate)} cannot have default value");
            if (startDate > endDate) throw new GameEngineException($"{nameof(startDate)} cannot be greater than {nameof(endDate)}");

            var queryMaker = new QueryMakerOfHistoricalPicks(lotteries.Company);
            var result = queryMaker.GenerateSalesAndPrizesPerDayReport(startDate, endDate);
            return result;
        }

        internal WinnerInfoByPrize WinnerTicketBy(string state, DateTime drawDate, string ticketNumber)
        {
            if (string.IsNullOrWhiteSpace(state)) throw new ArgumentNullException(nameof(state));
            if (drawDate == default(DateTime)) throw new GameEngineException("Date cannot have default value");
            if (drawDate.Second != 0 || drawDate.Millisecond != 0) throw new GameEngineException("Date of Lottery drawings can not have seconds");
            if (string.IsNullOrWhiteSpace(ticketNumber)) throw new ArgumentNullException(nameof(ticketNumber));

            var queryMaker = new Games.Lotto.QueryMakerOfHistoricalPicks(lotteries.Company);
            WinnerInfoByPrize winnerInfoByPrize;
            var winner = queryMaker.WinnerTicketBy(state, drawDate, ticketNumber);
            if (winner != null)
            {
                var result = TransformToWinnerInfoByPrize(new WinnerInfo[] { winner });
                winnerInfoByPrize = result.FirstOrDefault();
                if (winnerInfoByPrize == null) winnerInfoByPrize = new WinnerInfoByPrizeEmpty();
            }
            else
            {
                winnerInfoByPrize = new WinnerInfoByPrizeEmpty();
            }
            return winnerInfoByPrize;
        }

        internal IEnumerable<WinnerInfoByPrize> GetPlayedTicketsBy(string ticketNumber)
        {
            if (string.IsNullOrWhiteSpace(ticketNumber)) throw new ArgumentNullException(nameof(ticketNumber));

            var queryMaker = new QueryMakerOfHistoricalPicks(lotteries.Company);
            var winners = queryMaker.GetPlayedTicketsBy(ticketNumber);
            var result = TransformFakeWinnerInfoToInfoByPrize(winners);
            return result;
        }

        internal FireballPrizeDetailReport GenerateFireballPrizeDetailReport(string gameType, string numbers, decimal betAmount, string winnerNumber, int fireballNumber)
        {
            if (string.IsNullOrWhiteSpace(gameType)) throw new ArgumentNullException(nameof(gameType));
            if (string.IsNullOrWhiteSpace(numbers)) throw new ArgumentNullException(nameof(numbers));
            if (string.IsNullOrWhiteSpace(winnerNumber)) throw new ArgumentNullException(nameof(winnerNumber));
            if (betAmount <= 0) throw new GameEngineException($"{nameof(betAmount)} must be greater than 0");
            if (numbers.Length < 2 || numbers.Length > 5) throw new GameEngineException($"{nameof(numbers)} {numbers} is not valid");
            if (winnerNumber.Length < 2 || winnerNumber.Length > 5) throw new GameEngineException($"{nameof(winnerNumber)} {winnerNumber} is not valid");
            if (numbers.Length != winnerNumber.Length) throw new GameEngineException($"{nameof(numbers)} {numbers} and {nameof(winnerNumber)} {winnerNumber} must have the same size");
            if (fireballNumber < 0 || fireballNumber > 9) new GameEngineException($"{nameof(fireballNumber)} {fireballNumber} is not valid");

            var report = new FireballPrizeDetailReport(); 
            report.CalculatePrizes(gameType, numbers, betAmount, winnerNumber, fireballNumber);
            return report;
        }

        static void CheckIfItIsOnlyOnePrize(IEnumerable<MessageInfoByPrize> ticketsByPrize)
        {
            if (ticketsByPrize.Count() == 1)
            {
                foreach (var ticketByPrize in ticketsByPrize)
                {
                    ticketByPrize.HasOnlyOnePrize = true;
                }
            }
        }

        internal static IEnumerable<WinnerInfoByPrize> TransformFakeWinnerInfoToInfoByPrize(IEnumerable<WinnerInfo> winners)
        {
            var tickets = new List<WinnerInfoByPrize>();
            string type = "";
            foreach (var winnerInfo in winners)
            {
                type = winnerInfo.GameType();
                switch (type)
                {
                    case "TicketPick2Straight":
                        {
                            var winnerInfoByPrize1 = new WinnerInfoByPrizePick2Straight(winnerInfo);
                            winnerInfoByPrize1.HasOnlyOnePrize = true;
                            tickets.Add(winnerInfoByPrize1);
                            break;
                        }
                    case "TicketPick3Straight":
                        {
                            var winnerInfoByPrize1 = new WinnerInfoByPrizePick3Straight(winnerInfo);
                            winnerInfoByPrize1.HasOnlyOnePrize = true;
                            tickets.Add(winnerInfoByPrize1);
                            break;
                        }
                    case "TicketPick4Straight":
                        {
                            var winnerInfoByPrize1 = new WinnerInfoByPrizePick4Straight(winnerInfo);
                            winnerInfoByPrize1.HasOnlyOnePrize = true;
                            tickets.Add(winnerInfoByPrize1);
                            break;
                        }
                    case "TicketPick5Straight":
                        {
                            var winnerInfoByPrize1 = new WinnerInfoByPrizePick5Straight(winnerInfo);
                            winnerInfoByPrize1.HasOnlyOnePrize = true;
                            tickets.Add(winnerInfoByPrize1);
                            break;
                        }
                    case "TicketPick2Boxed":
                        {
                            var winnerInfoByPrize1 = new WinnerInfoByPrizePick2Boxed(winnerInfo, PrizesPicks.PRIZE_CRITERIA_ALL_NUMBERS_ARE_THE_SAME);
                            var winnerInfoByPrize2 = new WinnerInfoByPrizePick2Boxed(winnerInfo, PrizesPicks.PRIZE_CRITERIA_2_WAY);

                            tickets.Add(winnerInfoByPrize1);
                            tickets.Add(winnerInfoByPrize2);
                            CheckIfItIsOnlyOnePrize(tickets);
                            tickets = tickets.Where(x => x.Count > 0).ToList();
                            break;
                        }
                    case "TicketPick3Boxed":
                        {
                            var winnerInfoByPrize1 = new WinnerInfoByPrizePick3Boxed(winnerInfo, PrizesPicks.PRIZE_CRITERIA_ALL_NUMBERS_ARE_THE_SAME);
                            var winnerInfoByPrize2 = new WinnerInfoByPrizePick3Boxed(winnerInfo, PrizesPicks.PRIZE_CRITERIA_6_WAY);
                            var winnerInfoByPrize3 = new WinnerInfoByPrizePick3Boxed(winnerInfo, PrizesPicks.PRIZE_CRITERIA_3_WAY);

                            tickets.Add(winnerInfoByPrize1);
                            tickets.Add(winnerInfoByPrize2);
                            tickets.Add(winnerInfoByPrize3);

                            CheckIfItIsOnlyOnePrize(tickets);
                            tickets = tickets.Where(x => x.Count > 0).ToList();
                            break;
                        }
                    case "TicketPick4Boxed":
                        {
                            var winnerInfoByPrize1 = new WinnerInfoByPrizePick4Boxed(winnerInfo, PrizesPicks.PRIZE_CRITERIA_ALL_NUMBERS_ARE_THE_SAME);
                            var winnerInfoByPrize2 = new WinnerInfoByPrizePick4Boxed(winnerInfo, PrizesPicks.PRIZE_CRITERIA_4_WAY);
                            var winnerInfoByPrize3 = new WinnerInfoByPrizePick4Boxed(winnerInfo, PrizesPicks.PRIZE_CRITERIA_6_WAY);
                            var winnerInfoByPrize4 = new WinnerInfoByPrizePick4Boxed(winnerInfo, PrizesPicks.PRIZE_CRITERIA_12_WAY);
                            var winnerInfoByPrize5 = new WinnerInfoByPrizePick4Boxed(winnerInfo, PrizesPicks.PRIZE_CRITERIA_24_WAY);

                            tickets.Add(winnerInfoByPrize1);
                            tickets.Add(winnerInfoByPrize2);
                            tickets.Add(winnerInfoByPrize3);
                            tickets.Add(winnerInfoByPrize4);
                            tickets.Add(winnerInfoByPrize5);
                            
                            CheckIfItIsOnlyOnePrize(tickets);
                            tickets = tickets.Where(x => x.Count > 0).ToList();
                            break;
                        }
                    case "TicketPick5Boxed":
                        {
                            var winnerInfoByPrize1 = new WinnerInfoByPrizePick5Boxed(winnerInfo, PrizesPicks.PRIZE_CRITERIA_ALL_NUMBERS_ARE_THE_SAME);
                            var winnerInfoByPrize2 = new WinnerInfoByPrizePick5Boxed(winnerInfo, PrizesPicks.PRIZE_CRITERIA_5_WAY);
                            var winnerInfoByPrize3 = new WinnerInfoByPrizePick5Boxed(winnerInfo, PrizesPicks.PRIZE_CRITERIA_10_WAY);
                            var winnerInfoByPrize4 = new WinnerInfoByPrizePick5Boxed(winnerInfo, PrizesPicks.PRIZE_CRITERIA_20_WAY);
                            var winnerInfoByPrize5 = new WinnerInfoByPrizePick5Boxed(winnerInfo, PrizesPicks.PRIZE_CRITERIA_30_WAY);
                            var winnerInfoByPrize6 = new WinnerInfoByPrizePick5Boxed(winnerInfo, PrizesPicks.PRIZE_CRITERIA_60_WAY);
                            var winnerInfoByPrize7 = new WinnerInfoByPrizePick5Boxed(winnerInfo, PrizesPicks.PRIZE_CRITERIA_120_WAY);

                            tickets.Add(winnerInfoByPrize1);
                            tickets.Add(winnerInfoByPrize2);
                            tickets.Add(winnerInfoByPrize3);
                            tickets.Add(winnerInfoByPrize4);
                            tickets.Add(winnerInfoByPrize5);
                            tickets.Add(winnerInfoByPrize6);
                            tickets.Add(winnerInfoByPrize7);
                            
                            CheckIfItIsOnlyOnePrize(tickets);
                            tickets = tickets.Where(x => x.Count > 0).ToList();
                            break;
                        }
                    case "TicketPowerBallSingle":
                    case "TicketPowerBallPowerPlay":
                        {
                            var winnerInfoByPrize1 = new WinnerInfoByPrizePowerball(winnerInfo);
                            winnerInfoByPrize1.HasOnlyOnePrize = true;
                            tickets.Add(winnerInfoByPrize1);
                            break;
                        }
                    default:
                        throw new Exception($"The type {type} is not valid.");
                }
            }

            return tickets;
        }

        internal static IEnumerable<WinnerInfoByPrize> TransformToWinnerInfoByPrize(IEnumerable<WinnerInfo> winners)
        {
            var tickets = new List<WinnerInfoByPrize>();
            string type = "";
            foreach (var winnerInfo in winners)
            {
                type = winnerInfo.GameType();
                switch (type)
                {
                    case "TicketPick2Straight":
                        {
                            var winnerInfoByPrize1 = new WinnerInfoByPrizePick2Straight(winnerInfo);
                            winnerInfoByPrize1.HasOnlyOnePrize = true;
                            tickets.Add(winnerInfoByPrize1);
                            break;
                        }
                    case "TicketPick3Straight":
                        {
                            var winnerInfoByPrize1 = new WinnerInfoByPrizePick3Straight(winnerInfo);
                            winnerInfoByPrize1.HasOnlyOnePrize = true;
                            tickets.Add(winnerInfoByPrize1);
                            break;
                        }
                    case "TicketPick4Straight":
                        {
                            var winnerInfoByPrize1 = new WinnerInfoByPrizePick4Straight(winnerInfo);
                            winnerInfoByPrize1.HasOnlyOnePrize = true;
                            tickets.Add(winnerInfoByPrize1);
                            break;
                        }
                    case "TicketPick5Straight":
                        {
                            var winnerInfoByPrize1 = new WinnerInfoByPrizePick5Straight(winnerInfo);
                            winnerInfoByPrize1.HasOnlyOnePrize = true;
                            tickets.Add(winnerInfoByPrize1);
                            break;
                        }
                    case "TicketPick2Boxed":
                        {
                            var winnerInfoByPrize1 = new WinnerInfoByPrizePick2Boxed(winnerInfo, PrizesPicks.PRIZE_CRITERIA_ALL_NUMBERS_ARE_THE_SAME);
                            var winnerInfoByPrize2 = new WinnerInfoByPrizePick2Boxed(winnerInfo, PrizesPicks.PRIZE_CRITERIA_2_WAY);

                            if (winnerInfoByPrize1.IsWinner())
                            {
                                tickets.Add(winnerInfoByPrize1);
                            }
                            if (winnerInfoByPrize2.Count > 0)
                            {
                                tickets.Add(winnerInfoByPrize2);
                            }
                            CheckIfItIsOnlyOnePrize(tickets);
                            tickets = tickets.Where(x => x.IsWinner()).ToList();
                            break;
                        }
                    case "TicketPick3Boxed":
                        {
                            var winnerInfoByPrize1 = new WinnerInfoByPrizePick3Boxed(winnerInfo, PrizesPicks.PRIZE_CRITERIA_ALL_NUMBERS_ARE_THE_SAME);
                            var winnerInfoByPrize2 = new WinnerInfoByPrizePick3Boxed(winnerInfo, PrizesPicks.PRIZE_CRITERIA_6_WAY);
                            var winnerInfoByPrize3 = new WinnerInfoByPrizePick3Boxed(winnerInfo, PrizesPicks.PRIZE_CRITERIA_3_WAY);

                            if (winnerInfoByPrize1.IsWinner())
                            {
                                tickets.Add(winnerInfoByPrize1);
                            }
                            if (winnerInfoByPrize2.Count > 0)
                            {
                                tickets.Add(winnerInfoByPrize2);
                            }
                            if (winnerInfoByPrize3.Count > 0)
                            {
                                tickets.Add(winnerInfoByPrize3);
                            }
                            CheckIfItIsOnlyOnePrize(tickets);
                            tickets = tickets.Where(x => x.IsWinner()).ToList();
                            break;
                        }
                    case "TicketPick4Boxed":
                        {
                            var winnerInfoByPrize1 = new WinnerInfoByPrizePick4Boxed(winnerInfo, PrizesPicks.PRIZE_CRITERIA_ALL_NUMBERS_ARE_THE_SAME);
                            var winnerInfoByPrize2 = new WinnerInfoByPrizePick4Boxed(winnerInfo, PrizesPicks.PRIZE_CRITERIA_4_WAY);
                            var winnerInfoByPrize3 = new WinnerInfoByPrizePick4Boxed(winnerInfo, PrizesPicks.PRIZE_CRITERIA_6_WAY);
                            var winnerInfoByPrize4 = new WinnerInfoByPrizePick4Boxed(winnerInfo, PrizesPicks.PRIZE_CRITERIA_12_WAY);
                            var winnerInfoByPrize5 = new WinnerInfoByPrizePick4Boxed(winnerInfo, PrizesPicks.PRIZE_CRITERIA_24_WAY);

                            if (winnerInfoByPrize1.IsWinner())
                            {
                                tickets.Add(winnerInfoByPrize1);
                            }
                            if (winnerInfoByPrize2.Count > 0)
                            {
                                tickets.Add(winnerInfoByPrize2);
                            }
                            if (winnerInfoByPrize3.Count > 0)
                            {
                                tickets.Add(winnerInfoByPrize3);
                            }
                            if (winnerInfoByPrize4.Count > 0)
                            {
                                tickets.Add(winnerInfoByPrize4);
                            }
                            if (winnerInfoByPrize5.Count > 0)
                            {
                                tickets.Add(winnerInfoByPrize5);
                            }
                            CheckIfItIsOnlyOnePrize(tickets);
                            tickets = tickets.Where(x => x.IsWinner()).ToList();
                            break;
                        }
                    case "TicketPick5Boxed":
                        {
                            var winnerInfoByPrize1 = new WinnerInfoByPrizePick5Boxed(winnerInfo, PrizesPicks.PRIZE_CRITERIA_ALL_NUMBERS_ARE_THE_SAME);
                            var winnerInfoByPrize2 = new WinnerInfoByPrizePick5Boxed(winnerInfo, PrizesPicks.PRIZE_CRITERIA_5_WAY);
                            var winnerInfoByPrize3 = new WinnerInfoByPrizePick5Boxed(winnerInfo, PrizesPicks.PRIZE_CRITERIA_10_WAY);
                            var winnerInfoByPrize4 = new WinnerInfoByPrizePick5Boxed(winnerInfo, PrizesPicks.PRIZE_CRITERIA_20_WAY);
                            var winnerInfoByPrize5 = new WinnerInfoByPrizePick5Boxed(winnerInfo, PrizesPicks.PRIZE_CRITERIA_30_WAY);
                            var winnerInfoByPrize6 = new WinnerInfoByPrizePick5Boxed(winnerInfo, PrizesPicks.PRIZE_CRITERIA_60_WAY);
                            var winnerInfoByPrize7 = new WinnerInfoByPrizePick5Boxed(winnerInfo, PrizesPicks.PRIZE_CRITERIA_120_WAY);

                            if (winnerInfoByPrize1.IsWinner())
                            {
                                tickets.Add(winnerInfoByPrize1);
                            }
                            if (winnerInfoByPrize2.Count > 0)
                            {
                                tickets.Add(winnerInfoByPrize2);
                            }
                            if (winnerInfoByPrize3.Count > 0)
                            {
                                tickets.Add(winnerInfoByPrize3);
                            }
                            if (winnerInfoByPrize4.Count > 0)
                            {
                                tickets.Add(winnerInfoByPrize4);
                            }
                            if (winnerInfoByPrize5.Count > 0)
                            {
                                tickets.Add(winnerInfoByPrize5);
                            }
                            if (winnerInfoByPrize6.Count > 0)
                            {
                                tickets.Add(winnerInfoByPrize6);
                            }
                            if (winnerInfoByPrize7.Count > 0)
                            {
                                tickets.Add(winnerInfoByPrize7);
                            }
                            CheckIfItIsOnlyOnePrize(tickets);
                            tickets = tickets.Where(x => x.IsWinner()).ToList();
                            break;
                        }
                    case "TicketPowerBallSingle":
                    case "TicketPowerBallPowerPlay":
                        {
                            var winnerInfoByPrize1 = new WinnerInfoByPrizePowerball(winnerInfo);
                            winnerInfoByPrize1.HasOnlyOnePrize = true;
                            tickets.Add(winnerInfoByPrize1);
                            break;
                        }
                    default:
                        throw new Exception($"The type {type} is not valid.");
                }
            }

            return tickets;
        }

        internal static IEnumerable<LoserInfoByPrize> TransformToLoserInfoByPrize(IEnumerable<LoserInfo> losers)
        {
            var tickets = new List<LoserInfoByPrize>();
            string type = "";
            foreach (var loserInfo in losers)
            {
                type = loserInfo.GameType();
                switch (type)
                {
                    case "TicketPick2Straight":
                        {
                            var loserInfoByPrize = new LoserInfoByPrizePick2Straight(loserInfo);
                            loserInfoByPrize.HasOnlyOnePrize = true;
                            tickets.Add(loserInfoByPrize);
                            break;
                        }
                    case "TicketPick3Straight":
                        {
                            var loserInfoByPrize = new LoserInfoByPrizePick3Straight(loserInfo);
                            loserInfoByPrize.HasOnlyOnePrize = true;
                            tickets.Add(loserInfoByPrize);
                            break;
                        }
                    case "TicketPick4Straight":
                        {
                            var loserInfoByPrize = new LoserInfoByPrizePick4Straight(loserInfo);
                            loserInfoByPrize.HasOnlyOnePrize = true;
                            tickets.Add(loserInfoByPrize);
                            break;
                        }
                    case "TicketPick5Straight":
                        {
                            var loserInfoByPrize = new LoserInfoByPrizePick5Straight(loserInfo);
                            loserInfoByPrize.HasOnlyOnePrize = true;
                            tickets.Add(loserInfoByPrize);
                            break;
                        }
                    case "TicketPick2Boxed":
                        {
                            var loserInfoByPrize1 = new LoserInfoByPrizePick2Boxed(loserInfo, PrizesPicks.PRIZE_CRITERIA_ALL_NUMBERS_ARE_THE_SAME);
                            var loserInfoByPrize2 = new LoserInfoByPrizePick3Boxed(loserInfo, PrizesPicks.PRIZE_CRITERIA_2_WAY);

                            tickets.Add(loserInfoByPrize1);
                            tickets.Add(loserInfoByPrize2);
                            tickets = tickets.Where(x => x.Count > 0).ToList();
                            CheckIfItIsOnlyOnePrize(tickets);
                            break;
                        }
                    case "TicketPick3Boxed":
                        {
                            var loserInfoByPrize1 = new LoserInfoByPrizePick3Boxed(loserInfo, PrizesPicks.PRIZE_CRITERIA_ALL_NUMBERS_ARE_THE_SAME);
                            var loserInfoByPrize2 = new LoserInfoByPrizePick3Boxed(loserInfo, PrizesPicks.PRIZE_CRITERIA_6_WAY);
                            var loserInfoByPrize3 = new LoserInfoByPrizePick3Boxed(loserInfo, PrizesPicks.PRIZE_CRITERIA_3_WAY);

                            tickets.Add(loserInfoByPrize1);
                            tickets.Add(loserInfoByPrize2);
                            tickets.Add(loserInfoByPrize3);
                            tickets = tickets.Where(x => x.Count > 0).ToList();
                            CheckIfItIsOnlyOnePrize(tickets);
                            break;
                        }
                    case "TicketPick4Boxed":
                        {
                            var loserInfoByPrize1 = new LoserInfoByPrizePick4Boxed(loserInfo, PrizesPicks.PRIZE_CRITERIA_ALL_NUMBERS_ARE_THE_SAME);
                            var loserInfoByPrize2 = new LoserInfoByPrizePick4Boxed(loserInfo, PrizesPicks.PRIZE_CRITERIA_4_WAY);
                            var loserInfoByPrize3 = new LoserInfoByPrizePick4Boxed(loserInfo, PrizesPicks.PRIZE_CRITERIA_6_WAY);
                            var loserInfoByPrize4 = new LoserInfoByPrizePick4Boxed(loserInfo, PrizesPicks.PRIZE_CRITERIA_12_WAY);
                            var loserInfoByPrize5 = new LoserInfoByPrizePick4Boxed(loserInfo, PrizesPicks.PRIZE_CRITERIA_24_WAY);

                            tickets.Add(loserInfoByPrize1);
                            tickets.Add(loserInfoByPrize2);
                            tickets.Add(loserInfoByPrize3);
                            tickets.Add(loserInfoByPrize4);
                            tickets.Add(loserInfoByPrize5);
                            tickets = tickets.Where(x => x.Count > 0).ToList();
                            CheckIfItIsOnlyOnePrize(tickets);
                            break;
                        }
                    case "TicketPick5Boxed":
                        {
                            var loserInfoByPrize1 = new LoserInfoByPrizePick5Boxed(loserInfo, PrizesPicks.PRIZE_CRITERIA_ALL_NUMBERS_ARE_THE_SAME);
                            var loserInfoByPrize2 = new LoserInfoByPrizePick5Boxed(loserInfo, PrizesPicks.PRIZE_CRITERIA_5_WAY);
                            var loserInfoByPrize3 = new LoserInfoByPrizePick5Boxed(loserInfo, PrizesPicks.PRIZE_CRITERIA_10_WAY);
                            var loserInfoByPrize4 = new LoserInfoByPrizePick5Boxed(loserInfo, PrizesPicks.PRIZE_CRITERIA_20_WAY);
                            var loserInfoByPrize5 = new LoserInfoByPrizePick5Boxed(loserInfo, PrizesPicks.PRIZE_CRITERIA_30_WAY);
                            var loserInfoByPrize6 = new LoserInfoByPrizePick5Boxed(loserInfo, PrizesPicks.PRIZE_CRITERIA_60_WAY);
                            var loserInfoByPrize7 = new LoserInfoByPrizePick5Boxed(loserInfo, PrizesPicks.PRIZE_CRITERIA_120_WAY);

                            tickets.Add(loserInfoByPrize1);
                            tickets.Add(loserInfoByPrize2);
                            tickets.Add(loserInfoByPrize3);
                            tickets.Add(loserInfoByPrize4);
                            tickets.Add(loserInfoByPrize5);
                            tickets.Add(loserInfoByPrize6);
                            tickets.Add(loserInfoByPrize7);
                            tickets = tickets.Where(x => x.Count > 0).ToList();
                            CheckIfItIsOnlyOnePrize(tickets);
                            break;
                        }
                    case "TicketPowerBallSingle":
                    case "TicketPowerBallPowerPlay":
                        {
                            var loserInfoByPrize = new LoserInfoByPrizePowerball(loserInfo);
                            loserInfoByPrize.HasOnlyOnePrize = true;
                            tickets.Add(loserInfoByPrize);
                            break;
                        }
                    default:
                        throw new Exception($"The type {type} is not valid.");
                }
            }

            return tickets;
        }

        internal static IEnumerable<NoActionInfoByPrize> TransformToNoActionInfoByPrize(IEnumerable<NoActionInfo> noActions)
        {
            var tickets = new List<NoActionInfoByPrize>();
            string type = "";
            foreach (var noActionInfo in noActions)
            {
                type = noActionInfo.GameType();
                switch (type)
                {
                    case "TicketPick2Straight":
                        {
                            var noActionInfoByPrize = new NoActionInfoByPrizePick2Straight(noActionInfo);
                            noActionInfoByPrize.HasOnlyOnePrize = true;
                            tickets.Add(noActionInfoByPrize);
                            break;
                        }
                    case "TicketPick3Straight":
                        {
                            var noActionInfoByPrize = new NoActionInfoByPrizePick3Straight(noActionInfo);
                            noActionInfoByPrize.HasOnlyOnePrize = true;
                            tickets.Add(noActionInfoByPrize);
                            break;
                        }
                    case "TicketPick4Straight":
                        {
                            var noActionInfoByPrize = new NoActionInfoByPrizePick4Straight(noActionInfo);
                            noActionInfoByPrize.HasOnlyOnePrize = true;
                            tickets.Add(noActionInfoByPrize);
                            break;
                        }
                    case "TicketPick5Straight":
                        {
                            var noActionInfoByPrize = new NoActionInfoByPrizePick5Straight(noActionInfo);
                            noActionInfoByPrize.HasOnlyOnePrize = true;
                            tickets.Add(noActionInfoByPrize);
                            break;
                        }
                    case "TicketPick2Boxed":
                        {
                            var noActionInfoByPrize1 = new NoActionInfoByPrizePick2Boxed(noActionInfo, PrizesPicks.PRIZE_CRITERIA_ALL_NUMBERS_ARE_THE_SAME);
                            var noActionInfoByPrize2 = new NoActionInfoByPrizePick2Boxed(noActionInfo, PrizesPicks.PRIZE_CRITERIA_2_WAY);

                            tickets.Add(noActionInfoByPrize1);
                            tickets.Add(noActionInfoByPrize2);
                            tickets = tickets.Where(x => x.Count > 0).ToList();
                            CheckIfItIsOnlyOnePrize(tickets);
                            break;
                        }
                    case "TicketPick3Boxed":
                        {
                            var noActionInfoByPrize1 = new NoActionInfoByPrizePick3Boxed(noActionInfo, PrizesPicks.PRIZE_CRITERIA_ALL_NUMBERS_ARE_THE_SAME);
                            var noActionInfoByPrize2 = new NoActionInfoByPrizePick3Boxed(noActionInfo, PrizesPicks.PRIZE_CRITERIA_6_WAY);
                            var noActionInfoByPrize3 = new NoActionInfoByPrizePick3Boxed(noActionInfo, PrizesPicks.PRIZE_CRITERIA_3_WAY);

                            tickets.Add(noActionInfoByPrize1);
                            tickets.Add(noActionInfoByPrize2);
                            tickets.Add(noActionInfoByPrize3);
                            tickets = tickets.Where(x => x.Count > 0).ToList();
                            CheckIfItIsOnlyOnePrize(tickets);
                            break;
                        }
                    case "TicketPick4Boxed":
                        {
                            var noActionInfoByPrize1 = new NoActionInfoByPrizePick4Boxed(noActionInfo, PrizesPicks.PRIZE_CRITERIA_ALL_NUMBERS_ARE_THE_SAME);
                            var noActionInfoByPrize2 = new NoActionInfoByPrizePick4Boxed(noActionInfo, PrizesPicks.PRIZE_CRITERIA_4_WAY);
                            var noActionInfoByPrize3 = new NoActionInfoByPrizePick4Boxed(noActionInfo, PrizesPicks.PRIZE_CRITERIA_6_WAY);
                            var noActionInfoByPrize4 = new NoActionInfoByPrizePick4Boxed(noActionInfo, PrizesPicks.PRIZE_CRITERIA_12_WAY);
                            var noActionInfoByPrize5 = new NoActionInfoByPrizePick4Boxed(noActionInfo, PrizesPicks.PRIZE_CRITERIA_24_WAY);

                            tickets.Add(noActionInfoByPrize1);
                            tickets.Add(noActionInfoByPrize2);
                            tickets.Add(noActionInfoByPrize3);
                            tickets.Add(noActionInfoByPrize4);
                            tickets.Add(noActionInfoByPrize5);
                            tickets = tickets.Where(x => x.Count > 0).ToList();
                            CheckIfItIsOnlyOnePrize(tickets);
                            break;
                        }
                    case "TicketPick5Boxed":
                        {
                            var noActionInfoByPrize1 = new NoActionInfoByPrizePick5Boxed(noActionInfo, PrizesPicks.PRIZE_CRITERIA_ALL_NUMBERS_ARE_THE_SAME);
                            var noActionInfoByPrize2 = new NoActionInfoByPrizePick5Boxed(noActionInfo, PrizesPicks.PRIZE_CRITERIA_5_WAY);
                            var noActionInfoByPrize3 = new NoActionInfoByPrizePick5Boxed(noActionInfo, PrizesPicks.PRIZE_CRITERIA_10_WAY);
                            var noActionInfoByPrize4 = new NoActionInfoByPrizePick5Boxed(noActionInfo, PrizesPicks.PRIZE_CRITERIA_20_WAY);
                            var noActionInfoByPrize5 = new NoActionInfoByPrizePick5Boxed(noActionInfo, PrizesPicks.PRIZE_CRITERIA_30_WAY);
                            var noActionInfoByPrize6 = new NoActionInfoByPrizePick5Boxed(noActionInfo, PrizesPicks.PRIZE_CRITERIA_60_WAY);
                            var noActionInfoByPrize7 = new NoActionInfoByPrizePick5Boxed(noActionInfo, PrizesPicks.PRIZE_CRITERIA_120_WAY);

                            tickets.Add(noActionInfoByPrize1);
                            tickets.Add(noActionInfoByPrize2);
                            tickets.Add(noActionInfoByPrize3);
                            tickets.Add(noActionInfoByPrize4);
                            tickets.Add(noActionInfoByPrize5);
                            tickets.Add(noActionInfoByPrize6);
                            tickets.Add(noActionInfoByPrize7);
                            tickets = tickets.Where(x => x.Count > 0).ToList();
                            CheckIfItIsOnlyOnePrize(tickets);
                            break;
                        }
                    case "TicketPowerBallSingle":
                    case "TicketPowerBallPowerPlay":
                        {
                            var noActionInfoByPrize = new NoActionInfoByPrizePick5Straight(noActionInfo);
                            noActionInfoByPrize.HasOnlyOnePrize = true;
                            tickets.Add(noActionInfoByPrize);
                            break;
                        }
                    default:
                        throw new Exception($"The type {type} is not valid.");
                }
            }

            return tickets;
        }
    }

    internal abstract class DrawingsSummaryReport : Objeto
    {
        internal int TotalPlayers { get; private protected set; }

        internal int TotalWinners { get; private protected set; }

        internal int TotalTickets { get; private protected set; }

        internal decimal TotalTicketAmount { get; private protected set; }

        internal decimal TotalPrize { get; private protected set; }

        internal decimal Profit { get; private protected set; }

        internal bool IsAlreadyGradedWithTheSameNumbers { get; private protected set; }

        internal void JoinReport(DrawingsSummaryReport drawingsSummary)
        {
            if (drawingsSummary == null) throw new ArgumentNullException(nameof(drawingsSummary));

            TotalPlayers += drawingsSummary.TotalPlayers;
            TotalWinners += drawingsSummary.TotalWinners;
            TotalTickets += drawingsSummary.TotalTickets;
            TotalTicketAmount += drawingsSummary.TotalTicketAmount;
            TotalPrize += drawingsSummary.TotalPrize;
            Profit += drawingsSummary.Profit;
        }
    }

    internal class EmptyDrawingsSummaryReport : DrawingsSummaryReport
    {
        internal bool WithFireBall { get; private set; }
    }

    internal class PicksDrawingsSummaryReport : DrawingsSummaryReport
    {
        internal int Fireball { get; private set; }
        internal bool WithFireBall { get; private set; }

        private readonly decimal totalTicketAmountForLR;
        
        internal PicksDrawingsSummaryReport(bool isAlreadyGradedWithTheSameNumbers)
        {
            IsAlreadyGradedWithTheSameNumbers = isAlreadyGradedWithTheSameNumbers;
        }

        internal PicksDrawingsSummaryReport(TicketsOfDraw ticketsOfDraw)
        {
            var ticketsWithoutNoAction = ticketsOfDraw.TicketsWithoutNoAction();
            var players = new HashSet<Player>();
            var totalTicketAmountGraded = 0m;
            foreach (var ticket in ticketsWithoutNoAction)
            {
                var ticketAmount = ticket.TicketAmount();
                players.Add(ticket.Player);
                if (ticket.IsWinner())
                {
                    TotalWinners++;
                }
                TotalTickets += ticket.CountWagers;
                TotalTicketAmount += ticketAmount;
                if (!ticket.IsRegraded())
				{
                    totalTicketAmountGraded += ticketAmount;
                    if (ticket.WasPurchasedForFree)
                    {
                        totalTicketAmountForLR += ticketAmount;
                    }
                }
            }
            TotalPlayers = players.Count;
            TotalPrize = ticketsOfDraw.TotalPrize;
            Profit = totalTicketAmountGraded - totalTicketAmountForLR - TotalPrize;
            WithFireBall = ticketsWithoutNoAction.Any() ? ticketsWithoutNoAction.First().BelongsToFireBallDraw && ticketsWithoutNoAction.First().Draw.IsLotteryPickWithFireBall : false;
            Fireball = WithFireBall ? ticketsWithoutNoAction.First().Draw.FireBallNumber : LotteryDraw.WITHOUT_FIREBALL;
        }
    }

    internal class KenoDrawingsSummaryReport : DrawingsSummaryReport
    {
        internal decimal TotalNumbersPrize { get; private set; }

        internal decimal TotalMultiplierPrize { get; private set; }

        internal decimal TotalBulleyePrize { get; private set; }

        internal KenoDrawingsSummaryReport(bool isAlreadyGradedWithTheSameNumbers)
        {
            IsAlreadyGradedWithTheSameNumbers = isAlreadyGradedWithTheSameNumbers;
        }

        internal KenoDrawingsSummaryReport(TicketsOfDraw ticketsOfDraw)
        {
            var ticketsWithoutNoAction = ticketsOfDraw.TicketsWithoutNoAction();
            var players = new HashSet<Player>();
            var totalTicketAmountGraded = 0m;
            foreach (TicketKeno ticket in ticketsWithoutNoAction)
            {
                var ticketAmount = ticket.TicketAmount();
                players.Add(ticket.Player);
                if (ticket.IsWinner())
                {
                    TotalWinners++;
                }
                TotalTickets += ticket.CountWagers;
                TotalTicketAmount += ticketAmount;
                if (!ticket.IsRegraded())
                {
                    totalTicketAmountGraded += ticketAmount;
                }
                if (ticket.Prizing == Gameboards.GameboardStatus.WINNER)
                {
                    TotalMultiplierPrize += ticket.CalculateMultiplierPrize();
                    TotalNumbersPrize += ticket.Grade() * ticket.BetAmount();
                    TotalPrize += ticket.CalculatedPrize();
                }
                TotalBulleyePrize += ticket.CalculateBulleyePrizeCriteria();
            }
            TotalPlayers = players.Count;
            Profit = totalTicketAmountGraded - TotalPrize;
        }
    }

    internal class DailyTotalProfitReport : Objeto
    {
        private readonly List<DailyTotalProfitRecord2> dailyTotalProfitRecords = new List<DailyTotalProfitRecord2>();
        internal List<DailyTotalProfitRecord2> DailyTotalProfitRecords
        {
            get
            {
                return dailyTotalProfitRecords;
            }
        }

        private readonly int totalTickets;
        internal int TotalTickets
        {
            get
            {
                return totalTickets;
            }
        }

        private readonly int totalWinners;
        internal int TotalWinners
        {
            get
            {
                return totalWinners;
            }
        }

        private readonly decimal totalSold;
        internal decimal TotalSold
        {
            get
            {
                return totalSold;
            }
        }

        private readonly decimal totalPrizes;
        internal decimal TotalPrizes
        {
            get
            {
                return totalPrizes;
            }
        }

        private readonly decimal totalProfits;
        internal decimal TotalProfits
        {
            get
            {
                return totalProfits;
            }
        }

		internal DailyTotalProfitReport(List<DailyTotalProfitRecord2> dailyTotalProfitRecords, IEnumerable<DailyTotalProfitRecord2> totalProfitRecordsForTodayAndTomorrow)
		{
			Dictionary<DailyProfitKey, DailyTotalProfitRecord2> newDailyTotalProfitRecords;
            dailyTotalProfitRecords.AddRange(totalProfitRecordsForTodayAndTomorrow);
            var hasDateRepeated = HasDateRepeated(dailyTotalProfitRecords);
			if (hasDateRepeated)
			{
				newDailyTotalProfitRecords = new Dictionary<DailyProfitKey, DailyTotalProfitRecord2>();
				foreach (var dailyTotalProfit in dailyTotalProfitRecords)
				{
                    var key = new DailyProfitKey(dailyTotalProfit.Date, dailyTotalProfit.GameType, dailyTotalProfit.DomainId, dailyTotalProfit.AffiliateId, dailyTotalProfit.CurrencyId);
                    var value = newDailyTotalProfitRecords.GetValueOrDefault(key);
					if (value == null)
					{
						value = new DailyTotalProfitRecord2(dailyTotalProfit.Date, dailyTotalProfit.GameType, dailyTotalProfit.DomainId, dailyTotalProfit.AffiliateId, dailyTotalProfit.DomainUrl, dailyTotalProfit.CurrencyId);
						newDailyTotalProfitRecords.Add(key, value);
					}

					value.AddTicketInfo(dailyTotalProfit);
				}
				this.dailyTotalProfitRecords = newDailyTotalProfitRecords.Values.ToList();
			}
			else
			{
				this.dailyTotalProfitRecords = dailyTotalProfitRecords;
			}

			foreach (var dailyTotalProfit in this.dailyTotalProfitRecords)
			{
				totalTickets += dailyTotalProfit.TicketsCount;
				totalWinners += dailyTotalProfit.WinnersCount;
				totalSold += dailyTotalProfit.Sold;
				totalPrizes += dailyTotalProfit.Prizes;
				totalProfits += dailyTotalProfit.Profits;
			}
		}
            
        private bool HasDateRepeated(IEnumerable<AbstractDailyTotalProfitRecord> dailyTotalProfitRecords)
        {
            var dates = new List<DateTime>();
            var hasDateRepeated = false;
            foreach (var dailyTotalProfit in dailyTotalProfitRecords)
            {
                if (dates.Contains(dailyTotalProfit.Date))
                {
                    hasDateRepeated = true;
                    break;
                }
                else
                {
                    dates.Add(dailyTotalProfit.Date);
                }
            }
            return hasDateRepeated;
        }

        internal decimal TotalSoldForFree()
        {
            var total = totalSold - totalPrizes - totalProfits;
            return total;
        }
    }

    internal class DailyTotalProfitReportUSD : DailyTotalProfitReport
    {
        internal decimal TotalPrizesFP { get; set; }
        internal DailyTotalProfitReportUSD(List<DailyTotalProfitRecord2> dailyTotalProfitRecords, IEnumerable<DailyTotalProfitRecord2> totalProfitRecordsForTodayAndTomorrow, Currencies.CODES currency, Currencies.CODES rewardCurrency) : 
            base(dailyTotalProfitRecords.Where(x => x.CurrencyId == (int)currency).ToList(), totalProfitRecordsForTodayAndTomorrow.Where(x => x.CurrencyId == (int)currency))
        {
            Dictionary<DailyProfitKey, DailyTotalProfitRecord2> newDailyTotalProfitRecordsFP;
            dailyTotalProfitRecords.AddRange(totalProfitRecordsForTodayAndTomorrow);
            newDailyTotalProfitRecordsFP = new Dictionary<DailyProfitKey, DailyTotalProfitRecord2>();
            foreach (var dailyTotalProfit in dailyTotalProfitRecords)
            {
                if (dailyTotalProfit.CurrencyId == (int)rewardCurrency)
                {
                    var key = new DailyProfitKey(dailyTotalProfit.Date, dailyTotalProfit.GameType, dailyTotalProfit.DomainId, dailyTotalProfit.AffiliateId, dailyTotalProfit.CurrencyId);
                    var value = newDailyTotalProfitRecordsFP.GetValueOrDefault(key);
                    if (value == null)
                    {
                        value = new DailyTotalProfitRecord2(dailyTotalProfit.Date, string.Empty, dailyTotalProfit.DomainId, dailyTotalProfit.AffiliateId, dailyTotalProfit.DomainUrl, dailyTotalProfit.CurrencyId);
                        newDailyTotalProfitRecordsFP.Add(key, value);
                    }

                    value.AddTicketInfo(dailyTotalProfit);
                    TotalPrizesFP -= dailyTotalProfit.Prizes;
                }
            }
        }
    }
        
    interface IDrawingReport
    {
        int TotalPlayers();

        decimal TotalTicketAmount();

        int TotalWagers();

        decimal TotalPrize();

        decimal TotalProfit();
    }

    interface IDrawingRecordReport
    {
        int TotalPlayers();

        decimal TotalTicketAmount();

        int TotalWagers();

        decimal TotalPrize();

        decimal Profit();
    }

    internal class TotalProfitByDrawingReport : Objeto, IDrawingReport
    {
        private readonly List<TotalProfitByDrawingRecord> dailyTotalProfitByDrawingRecords = new List<TotalProfitByDrawingRecord>();

        internal IEnumerable<TotalProfitByDrawingRecord> GetAll
        {
            get
            {
                return dailyTotalProfitByDrawingRecords;
            }
        }

        private readonly int totalTickets;
        internal int TotalTickets
        {
            get
            {
                return totalTickets;
            }
        }

        private readonly int totalPlayers;
        internal int TotalOfPlayers
        {
            get
            {
                return totalPlayers;
            }
        }

        private readonly decimal totalSold;
        internal decimal TotalSold
        {
            get
            {
                return totalSold;
            }
        }

        private readonly decimal totalPrizes;
        internal decimal TotalPrizes
        {
            get
            {
                return totalPrizes;
            }
        }

        private readonly decimal totalProfits;
        internal decimal TotalProfits
        {
            get
            {
                return totalProfits;
            }
        }

        internal TotalProfitByDrawingReport(List<TotalProfitByDrawingRecord> totalProfitByDrawingRecords, List<TotalProfitByDrawingRecord> totalProfitByDrawingRecordsForTodayAndTomorrow, DateTime now)
        {
            var collectionContainsToday = CollectionContainsToday(totalProfitByDrawingRecords, now);
            if (collectionContainsToday)
            {
                this.dailyTotalProfitByDrawingRecords = totalProfitByDrawingRecords;
            }
            else
            {
                var collectionContainsYesterday = CollectionContainsYesterday(totalProfitByDrawingRecords, now);
                if (collectionContainsYesterday)
                {
                    foreach (var totalProfitRecord in totalProfitByDrawingRecordsForTodayAndTomorrow)
                    {
                        if (totalProfitRecord.DrawDate.Date == now.Date)
                        {
                            totalProfitByDrawingRecords.Add(totalProfitRecord);
                        }
                    }
                    this.dailyTotalProfitByDrawingRecords = totalProfitByDrawingRecords;
                }
                else
                {
                    totalProfitByDrawingRecords.AddRange(totalProfitByDrawingRecordsForTodayAndTomorrow);
                    this.dailyTotalProfitByDrawingRecords = totalProfitByDrawingRecords;
                }
            }

            foreach (var dailyTotalProfit in this.dailyTotalProfitByDrawingRecords)
            {
                totalTickets += dailyTotalProfit.TicketsCount;
                totalPlayers += dailyTotalProfit.PlayersCount;
                totalSold += dailyTotalProfit.Sold;
                totalPrizes += dailyTotalProfit.Prizes;
                totalProfits += dailyTotalProfit.Profits;
            }
        }

        internal TotalProfitByDrawingReport(List<TotalProfitByDrawingRecord> totalProfitByDrawingRecords)
        {
            this.dailyTotalProfitByDrawingRecords = totalProfitByDrawingRecords;

            foreach (var dailyTotalProfit in this.dailyTotalProfitByDrawingRecords)
            {
                totalTickets += dailyTotalProfit.TicketsCount;
                totalPlayers += dailyTotalProfit.PlayersCount;
                totalSold += dailyTotalProfit.Sold;
                totalPrizes += dailyTotalProfit.Prizes;
                totalProfits += dailyTotalProfit.Profits;
            }
        }

        private bool CollectionContainsToday(List<TotalProfitByDrawingRecord> dailyTotalProfitRecords, DateTime now)
        {
            if (dailyTotalProfitRecords.Count == 0) return false;
            var today = now.Date;
            var lastTotalProfitRecord = dailyTotalProfitRecords[dailyTotalProfitRecords.Count - 1];
            var collectionContainsToday = lastTotalProfitRecord.DrawDate.Date == today;
            if (collectionContainsToday)
            {
                return true;
            }
            return false;
        }

        private bool CollectionContainsYesterday(List<TotalProfitByDrawingRecord> dailyTotalProfitRecords, DateTime now)
        {
            if (dailyTotalProfitRecords.Count == 0) return false;
            var yesterday = now.AddDays(-1).Date;
            var lastTotalProfitRecord = dailyTotalProfitRecords[dailyTotalProfitRecords.Count - 1];
            var collectionContainsYesterday = lastTotalProfitRecord.DrawDate.Date == yesterday;
            if (collectionContainsYesterday)
            {
                return true;
            }
            return false;
        }

        public int TotalPlayers()
        {
            return totalPlayers;
        }

        public decimal TotalTicketAmount()
        {
            return totalSold;
        }

        public decimal TotalTicketAmountForFree()
        {
            return totalSold - totalPrizes - totalProfits;
        }

        public int TotalWagers()
        {
            return totalTickets;
        }

        public decimal TotalPrize()
        {
            return totalPrizes;
        }

        public decimal TotalProfit()
        {
            return totalProfits;
        }
    }

    public class WinnerRecord : Objeto
    {
        public string AccountNumber { get; set; }
        public DateTime Date { get; set; }
        public string TicketNumber { get; set; }
        public string WinnerNumber { get; set; }
        public string DomainUrl { get; set; }
        public int DomainId { get; set; }
        public int AffiliateId { get; set; }
        public decimal Amount { get; set; }
        public decimal TicketCost { get; set; }
        public decimal Prize { get; set; }
        public DateTime CreationDate { get; set; }
    }

    public class WinnersReport : Objeto
    {
        public List<PickWinnerRecord> Winners { get; private set; }
        public int TotalWinners { get; private set; }
        public decimal TotalAmount { get; private set; }
        public decimal TotalPrizes { get; private set; }
        public decimal TotalTicketCost { get; private set; }

        public WinnersReport(List<PickWinnerRecord> winners)
        {
            Winners = winners;
            TotalWinners = winners.Count;
            foreach (var winner in winners)
            {
                TotalAmount += winner.Amount;
                TotalPrizes += winner.Prize;
                TotalTicketCost += winner.TicketCost;
            }
        }
    }

    public class PickWinnerRecord : WinnerRecord
    {
        internal string State { get; set; }
        internal string IdOfLottery { get; set; }
        internal int UniqueDrawingId { get; set; }
        public string DrawingName { get; set; }
        internal string WinnerDigits { get; set; }
        public string GameType { get; set; }
        internal int TicketsCount { get; set; }
        internal int Fireball { get; set; }
        internal bool WithFireBall => Fireball != LotteryDraw.WITHOUT_FIREBALL;
        internal string TypeNumberSequenceAsText { get; set; }
    }

    public class KenoWinnerRecord : WinnerRecord
    {
        public int DrawId { get; set; }
    }

    interface IDailyTotalProfitKey
	{
		DateTime Date { get; }
		string GameType { get; }
	}

	struct TotalProfitByDrawingKey : IComparable
    {
        private DateTime drawDate;
        internal DateTime DrawDate
        {
            get
            {
                return drawDate;
            }
        }

        private string state;
        internal string State
        {
            get
            {
                return state;
            }
        }

        private string gameType;
        internal string GameType
        {
            get
            {
                return gameType;
            }
        }

        public int DomainId { get; }

        internal TotalProfitByDrawingKey(DateTime drawDate, string state, string gameType, int domainId)
        {
            this.drawDate = drawDate;
            this.state = state;
            this.gameType = gameType;
            DomainId = domainId;
        }

        internal TotalProfitByDrawingKey(DateTime drawDate, string gameType, int domainId)
        {
            this.drawDate = drawDate;
            this.state = "--";
            this.gameType = gameType;
            DomainId = domainId;
        }

        public int CompareTo(object obj)
        {
            var otherKey = (TotalProfitByDrawingKey)obj;
            if (otherKey.drawDate != drawDate)
            {
                return -DateTime.Compare(otherKey.drawDate, drawDate);
            }
            else
            {
                if (otherKey.state != state)
                {
                    return -String.Compare(otherKey.state, state);
                }
                else
                {
                    if (otherKey.gameType != gameType)
                    {
                        return -string.Compare(otherKey.gameType, gameType);
                    }
                    else
                    {
                        return otherKey.DomainId.CompareTo(DomainId);
                    }
                }
            }
        }
    }
    public abstract class AbstractDailyTotalProfitRecord : Objeto
	{
		internal DateTime Date
		{
            get;
		}

		internal string GameType
		{
            get;
		}

		private int ticketsCount;
		internal int TicketsCount
		{
			get
			{
				return ticketsCount;
			}
		}

		private int winnersCount;
		internal int WinnersCount
		{
			get
			{
				return winnersCount;
			}
		}

		private decimal sold;
		internal decimal Sold
		{
			get
			{
				return sold;
			}
		}

		private decimal prizes;
		internal decimal Prizes
		{
			get
			{
				return prizes;
			}
		}

		private decimal profits;
		internal decimal Profits
		{
			get
			{
				return profits;
			}
		}

        internal AbstractDailyTotalProfitRecord(DateTime date, string gameType)
        {
            if (date == default(DateTime)) throw new GameEngineException($"{nameof(date)} cannot have default value");

            Date = date;
            GameType = gameType;
        }

        internal AbstractDailyTotalProfitRecord(DateTime date, string gameType, int ticketsCount, int winnersCount, decimal sold, decimal prizes, decimal profits) : this(date, gameType)
        {
			if (ticketsCount < 0) throw new GameEngineException($"{nameof(ticketsCount)} must be greater or equal than 0");
			if (winnersCount < 0) throw new GameEngineException($"{nameof(winnersCount)} must be greater or equal than 0");
			if (sold < 0) throw new GameEngineException($"{nameof(sold)} must be greater or equal than 0");
			if (prizes < 0) throw new GameEngineException($"{nameof(prizes)} must be greater or equal than 0");

			this.ticketsCount = ticketsCount;
			this.winnersCount = winnersCount;
			this.sold = sold;
			this.prizes = prizes;
			this.profits = profits;
		}

		internal void AddTicketInfo(WinnerInfo info, bool isWinner)
		{
			if (info == null) throw new ArgumentNullException(nameof(info));

			ticketsCount += info.Wagers().Count();
			sold += info.TicketAmount();
			if (isWinner)
			{
				winnersCount++;
                prizes += info.Prize;
				profits += (info.CurrencyId != (int)Currencies.CODES.FP && info.CurrencyId != (int)Currencies.CODES.KRWFP) ? info.TicketAmount() - info.Prize : -info.Prize;
			}
			else
			{
                if (info.CurrencyId != (int)Currencies.CODES.FP && info.CurrencyId != (int)Currencies.CODES.KRWFP) profits += info.TicketAmount();
			}
		}

        internal void AddTicketInfo(CompletedKenoTicket info, bool isWinner)
        {
            if (info == null) throw new ArgumentNullException(nameof(info));

            ticketsCount += 1;
            sold += info.TicketAmount;
            if (isWinner)
            {
                winnersCount++;
                prizes += info.Prize;
                profits += info.TicketAmount - info.Prize;
            }
            else
            {
                profits += info.TicketAmount;
            }
        }

        internal void AddTicketNoActionInfo(WinnerInfo info)
        {
            if (info == null) throw new ArgumentNullException(nameof(info));
            ticketsCount += info.Wagers().Count();
        }

        internal void AddTicketNoActionInfo(CompletedKenoTicket info)
        {
            if (info == null) throw new ArgumentNullException(nameof(info));
            ticketsCount += 1;
        }

        internal virtual void AddTicketInfo(AbstractDailyTotalProfitRecord record)
		{
			if (record == null) throw new ArgumentNullException(nameof(record));

			ticketsCount += record.ticketsCount;
			sold += record.sold;
			winnersCount += record.winnersCount;
			prizes += record.prizes;
			profits += record.profits;
		}
	}

    internal struct DailyProfitKey:IComparable
    {
        internal DateTime DrawDate { get; }
        internal string IdOfLottery { get; }
        internal int DomainId { get; }
        internal int AffiliateId { get; }
        internal int CurrencyId { get; }

        internal DailyProfitKey(DateTime drawDate, string idOfLottery, int domainId, int affiliateId, int currencyId)
        {
            DrawDate = drawDate;
            IdOfLottery = idOfLottery;
            DomainId = domainId;
            AffiliateId = affiliateId;
            CurrencyId = currencyId;
        }

        public override int GetHashCode()
        {
            return DrawDate.GetHashCode() ^ IdOfLottery.GetHashCode() ^ DomainId.GetHashCode() ^ AffiliateId.GetHashCode() ^ CurrencyId.GetHashCode();
        }

        public override bool Equals(object obj)
        {
            if (ReferenceEquals(this, obj)) return true;
            if (obj is null) return false;
            if (!(obj is DailyProfitKey)) return false;

            var other = (DailyProfitKey)obj;
            return DrawDate == other.DrawDate && IdOfLottery == other.IdOfLottery && DomainId == other.DomainId && AffiliateId == other.AffiliateId && CurrencyId == other.CurrencyId;
        }

        public int CompareTo(object obj)
        {
            var otherKey = (DailyProfitKey)obj;

            if (otherKey.DrawDate != DrawDate)
            {
                return -DateTime.Compare(otherKey.DrawDate, DrawDate);
            }
            else
            {
                if (otherKey.CurrencyId != CurrencyId)
                {
                    return otherKey.CurrencyId.CompareTo(CurrencyId);
                }
                else
                {
                    if (otherKey.DomainId != DomainId)
                    {
                        return otherKey.DomainId.CompareTo(DomainId);
                    }
                    else
                    {
                        if (otherKey.IdOfLottery != IdOfLottery)
                        {
                            return otherKey.IdOfLottery.CompareTo(IdOfLottery);
                        }
                        else
                        {
                            return otherKey.AffiliateId - AffiliateId;
                        }
                    }
                }
            }
        }
    }

	public class DailyTotalProfitRecord2 : AbstractDailyTotalProfitRecord
	{
        internal DailyTotalProfitRecord2(DateTime date, string gameType, int domainId, int affiliateId, string domainUrl, int currencyId) : base(date, gameType)
        {
            DomainId = domainId;
            AffiliateId = affiliateId;
            DomainUrl = domainUrl;
            CurrencyId = currencyId;
        }

        internal DailyTotalProfitRecord2(DateTime date, string gameType, int domainId, int affiliateId, string domainUrl, int currencyId, int ticketsCount, int winnersCount, decimal sold, decimal prizes, decimal profits)
				: base(date, gameType, ticketsCount, winnersCount, sold, prizes, profits)
		{
            DomainId = domainId;
            AffiliateId = affiliateId;
            DomainUrl = domainUrl;
            CurrencyId = currencyId;
        }

        internal string DomainUrl { get; private set; }

        internal int DomainId
        {
            get; private set;
        }

        internal int AffiliateId
		{
            get; private set;
        }

        internal int CurrencyId { get; private set; }
        internal string CurrencyCode
        {
            get
            {
                return ((Currencies.CODES)CurrencyId).ToString();
            }
        }

        internal override void AddTicketInfo(AbstractDailyTotalProfitRecord record)
        {
            base.AddTicketInfo(record);
            var record2 = (DailyTotalProfitRecord2)record;
            DomainUrl = record2.DomainUrl;
        }
    }

    public abstract class TotalProfitByDrawingRecord : Objeto, IDrawingRecordReport
    {
        internal DateTime DrawDate
        {
            get; private set;
        }

        internal int DrawingId { get; private protected set; }

        internal int TicketsCount { get; private protected set; }

        internal int PlayersCount { get; private protected set; }

        internal decimal Sold { get; private protected set; }

        internal decimal Prizes { get; private protected set; }

        internal decimal Profits { get; private protected set; }

        internal int DomainId { get; private set; }

        public string DomainUrl { get; protected set; }
        internal int AffiliateId { get; }

        public TotalProfitByDrawingRecord(DateTime date, int affiliateId, int domainId)
        {
            DrawDate = date;
            AffiliateId = affiliateId;
            DomainId = domainId;
        }

        internal TotalProfitByDrawingRecord(DateTime date, int ticketsCount, int playersCount, decimal sold, decimal prizes, decimal profits, int affiliateId, int domainId, string domainUrl, int drawId)
        {
            if (date == default(DateTime)) throw new GameEngineException($"{nameof(date)} cannot have default value");
            if (ticketsCount < 0) throw new GameEngineException($"{nameof(ticketsCount)} must be greater or equal than 0");
            if (playersCount < 0) throw new GameEngineException($"{nameof(playersCount)} must be greater or equal than 0");
            if (sold < 0) throw new GameEngineException($"{nameof(sold)} must be greater or equal than 0");
            if (prizes < 0) throw new GameEngineException($"{nameof(prizes)} must be greater or equal than 0");
            if (domainId < 0) throw new GameEngineException($"{nameof(domainId)} must be greater or equal than 0");

            DrawDate = date;
            TicketsCount = ticketsCount;
            PlayersCount = playersCount;
            Sold = sold;
            Prizes = prizes;
            Profits = profits;
            AffiliateId = affiliateId;
            DomainId = domainId;
            DomainUrl = domainUrl;
            DrawingId = drawId;
        }

        public int TotalPlayers()
        {
            return PlayersCount;
        }

        public decimal TotalTicketAmount()
        {
            return Sold;
        }

        public int TotalWagers()
        {
            return TicketsCount;
        }

        public decimal TotalPrize()
        {
            return Prizes;
        }

        public decimal Profit()
        {
            return Profits;
        }
    }

    public class TotalProfitByPicksDrawingRecord : TotalProfitByDrawingRecord
    {
        internal string State
        {
            get; private set;
        }

        internal string GameTypeForReports
        {
            get; private set;
        }

        internal int UniqueDrawingId { get; private set; }

        internal TypeNumberSequence Position { get; private protected set; }

        private string drawingName;
        internal string DrawingName
        {
            get
            {
                return drawingName;
            }
        }

        internal TotalProfitByPicksDrawingRecord(DateTime date, string state, string gameType, int affiliateId, int domainId, string domainUrl, int drawingId, TypeNumberSequence position, string drawingName) : base(date, affiliateId, domainId)
        {
            State = state;
            GameTypeForReports = gameType;
            DomainUrl = domainUrl;
            DrawingId = drawingId;
            Position = position;
            this.drawingName = drawingName;
        }

        internal TotalProfitByPicksDrawingRecord(DateTime date, string state, string gameType, int ticketsCount, int playersCount, decimal sold, decimal prizes, decimal profits, int affiliateId, int domainId, string domainUrl, int drawingId, TypeNumberSequence position, string drawingName):
            base(date, ticketsCount, playersCount, sold, prizes, profits, affiliateId, domainId, domainUrl, drawingId)
        {
            if (date == default(DateTime)) throw new GameEngineException($"{nameof(date)} cannot have default value");
            if (ticketsCount < 0) throw new GameEngineException($"{nameof(ticketsCount)} must be greater or equal than 0");
            if (playersCount < 0) throw new GameEngineException($"{nameof(playersCount)} must be greater or equal than 0");
            if (sold < 0) throw new GameEngineException($"{nameof(sold)} must be greater or equal than 0");
            if (prizes < 0) throw new GameEngineException($"{nameof(prizes)} must be greater or equal than 0");
            if (domainId < 0) throw new GameEngineException($"{nameof(domainId)} must be greater or equal than 0");

            State = state;
            GameTypeForReports = gameType;
            UniqueDrawingId = drawingId;
            Position = position;
            this.drawingName = drawingName;
        }

        HashSet<string> accountNumbers = new HashSet<string>();
        internal void AddTicketInfo(TicketMessage msg, bool isWinner)
        {
            if (msg == null) throw new ArgumentNullException(nameof(msg));

            var info = (WinnerInfo)msg;
            var drawingIdUnassigned = UniqueDrawingId == 0;
            if (drawingIdUnassigned)
            {
                UniqueDrawingId = info.UniqueDrawingId;
                drawingName = info.DrawingName;
            }

            TicketsCount += info.Wagers().Count();
            Sold += info.TicketAmount();
            if (!accountNumbers.Contains(info.AccountNumber))
            {
                PlayersCount++;
                accountNumbers.Add(info.AccountNumber);
            }
            Profits += info.Profit;
            if (isWinner)
            {
                Prizes += info.Prize;
            }
        }

        internal void AddTicketNoActionInfo(TicketMessage msg)
        {
            if (msg == null) throw new ArgumentNullException(nameof(msg));

            var info = (WinnerInfo)msg;
            var drawingIdUnassigned = UniqueDrawingId == 0;
            if (drawingIdUnassigned)
            {
                UniqueDrawingId = info.UniqueDrawingId;
                drawingName = info.DrawingName;
            }
            TicketsCount += info.Wagers().Count();
            if (!accountNumbers.Contains(info.AccountNumber))
            {
                PlayersCount++;
                accountNumbers.Add(info.AccountNumber);
            }
        }
    }

    public class TotalProfitByKenoDrawingRecord : TotalProfitByDrawingRecord
    {
        public TotalProfitByKenoDrawingRecord(DateTime drawDate, int affiliateId, int domainId) : base(drawDate, affiliateId, domainId)
        {

        }

        internal TotalProfitByKenoDrawingRecord(DateTime date, int ticketsCount, int playersCount, decimal sold, decimal prizes, decimal profits, int affiliateId, int domainId, string domainUrl, int drawId) :
            base(date, ticketsCount, playersCount, sold, prizes, profits, affiliateId, domainId, domainUrl, drawId)
        {
        }

        internal void AddTicketInfo(CompletedTicket msg, bool isWinner)
        {
            if (msg == null) throw new ArgumentNullException(nameof(msg));

            var info = (CompletedKenoTicket)msg;
            var drawingIdUnassigned = DrawingId == 0;
            if (drawingIdUnassigned)
            {
                DrawingId = info.DrawId;
            }

            TicketsCount++;
            Sold += info.TicketAmount;
            PlayersCount++;
            Profits += info.Profit;
            if (isWinner)
            {
                Prizes += info.Prize;
            }
        }

        internal void AddTicketNoActionInfo(CompletedTicket msg)
        {
            if (msg == null) throw new ArgumentNullException(nameof(msg));

            var info = (CompletedKenoTicket)msg;
            var drawingIdUnassigned = DrawingId == 0;
            if (drawingIdUnassigned)
            {
                DrawingId = info.DrawId;
            }
            TicketsCount++;
            PlayersCount++;
        }
    }

    internal class GradingKenoReport
    {
        List<GradingKenoRecord> records = new List<GradingKenoRecord>();
        internal IEnumerable<GradingKenoRecord> GetAll 
        { 
            get 
            {
                return records;
            } 
        }

        public GradingKenoReport(IEnumerable<LotteryDraw> drawnLotteriesGraded, IEnumerable<LotteryDraw> drawnLotteriesRegraded, IEnumerable<LotteryNoAction> drawnLotteriesNoAction)
        {
            foreach (var draw in drawnLotteriesGraded)
            {
                var drawId = draw.Lottery.PicksLotteryGame.NextPendingAndNoRegradedSchedulesAtForKeno(draw.Date).IdPrefix;
                var record = new GradingKenoRecord
                {
                    DrawingDate = draw.Date,
                    DrawingId = int.Parse(drawId),
                    GradedNumber = SeparateGradeNumbersByComma(draw.SequenceOfNumbers),
                    Action = "Graded",
                    GradedBy = draw.WhoGraded,
                    GradedDate = draw.LastGradedDate,
                    Log = draw.Log
                };
                records.Add(record);
            }
            foreach (var draw in drawnLotteriesRegraded)
            {
                var drawId = draw.Lottery.PicksLotteryGame.NextPendingAndNoRegradedSchedulesAtForKeno(draw.Date).IdPrefix;
                var record = new GradingKenoRecord
                {
                    DrawingDate = draw.Date,
                    DrawingId = int.Parse(drawId),
                    GradedNumber = SeparateGradeNumbersByComma(draw.SequenceOfNumbers),
                    Action = "Regraded",
                    GradedBy = draw.WhoGraded,
                    GradedDate = draw.LastGradedDate,
                    Log = draw.Log
                };
                records.Add(record);
            }
            foreach (var draw in drawnLotteriesNoAction)
            {
                var drawId = draw.Lottery.PicksLotteryGame.NextPendingAndNoRegradedSchedulesAtForKeno(draw.Date).IdPrefix;
                var record = new GradingKenoRecord
                {
                    DrawingDate = draw.Date,
                    DrawingId = int.Parse(drawId),
                    GradedNumber = string.Empty,
                    Action = "NoAction",
                    GradedBy = draw.WhoSetNoAction,
                    GradedDate = draw.NoActionDate,
                    Log = draw.Log
                };
                records.Add(record);
            }
        }

        string SeparateGradeNumbersByComma(string sequence)
        {
            var listWinnerNumbers = Enumerable.Range(0, sequence.Length / 2).Select(i => sequence.Substring(i * 2, 2).TrimStart('0'));
            var result = string.Join(',', listWinnerNumbers);
            return result;
        }
    }

    internal class GradingRecord : Objeto
    {
        internal DateTime DrawingDate { get; set; }
        internal string GradedNumber { get; set; }
        internal string RootGradedNumber { get; set; }
        internal int Fireball { get; set; }
        internal bool WithFireBall { get; set; }
        internal string TypeNumberSequenceAsText { get; set; }
        internal string Action { get; set; }
        internal string GradedBy { get; set; }
        internal DateTime GradedDate { get; set; }
        internal string Log { get; set; }
    }

    internal class GradingPicksRecord : GradingRecord
    {
        internal string State { get; set; }
        internal int UniqueDrawingId { get; set; }
        internal string IdOfLottery{ get; set; }
        internal string DrawingName { get; set; }
        internal string GameType { get; set; }
    }

    internal class GradingKenoRecord : GradingRecord
    {
        public int DrawingId { get; set; }
    }

    internal class NoActionReport : Objeto
    {
        public int TotalTickets { get; set; }

        public NoActionReport(IEnumerable<Ticket> ticketsOfDraw)
        {
            var countWagers = ticketsOfDraw.Sum(x => x.CountWagers);
            TotalTickets = countWagers;
        }

        internal void JoinReport(NoActionReport reportFB)
        {
            if (reportFB == null) throw new ArgumentNullException(nameof(reportFB));
            
            TotalTickets += reportFB.TotalTickets;
        }
    }

    internal class EmptyNoActionReport : NoActionReport
    {
        private IEnumerable<Ticket> ticketsOfDraw;

        public EmptyNoActionReport(IEnumerable<Ticket> ticketsOfDraw) : base(ticketsOfDraw)
        {
            if (ticketsOfDraw == null) throw new ArgumentNullException(nameof(ticketsOfDraw));

            this.ticketsOfDraw = ticketsOfDraw;
        }
    }

    struct TicketsSoldPerDayAndGameKey
    {
        private readonly DateTime date;
        internal DateTime Date
        {
            get
            {
                return date;
            }
        }

        private readonly string gameType;
        internal string GameType
        {
            get
            {
                return gameType;
            }
        }

        internal TicketsSoldPerDayAndGameKey(DateTime date, string gameType)
        {
            this.gameType = gameType;
            this.date = date;
        }
    }

    internal class CountsOfTicketsPerDayAndGameReport : Objeto
    {
        private readonly Dictionary<TicketsSoldPerDayAndGameKey, CountsOfTicketsPerDayAndGame> counts = new Dictionary<TicketsSoldPerDayAndGameKey, CountsOfTicketsPerDayAndGame>();

        internal IEnumerable<CountsOfTicketsPerDayAndGame> GetAll
        {
            get
            {
                return counts.Values.ToList();
            }
        }

        internal CountsOfTicketsPerDayAndGameReport(DateTime startDate, DateTime endDate, IEnumerable<WinnerInfo> winners, IEnumerable<WinnerInfo> losers)
        {
            List<string> gameTypes = new List<string>()
            {
                QueryMakerOfHistoricalPicks.ID_PICK2,
                QueryMakerOfHistoricalPicks.ID_PICK3,
                QueryMakerOfHistoricalPicks.ID_PICK4,
                QueryMakerOfHistoricalPicks.ID_PICK5,
                QueryMakerOfHistoricalPicks.ID_POWERBALL
            };

            for (DateTime date = startDate; date.Date <= endDate.Date; date = date.AddDays(1))
            {
                foreach (var gameType in gameTypes)
                {
                    var key = new TicketsSoldPerDayAndGameKey(date.Date, gameType);
                    counts.Add(key, new CountsOfTicketsPerDayAndGame(key, 0));
                }
            }

            foreach (var winner in winners)
            {
                var date = winner.DrawDate().Date;
                var key = new TicketsSoldPerDayAndGameKey(date, winner.IdOfLottery.ToString());
                var value = counts.GetValueOrDefault(key);
                if (value == null) throw new GameEngineException($"Date {date} is out of range");

                value.AddTicket(winner);
            }

            foreach (var loser in losers)
            {
                var date = loser.DrawDate().Date;
                var key = new TicketsSoldPerDayAndGameKey(date, loser.IdOfLottery.ToString());
                var value = counts.GetValueOrDefault(key);
                if (value == null) throw new GameEngineException($"Date {date} is out of range");

                value.AddTicket(loser);
            }
        }
    }

    internal class SalesAndPrizesPerDayReport : Objeto
    {
        private readonly Dictionary<DateTime, SalesAndPrizesPerDay> salesAndPrizesPerDay = new Dictionary<DateTime, SalesAndPrizesPerDay>();

        internal IEnumerable<SalesAndPrizesPerDay> GetAll
        {
            get
            {
                return salesAndPrizesPerDay.Values.ToList();
            }
        }

        internal SalesAndPrizesPerDayReport(DateTime startDate, DateTime endDate, IEnumerable<WinnerInfo> winners, IEnumerable<WinnerInfo> losers)
        {
            for (DateTime date = startDate; date.Date <= endDate.Date; date = date.AddDays(1))
            {
                salesAndPrizesPerDay.Add(date, new SalesAndPrizesPerDay(date, 0));
            }

            foreach (var winner in winners)
            {
                var date = winner.DrawDate().Date;
                var value = salesAndPrizesPerDay.GetValueOrDefault(date);
                if (value == null) throw new GameEngineException($"Date {date} is out of range");

                value.AddTicket(winner);
            }

            foreach (var loser in losers)
            {
                var date = loser.DrawDate().Date;
                var value = salesAndPrizesPerDay.GetValueOrDefault(date);
                if (value == null) throw new GameEngineException($"Date {date} is out of range");

                value.AddTicket(loser);
            }
        }
    }

    internal class CountsOfTicketsPerDayAndGame : Objeto
    {
        private int count;
        internal int Count
        {
            get
            {
                return count;
            }
        }

        private readonly TicketsSoldPerDayAndGameKey key;

        internal DateTime Date
        {
            get
            {
                return key.Date;
            }
        }

        internal string GameType
        {
            get
            {
                return key.GameType;
            }
        }

        internal CountsOfTicketsPerDayAndGame(TicketsSoldPerDayAndGameKey key, int count)
        {
            this.key = key;
            this.count = count;
        }

        internal void AddTicket(WinnerInfo ticket)
        {
            if (key.Date != ticket.DrawDate().Date) throw new GameEngineException($"Ticket does not belong to day {key.Date}");
            if (!ticket.IdOfLottery.EqualsTo(key.GameType)) throw new GameEngineException($"Ticket does not belong to gameType {key.GameType}");
            this.count += ticket.Wagers().Count();
        }
    }

    internal class SalesAndPrizesPerDay : Objeto
    {
        private int count;
        internal int Count
        {
            get
            {
                return count;
            }
        }

        private readonly DateTime date;
        internal DateTime Date
        {
            get
            {
                return date;
            }
        }

        private decimal sales;
        internal decimal Sales
        {
            get
            {
                return sales;
            }
        }

        private decimal prizes;
        internal decimal Prizes
        {
            get
            {
                return prizes;
            }
        }

        internal decimal PrizeTicketAverage
        {
            get
            {
                return Count == 0 ? 0 : prizes / Count;
            }
        }

        internal decimal PrizeSaleAverage
        {
            get
            {
                return sales == 0 ? 0 : prizes / sales;
            }
        }

        internal SalesAndPrizesPerDay(DateTime date, int count)
        {
            this.date = date;
            this.count = count;
        }

        internal void AddTicket(WinnerInfo ticket)
        {
            if (date != ticket.DrawDate().Date) throw new GameEngineException($"Ticket does not belong to day {date}");

            count += ticket.Wagers().Count();
            sales += ticket.Amount;
            prizes += ticket.Prize;
        }
    }

    internal class TicketRecord : Objeto
    {
        private readonly DateTime date;
        internal DateTime Date
        {
            get
            {
                return date;
            }
        }

        private readonly string gameType;
        internal string GameType
        {
            get
            {
                return gameType;
            }
        }

        private readonly decimal amount;
        internal decimal Amount
        {
            get
            {
                return amount;
            }
        }

        private readonly int count;
        internal int Count
        {
            get
            {
                return count;
            }
        }

        private readonly decimal prize;
        internal decimal Prize
        {
            get
            {
                return prize;
            }
        }

        internal TicketRecord(decimal prize, int count, decimal amount, string gameType, DateTime date)
        {
            this.prize = prize;
            this.count = count;
            this.amount = amount;
            this.gameType = gameType;
            this.date = date;
        }
    }

}
