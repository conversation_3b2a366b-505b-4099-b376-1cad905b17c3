﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GamesEngine.Business.Liquidity.Persistence
{
    public class DataModel
    {
        public class Deposit
        {
            public long Id { get; set; }
            public string DocumentNumber { get; set; }
            public decimal Amount { get; set; }
            public DateTime Date { get; set; }
            public string DateAsText => Date.ToString("yyyy/MM/dd HH:mm:ss");
            public int StoreId { get; set; }
            public string AccountNumber { get; set; }
            public int DomainId { get; set; }
            public string Address { get; set; }
            public DateTime Created { get; set; }
            public string CreatedAsText => Created.ToString("yyyy/MM/dd HH:mm:ss");

            public Deposit() { }

            public Deposit(ConfirmDepositMessage msg)
            {
                Id = msg.DepositId;
                DocumentNumber = msg.TransactionId;
                Amount = msg.Amount;
                Date = msg.CreatedAt;
                StoreId = 1; // <<<< NEEDS ACTUAL BUSINESS LOGIC
                AccountNumber = msg.Account;
                DomainId = msg.DomainId;
                Address = msg.Address;
                Created = DateTime.Now;
            }

            public override string ToString()
            {
                return $"ID: {Id}, Domain: {DomainId}, Auth: {DocumentNumber}, Acct: {AccountNumber}, Store: {StoreId}, Date: {Date:MM/dd/yyyy HH:mm:ss}, Amount: {Amount}, Address: {Address}, Created: {Created:MM/dd/yyyy HH:mm:ss}";
            }
        }

        public class Tank
        {
            public long Id { get; set; }
            public string Description { get; set; }
            public DateTime Created { get; set; }
            public IEnumerable<int> DepositIds { get; set; }

            public Tank() { }

            public Tank(CreatedTankMessage msg)
            {
                Id = msg.TankId;
                Description = msg.Description;
                Created = msg.CreatedAt;
                DepositIds = msg.DepositIds;
            }

            public Tank(TankMergedMessage msg)
            {
                Id = msg.TankId;
                Description = msg.Description;
                Created = msg.CreatedAt;
                DepositIds = msg.DepositIds;
            }

            public override string ToString()
            {
                return $"ID: {Id}, Description: {Description}, Created: {Created:MM/dd/yyyy HH:mm:ss}, DepositIds: {string.Join(", ", DepositIds)}";
            }
        }

        public class TankWithDeposits
        {
            public Tank TankInfo { get; set; }
            public List<Deposit> Deposits { get; set; }

            public TankWithDeposits()
            {
                Deposits = new List<Deposit>();
            }
        }

        public class Tanker
        {
            public long Id { get; set; }
            public string Description { get; set; }
            public DateTime Created { get; set; }

            public Tanker() { }

            public Tanker(CreatedTankerMessage msg)
            {
                Id = msg.TankerId;
                Description = msg.Description;
                Created = msg.CreatedAt;
            }

            public override string ToString()
            {
                return $"ID: {Id}, Description: {Description}, Created: {Created:MM/dd/yyyy HH:mm:ss}";
            }
        }

        public class TankerWithDeposits
        {
            public Tanker TankerInfo { get; set; }
            public List<Deposit> Deposits { get; set; }

            public TankerWithDeposits()
            {
                Deposits = new List<Deposit>();
            }
        }

        public class Withdrawal
        {
            public long Id { get; set; }
            public string DocumentNumber { get; set; }
            public decimal Amount { get; set; }
            public DateTime Date { get; set; }
            public string DateAsText => Date.ToString("MM/dd/yyyy HH:mm:ss");
            public int StoreId { get; set; }
            public string AccountNumber { get; set; }
            public int DomainId { get; set; }
            public string Address { get; set; }
            public DateTime Created { get; set; }
            public string CreatedAsText => Created.ToString("MM/dd/yyyy HH:mm:ss");

            public override string ToString()
            {
                return $"ID: {Id}, Domain: {DomainId}, Auth: {DocumentNumber}, Acct: {AccountNumber}, Store: {StoreId}, Date: {Date:MM/dd/yyyy HH:mm:ss}, Amount: {Amount}, Address: {Address}, Created: {Created:MM/dd/yyyy HH:mm:ss}";
            }
        }

        public class Bottle
        {
            public long Id { get; set; }
            public string Description { get; set; }
            public DateTime Created { get; set; }
        }

        public class Dispenser
        {
            public long Id { get; set; }
            public string Description { get; set; }
            public DateTime Created { get; set; }

            public Dispenser() { }

            public Dispenser(CreatedDispenserMessage msg)
            {
                Id = msg.Id;
                Created = msg.StartDate;
            }
        }

        public class DispenserWithWithdrawals
        {
            public Dispenser DispenserInfo { get; set; } = new Dispenser();
            public List<Withdrawal> Withdrawals { get; set; } = new List<Withdrawal>();
        }
    }
}
