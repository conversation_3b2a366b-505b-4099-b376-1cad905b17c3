﻿namespace GamesEngine.Business.Liquidity.Containers
{
    public abstract class Bottle : Container
    {
        public Bottle(string kind) : base(kind)
        {
        }
    }

    public class BottlePending : Bottle
    {
        public BottlePending(string kind, decimal amount) : base(kind)
        {
        }
    }

    public class BottleProcessing : Bottle
    {
        public BottleProcessing(string kind, decimal amount) : base(kind)
        {
        }
    }

    public class BottleCompleted : Bottle
    {
        public BottleCompleted(string kind, decimal amount) : base(kind)
        {
        }
    }

    public class BottleCanceled : Bottle
    {
        public BottleCanceled(string kind, decimal amount) : base(kind)
        {
        }
    }

    public class BottleFailed : Bottle
    {
        public BottleFailed(string kind, decimal amount) : base(kind)
        {
        }
    }
}
