﻿using Connectors.town.connectors.driver.transactions;
using Connectors.town.connectors.drivers.consignment;
using ExchangeAPI.Logic;
using ExchangeAPI.Model;
using ExchangeAPI.Service;
using GamesEngine;
using GamesEngine.Business;
using GamesEngine.Exchange;
using GamesEngine.Finance;
using GamesEngine.PurchaseOrders;
using GamesEngine.Settings;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;
using town.connectors.commons;
using town.connectors.drivers;
using town.connectors.drivers.hades;
using static ExchangeAPI.Controllers.APIController;
using static GamesEngine.Business.WholePaymentProcessor;
using static GamesEngine.Business.WholePaymentProcessor.PaymentProcessor;
using static GamesEngine.Finance.PaymentChannels;
using static town.connectors.CustomSettings;
using static town.connectors.drivers.Result;
using LockBalanceResponse = GamesEngine.Finance.LockBalanceResponse;

namespace ExchangeAPI.Controllers
{
	public class APIController : AuthorizeController
	{
		[HttpGet("api/exchange/processors")]
        [Authorize(Roles = "devops")]//VERIFY_ROLES
        public async Task<IActionResult> ListProcessorsAsync()
		{
			string url = HttpContext.Request.Host.Host;
			
			IActionResult result = await ExchangeManagerAPI.ExchangeManager.PerformQryAsync(HttpContext, $@"
				{{
					for( domain : company.Sales.AllDomains )
					{{
						if( domain.Url == '{url}')
						{{
							print domain.Id domainId;
							print domain.Url domain;
							allowedProcessors = processors.List(domain);
							for( allowedProcessor : allowedProcessors )
							{{
								print allowedProcessor.Id id;
								print allowedProcessor.Name name;
							}}
						}}
						
					}}
				}}
				");
			if (!(result is OkObjectResult))
			{
				if (result is ContentResult contentResult) return BadRequest($"Error: {contentResult.Content}");
				return BadRequest($"Error:{result}");
			}

			return result;
		}

		[HttpGet("api/exchange/riskRatings")]
		[Authorize(Roles = "c42")]
		public async Task<IActionResult> ListRiskRatingsAsync()
		{
			var result = await ExchangeManagerAPI.ExchangeManager.PerformQryAsync(HttpContext, $@"
                {{
					print riskRatings.Count count;
					print riskRatings.MaxValidPriority maxValidPriority;
					for(riskRatingsList : riskRatings.List())
					{{
						riskRating = riskRatingsList;
						print riskRating.Name name;
						print riskRating.Priority priority;
						print riskRating.Description description;
						print riskRating.Enabled enabled;
                    }}
					for(log : riskRatings.ListLog())
					{{
						print log log;
                    }}
                }}
            ");
			return result;
		}

		[HttpGet("api/exchange/riskRatings/enabled")]
		[Authorize(Roles = "c43")]
		public async Task<IActionResult> ListEnabledRiskRatingsAsync()
		{
			var result = await ExchangeManagerAPI.ExchangeManager.PerformQryAsync(HttpContext, $@"
                {{
					for(riskRatingsList : riskRatings.ListEnabledRiskRatings())
					{{
						riskRating = riskRatingsList;
						print riskRating.Name name;
						print riskRating.Priority priority;
						print riskRating.Description description;
                    }}
                }}
            ");
			return result;
		}

		const byte MAX_RISK_RATING_NAME_LENGTH = 100;
		const byte MAX_DESCRIPTION_LENGTH = 100;

		[HttpPost("api/exchange/riskRating")]
		[Authorize(Roles = "c44")]
		public async Task<IActionResult> CreateRiskRatingAsync([FromBody] RiskRatingBody body)
		{
			if (body == null) return BadRequest("Body is required");
			if (string.IsNullOrWhiteSpace(body.Name)) return BadRequest($"{nameof(body.Name)} is required");
			if (string.IsNullOrWhiteSpace(body.Description)) return BadRequest($"{nameof(body.Description)} is required");
			if (body.Name.Length > MAX_RISK_RATING_NAME_LENGTH) return BadRequest($"{nameof(body.Name)} length is greater than {MAX_RISK_RATING_NAME_LENGTH}");
			if (body.Description.Length > MAX_DESCRIPTION_LENGTH) return BadRequest($"{nameof(body.Description)} length is greater than {MAX_DESCRIPTION_LENGTH}");

			var nameEscaped = Validator.StringEscape(body.Name.Trim());
			var result = await ExchangeManagerAPI.ExchangeManager.PerformQryAsync(HttpContext, $@"
                {{
                    existsRiskRating = riskRatings.ExistsName('{nameEscaped}');
                    print existsRiskRating existsRiskRating;
					isValidPriority = riskRatings.IsValidPriorityToAdd({body.Priority});
					print isValidPriority isValidPriority;
                }}
            ");

			if (!(result is OkObjectResult))
			{
				if (result is ContentResult contentResult) return BadRequest($"Error: {contentResult.Content}");
				return BadRequest($"Error:{result}");
			}

			OkObjectResult o = (OkObjectResult)result;
			string json = o.Value.ToString();
			var existence = JsonConvert.DeserializeObject<RiskRatingExistence>(json);

			if (! existence.existsRiskRating && existence.isValidPriority)
			{
				string commandToDisable = string.Empty;
				if (!body.Enabled) commandToDisable = "riskRating.Disable();";
				var descriptionEscaped = Validator.StringEscape(body.Description.Trim());
				return await ExchangeManagerAPI.ExchangeManager.PerformCmdAsync(HttpContext, $@"
					{{
						riskRating = riskRatings.NewRiskRating('{nameEscaped}','{descriptionEscaped}', {body.Priority});
						{commandToDisable}
					}}
				");
			}
			else
            {
				var errorMessage = new StringBuilder();
				if (existence.existsRiskRating)
                {
					errorMessage.AppendLine($"Sorry, there is a risk rating using the name '{nameEscaped}'. Please try again with other one.");
				}
				if (! existence.isValidPriority)
				{
					errorMessage.AppendLine($"Sorry, priority '{body.Priority}' is not valid. Please try again with other one.");
				}

				return PrefabBadRequest(errorMessage.ToString());
			}
		}

		[HttpPut("api/exchange/riskRating")]
		[Authorize(Roles = "c45")]
		public async Task<IActionResult> UpdateRiskRatingAsync([FromBody] RiskRatingUpdaterBody body)
		{
			if (body == null) return BadRequest("Body is required");
			if (string.IsNullOrWhiteSpace(body.Name)) return BadRequest($"{nameof(body.Name)} is required");
			if (string.IsNullOrWhiteSpace(body.NewName)) return BadRequest($"{nameof(body.NewName)} is required");
			if (string.IsNullOrWhiteSpace(body.Description)) return BadRequest($"{nameof(body.Description)} is required");
			if (body.Name.Length > MAX_RISK_RATING_NAME_LENGTH) return BadRequest($"{nameof(body.Name)} length is greater than {MAX_RISK_RATING_NAME_LENGTH}");
			if (body.NewName.Length > MAX_RISK_RATING_NAME_LENGTH) return BadRequest($"{nameof(body.NewName)} length is greater than {MAX_RISK_RATING_NAME_LENGTH}");
			if (body.Description.Length > MAX_DESCRIPTION_LENGTH) return BadRequest($"{nameof(body.Description)} length is greater than {MAX_DESCRIPTION_LENGTH}");

			var nameEscaped = Validator.StringEscape(body.Name.Trim());
			var newNameEscaped = Validator.StringEscape(body.NewName.Trim());
			var scriptCheckingNewName = string.Empty;
			var hasChangedName = nameEscaped != newNameEscaped;
			if (hasChangedName)
            {
				scriptCheckingNewName = $@"isAlreadyUsedNewName = riskRatings.ExistsName('{newNameEscaped}');
                    print isAlreadyUsedNewName isAlreadyUsedNewName;";
			}
			var result = await ExchangeManagerAPI.ExchangeManager.PerformQryAsync(HttpContext, $@"
                {{
                    existsRiskRating = riskRatings.ExistsName('{nameEscaped}');
                    print existsRiskRating existsRiskRating;
					{scriptCheckingNewName}
					isValidPriority = riskRatings.IsValidPriorityToUpdate({body.Priority});
					print isValidPriority isValidPriority;
                }}
            ");

			if (!(result is OkObjectResult))
			{
				if (result is ContentResult contentResult) return BadRequest($"Error: {contentResult.Content}");
				return BadRequest($"Error:{result}");
			}

			OkObjectResult o = (OkObjectResult)result;
			string json = o.Value.ToString();
			var existence = JsonConvert.DeserializeObject<RiskRatingAndNewNameExistence>(json);

			if (existence.existsRiskRating && ! existence.isAlreadyUsedNewName && existence.isValidPriority)
			{
				var descriptionEscaped = Validator.StringEscape(body.Description.Trim());
				return await ExchangeManagerAPI.ExchangeManager.PerformCmdAsync(HttpContext, $@"
					{{
						riskRating = riskRatings.FindRiskRating('{nameEscaped}');
						riskRating.Update('{newNameEscaped}', '{descriptionEscaped}', {body.Priority}, {body.Enabled});
					}}
				");
			}
			else
			{
				var errorMessage = new StringBuilder();
				if (! existence.existsRiskRating)
                {
					errorMessage.AppendLine($"Sorry, no risk rating with the name '{nameEscaped}'. Please try again with other one.");
				}
				if (existence.isAlreadyUsedNewName)
				{
					errorMessage.AppendLine($"Sorry, there is a risk rating using the name '{newNameEscaped}'. Please try again with other one.");
				}
				if (!existence.isValidPriority)
				{
					errorMessage.AppendLine($"Sorry, priority '{body.Priority}' is not valid. Please try again with other one.");
				}

				return PrefabBadRequest(errorMessage.ToString());
			}
		}

		[HttpGet("api/exchange/riskRating/assignation")]
		[Authorize(Roles = "c39")]
		public async Task<IActionResult> ListAmountRangesByRiskRatingAsync(string userName)
		{
			if (string.IsNullOrWhiteSpace(userName)) return BadRequest($"{nameof(userName)} is required");

			string path = Security.UserPath(HttpContext);
			var usernameEscaped = Validator.StringEscape(userName.Trim());
			var result = await ExchangeManagerAPI.ExchangeManager.PerformQryAsync(HttpContext, $@"
                {{
					existsAgent = marketplace.ExistsAgent('{path}');
					print existsAgent existsAgent;
					if (existsAgent)
					{{
						agent = marketplace.SearchAgent('{path}');
						user = agent.SearchUser('{usernameEscaped}');
						for(amountRangesByRiskRating : user.ListAmountRangesByRiskRating())
						{{
							amountRangeByRiskRating = amountRangesByRiskRating;
							print amountRangeByRiskRating.RiskRatingName riskRating;
							for(amountRangesByCurrency : amountRangeByRiskRating.ListAmountRanges())
							{{
								amountRange = amountRangesByCurrency;
								print amountRange.MaxAmount.Value maxAmount;
								print amountRange.MinAmount.Value minAmount;
								print amountRange.MaxAmount.CurrencyCodeAsText currencyCode;
							}}
							for(transactionTypes : amountRangeByRiskRating.ListTransactionTypes())
							{{
								print transactionTypes transactionType;
							}}
						}}
					}}
                }}
            ");
			if (!(result is OkObjectResult))
			{
				if (result is ContentResult contentResult) return BadRequest($"Error: {contentResult.Content}");
				return BadRequest($"Error:{result}");
			}

			OkObjectResult o = (OkObjectResult)result;
			string json = o.Value.ToString();
			var existence = JsonConvert.DeserializeObject<AgentExistence>(json);

			if (!existence.ExistsAgent)
			{
				string messageError = "Oops something went wrong. Please contact the support team for more information.";
				return PrefabBadRequest(messageError, "No stack.", "No command error info.", json);
			}

			return result;
		}

		[DataContract(Name = "AgentExistence")]
		public class AgentExistence
		{
			[DataMember(Name = "existsAgent")]
			public bool ExistsAgent { get; set; }
		}

		[HttpPost("api/exchange/riskRating/assignation")]
		[Authorize(Roles = "c40")]
		public async Task<IActionResult> AssignRiskRatingAndAmountRangeAsync([FromBody] RiskRatingAndAmountRangeAssignationBody body)
		{
			if (body == null) return BadRequest("Body is required");
			if (string.IsNullOrWhiteSpace(body.UserName)) return BadRequest($"{nameof(body.UserName)} is required");

			string path = Security.UserPath(HttpContext);
			var usernameEscaped = Validator.StringEscape(body.UserName.Trim());
			var strRiskRatingNames = string.Join(",", body.AmountRangesByRiskRating.Select(amountRangeByRiskRating => $"'{amountRangeByRiskRating.RiskRating}'"));
			
			if (body.AmountRangesByRiskRating.Count == 0) return BadRequest($"{nameof(body.AmountRangesByRiskRating)} is empty");
			string employeeName = Security.UserName(HttpContext);

			var scriptAmountRangeByCurrencies = new StringBuilder();
			foreach (var amountRangeByRiskRating in body.AmountRangesByRiskRating)
			{
				if (string.IsNullOrWhiteSpace(amountRangeByRiskRating.RiskRating)) return BadRequest($"{nameof(amountRangeByRiskRating.RiskRating)} is required");
				if (amountRangeByRiskRating.TransactionTypes == null || amountRangeByRiskRating.TransactionTypes.Count==0) return BadRequest($"{nameof(amountRangeByRiskRating.TransactionTypes)} is required");

				scriptAmountRangeByCurrencies.AppendLine($"riskRating = riskRatings.FindRiskRating('{amountRangeByRiskRating.RiskRating}');");
				var transactionTypesAsText = string.Join(",", amountRangeByRiskRating.TransactionTypes.Select(type => $"'{type.ToString()}'").ToArray());
				scriptAmountRangeByCurrencies.AppendLine($"amountRangeByCurrencies = agentAmountRanges.CreateAmountRangeByCurrencies(riskRating, {{{transactionTypesAsText}}});");

				if (amountRangeByRiskRating.AmountRangeByCurrencies.Count == 0) return BadRequest($"{nameof(amountRangeByRiskRating.AmountRangeByCurrencies)} is empty");
				foreach (var amountRangeByCurrency in amountRangeByRiskRating.AmountRangeByCurrencies)
				{
					var currencyCode = amountRangeByCurrency.CurrencyCode;
					if (string.IsNullOrWhiteSpace(currencyCode)) return BadRequest($"{nameof(currencyCode)} is required");
					var maxAmount = amountRangeByCurrency.MaxAmount;
					var minAmount = amountRangeByCurrency.MinAmount;
					if (!Coinage.Exists(currencyCode)) return BadRequest($"{nameof(currencyCode)} '{currencyCode}' is not valid");
					if (maxAmount < 0) return PrefabBadRequest($"{nameof(maxAmount)} must be greater or equal than 0");
					if (minAmount < 0) return PrefabBadRequest($"{nameof(minAmount)} must be greater or equal than 0");
					if (maxAmount < minAmount) return PrefabBadRequest($"{nameof(minAmount)} cannot be greater than {nameof(maxAmount)}");

					scriptAmountRangeByCurrencies.AppendLine($"amountRangeByCurrencies.SetAmountRange(Currency('{amountRangeByCurrency.CurrencyCode}',{maxAmount}), Currency('{amountRangeByCurrency.CurrencyCode}',{minAmount}), Now, '{employeeName}');");
				}
			}

			return await ExchangeManagerAPI.ExchangeManager.PerformChkThenCmdAsync(HttpContext, $@"
            {{
				existsAllRiskRating = riskRatings.ExistsAllRiskRating({{{strRiskRatingNames}}});
                Check(existsAllRiskRating) Error 'Sorry, some risk rating does not exist. Please check again the list.';
            }}
			",$@"
				{{
					agent = marketplace.SearchAgent('{path}');
					user = agent.SearchUser('{usernameEscaped}');
					agentAmountRanges = AgentAmountRanges();
					{scriptAmountRangeByCurrencies.ToString()}
					user.SetAmountRanges(agentAmountRanges);
				}}
			");
		}

		
		[HttpPost("api/exchange/riskRating/assignation/copy")]
		[Authorize(Roles = "c40")]
		public async Task<IActionResult> CopyRiskRatingAndAmountRangeAsync([FromBody] RiskRatingAndAmountRangeCopyBody body)
		{
			if (body == null) return BadRequest("Body is required");
			if (string.IsNullOrWhiteSpace(body.FromUserName)) return BadRequest($"{nameof(body.FromUserName)} is required");
			if (string.IsNullOrWhiteSpace(body.ToUserName)) return BadRequest($"{nameof(body.ToUserName)} is required");

			string path = Security.UserPath(HttpContext);
			var fromUsernameEscaped = Validator.StringEscape(body.FromUserName.Trim());
			var toUsernameEscaped = Validator.StringEscape(body.ToUserName.Trim());
			string employeeName = Security.UserName(HttpContext);

			return await ExchangeManagerAPI.ExchangeManager.PerformCmdAsync(HttpContext, $@"
				{{
					agent = marketplace.SearchAgent('{path}');
					fromUser = agent.SearchUser('{fromUsernameEscaped}');
					toUser = agent.SearchUser('{toUsernameEscaped}');
					toUser.CopyAmountRangesFrom(fromUser, Now, '{employeeName}');
				}}
			");
		}

		public ContentResult PrefabBadRequest(string message, string stack = "No stack.", string comando = "No command error info.", string info = "No Error info.")
        {
			ApiError error = new ApiError(message,
					stack,
					comando,
					info);

			var contentResult = new ContentResult
			{
				ContentType = "application/json",
				Content = JsonConvert.SerializeObject(error),
				StatusCode = 400
			};
			return contentResult;
		}

		[HttpGet("api/exchange/conversion")]
		[Authorize(Roles = "c2,player")]
		public async Task<IActionResult> ExchangeRateAsync(string from, string to, decimal amount)
		{
			if (string.IsNullOrWhiteSpace(from)) return BadRequest($"{nameof(from)} is required");
			if (string.IsNullOrWhiteSpace(to)) return BadRequest($"{nameof(to)} is required");
			if (from == to) return BadRequest($"Conversion for the same currency is not valid");
			if (amount <= 0) return BadRequest($"{nameof(amount)} must be greater than 0");

			var result = await ExchangeManagerAPI.ExchangeManager.PerformChkThenQryAsync(HttpContext, $@"
					existsRate = marketplace.ExistsRate(now, '{from}', '{to}');
					Check(existsRate) Error 'There is no conversion rate for today for {from}-{to}';
				",
				$@"
				{{
					rate = marketplace.SearchRate(now, '{from}', '{to}');
					if( rate.Exists )
					{{
						result = marketplace.Convert(now, '{from}', '{to}', {amount});
						print result.Value value;
						print result.CurrencyCodeAsText currencyCode;
						print result.ToDisplayFormat() valueFormatted;
					}}
					print rate.Exists exists;
					print rate.Id rateId;
					print rate.Rate rate;
					print rate.SaleRateBaseCurrency saleRateBaseCurrency;
					print rate.SaleRateQouteCurrency saleRateQouteCurrency;
					print rate.SaleRatePrice saleRatePrice; 
				}}
			");
			return result;
		}

		private const int MAX_DECIMAL_PLACES = 8;

		[HttpPost("api/exchange/rate")]
		[Authorize(Roles = "c3")]
		public async Task<IActionResult> AddExchangeRateAsync([FromBody]ExchangeRateBody body)
		{
			if (body == null) return NotFound("Body is required");
			if (body.PurchasePrice <= 0) return NotFound($"{nameof(body.PurchasePrice)} must be greater than 0");
			if (body.SalePrice <= 0) return NotFound($"{nameof(body.SalePrice)} must be greater than 0");
			if (body.PurchasePrice > body.SalePrice) return NotFound($"{nameof(body.SalePrice)} must be greater than {nameof(body.PurchasePrice)}");
			if (string.IsNullOrWhiteSpace(body.FromCurrencyCode)) return NotFound($"{nameof(body.FromCurrencyCode)} is required");
			if (string.IsNullOrWhiteSpace(body.ToCurrencyCode)) return NotFound($"{nameof(body.ToCurrencyCode)} is required");
			if (body.FromCurrencyCode == body.ToCurrencyCode) return  BadRequest($"{nameof(body.ToCurrencyCode)} must be distinct of {nameof(body.FromCurrencyCode)}");

			int countDecimalPlaces = BitConverter.GetBytes(decimal.GetBits(body.PurchasePrice)[3])[2];
			if (countDecimalPlaces > MAX_DECIMAL_PLACES) return NotFound($"Number of decimal places for {nameof(body.PurchasePrice)} was exceeded");
			countDecimalPlaces = BitConverter.GetBytes(decimal.GetBits(body.SalePrice)[3])[2];
			if (countDecimalPlaces > MAX_DECIMAL_PLACES) return NotFound($"Number of decimal places for {nameof(body.SalePrice)} was exceeded");

			string employeeName = Security.UserName(HttpContext);

			string command = $@"
				{{
					Eval('conversionSpreadNumber =' + marketplace.NewConversionSpreadNumber() + ';');
					rate = marketplace.CreateConversionSpread(conversionSpreadNumber, Now, itIsThePresent, '{body.FromCurrencyCode}', '{body.ToCurrencyCode}', {body.SalePrice}, {body.PurchasePrice}, '{employeeName}');
				}}
			";
			IActionResult result = await ExchangeManagerAPI.ExchangeManager.PerformCmdAsync(HttpContext, command);

			return Ok(result);
		}

		[HttpGet("api/exchange/rates")]
		[Authorize(Roles = "c4")]
		public IActionResult ListExchangeRates()
		{
			var exchangeRates = Logic.ExchangeRates.CurrentExchangeRates();
			var exchangeRatesDTO = exchangeRates.MapToExchangeRatesDTO();
			return Ok(exchangeRatesDTO);
		}

		[HttpPost("api/exchange/drafts/deposit")]
		[Authorize(Roles = "c5")]
		public async Task<IActionResult> AddDepositDraftAsync([FromBody]DepositCreationBody body)
		{
			if (body == null) return BadRequest("Body is required");
			if (body.Amount <= 0) return BadRequest($"{nameof(body.Amount)} must be greater than 0");
			if (body.EntityId <= 0) return BadRequest($"{nameof(body.EntityId)} must be greater than 0");
			if (!body.PaymentMethod.HasValue) return BadRequest($"{nameof(body.PaymentMethod)} is required");
			if (string.IsNullOrWhiteSpace(body.ToCurrencyCode)) return BadRequest($"{nameof(body.ToCurrencyCode)} is required");
			if (string.IsNullOrWhiteSpace(body.ToIdentifier)) return BadRequest($"{nameof(body.ToIdentifier)} is required");

			string path = Security.UserPath(HttpContext);
            string employeeName = Security.UserName(HttpContext);
			string domain = HttpContext.Request.Host.Host;
			string depositor = (body.Depositor == null) ? "" : body.Depositor;
			string voucher = (body.Voucher == null) ? "" : body.Voucher;
			string voucherUrl = (body.VoucherUrl == null) ? "" : body.VoucherUrl;


			var accountCommand = string.IsNullOrWhiteSpace(body.AccountNumber) ?
					$"account = customer.FindAccountByCurrency('{body.ToCurrencyCode}');" :
					$"account = customer.FindAccount('{body.AccountNumber}');";

			var result = await ExchangeManagerAPI.ExchangeManager.PerformChkThenCmdAsync(HttpContext, $@"
				existsCustomer = company.ExistsCustomerByIdentifier('{body.ToIdentifier}');
                Check(existsCustomer) Error 'Customer {body.ToIdentifier} does not exist';
				if (existsCustomer) 
				{{
					customer = company.CustomerByIdentifier('{body.ToIdentifier}');
					Check(customer.IsApproved()) Error 'Customer {body.ToIdentifier} is not approved';
				}}
				domain = company.Sales.DomainFrom('{domain}');
				isTransactionAllowed = domain.AllowedTransactions.ItsEnabled({TransactionType.Deposit});
				Check(isTransactionAllowed) Error 'Transaction {TransactionType.Deposit} is not enabled';
				existsDomainInMarketplace = company.Sales.CurrentStore.ExistsDomain(domain.Id);
				Check(existsDomainInMarketplace) Error 'Domain {domain} does not belong to marketplace';
			", 
			$@"
			{{
				domain = company.Sales.DomainFrom('{domain}');
				customer = company.CustomerByIdentifier('{body.ToIdentifier}');
				{accountCommand}
				Eval('transactionNumber =' + marketplace.NewTransationNumber() + ';');

				drivers = company.System.DriverManagers.PaymentProcessorsAndActionsByDomains.SearchForX(domain.AgentId, domain.Url);
				processor = drivers.SearchByX({TransactionType.Deposit}, '{body.ToCurrencyCode}', {body.PaymentMethod.Value}, {body.EntityId});

				processorAccountId = guardian.Accounts().SearchBy(processor).Id;
				transaction = marketplace.From(transactionNumber, account, '{path}', '{employeeName}', domain).Deposit(Now, itIsThePresent, Currency('{body.ToCurrencyCode}',{body.Amount}), '{employeeName}', '{depositor}', '{voucher}', '{voucherUrl}', '{body.Description}', processor, processorAccountId);

				print transactionNumber authorizationId;
				print transaction.BatchTransactions.TransactionsNumber batchNumber;
				print Now now;
				print customer.AccountNumber customerNumber;
				print company.Sales.CurrentStore.Id storeId;
				print domain.Id domainId;
				print domain.AgentId agentId;
				print marketplace.AreAllWithdrawalsManagedAsDisbursements isGuardianOn;

				agent = marketplace.SearchAgent('{path}');
				user = agent.SearchUser('{employeeName}');
				print user.IsInRange(Currency('{body.ToCurrencyCode}',{body.Amount})) isAmountInAgentRange;
			}}
			");

            if (!(result is OkObjectResult))
			{
				return result;
			}

			var o = (OkObjectResult)result;
			var json = o.Value.ToString();
			var idResponse = JsonConvert.DeserializeObject<DraftTransactionResponse>(json);


			if (idResponse.AuthorizationId > 0 && idResponse.IsAmountInAgentRange)
			{
				string appToken = string.Empty;
				if (SecurityConfiguration.ItsSecuritySchemeConfigured())
				{
					string header = HttpContext.Request.Headers["Authorization"];
					string[] tokens = header.Split(' ');
					appToken = tokens[1];
                }

                bool itIsThePresent = true;
				DespositBody despositBody = new DespositBody(
					string.IsNullOrWhiteSpace(body.AccountNumber) ? body.ToCurrencyCode : body.AccountNumber,
					body.ToIdentifier,
					body.Amount,
					$"{nameof(TransactionType.Deposit)} {body.Description}",
					DateTime.Now,
					string.Empty,
					employeeName,
					new GamesEngine.Domains.Domain(itIsThePresent, idResponse.DomainId, domain, (Agents)idResponse.AgentId),
					path,
					body.PaymentMethod.Value,
					body.EntityId,
					idResponse.StoreId,
					string.Empty,
					string.Empty,
					string.Empty,
					string.Empty,
					string.Empty,
					0,
                    idResponse.AuthorizationId.ToString()
                );

                int agentId = (int)Security.UserAgent(HttpContext);
                DespositResponse despositResponse = PaymentChannels.DepositX(itIsThePresent, agentId, domain, body.ToCurrencyCode, despositBody);

				switch (despositResponse.Status)
				{
					case TransactionStatus.APPROVED:
						int driverAuthorizationId = despositResponse.AuthorizationId;

						result = await ExchangeManagerAPI.ExchangeManager.PerformCmdAsync(HttpContext, $@"
							{{
								transaction = marketplace.FindDraftTransaction({idResponse.AuthorizationId}, '{path}', '{employeeName}');
								transactionCompleted = transaction.Approve(Now, itIsThePresent, {driverAuthorizationId}, '{employeeName}');
								result = transactionCompleted.Result;
								print result.Net.Sign netsign;
								print result.Net.Value net;
								print result.Net.ToDisplayFormat() netFormatted;
								print result.Gross.Sign grosssign;
								print result.Gross.Value gross;
								print result.Gross.ToDisplayFormat() grossFormatted;
								print result.Profit.Sign profitsign;
								print result.Profit.Value profit;
								print result.Profit.ToDisplayFormat() profitFormatted;
								print result.Amount.Sign amountsign;
								print result.Amount.Value amount;
								print result.Amount.ToDisplayFormat() amountFormatted;
							}}
						");

						if (!(result is OkObjectResult))
						{
							return BadRequest($@"Error:{((ObjectResult)result).Value}");
						}

						o = (OkObjectResult)result;
						json = o.Value.ToString();
						TransactionResult transactionResult = JsonConvert.DeserializeObject<TransactionResult>(json);
						idResponse.Approval = transactionResult;
						break;
					case TransactionStatus.DENIED:
						if (idResponse.IsAmountInAgentRange)
						{
							result = await ExchangeManagerAPI.ExchangeManager.PerformCmdAsync(HttpContext, $@"
								{{
									transaction = marketplace.FindDraftTransaction({idResponse.AuthorizationId}, '{path}', '{employeeName}');
									transaction.Deny(Now, itIsThePresent, '{employeeName}', 'driver rejection. Number {despositResponse.AuthorizationId}');
								}}
							");
							idResponse.Rejected = true;
						}
						break;
					default:
						return BadRequest($"No {nameof(TransactionStatus)} {despositResponse.Status} implemented yet.");
				}
			}

			return Ok(idResponse);
		}

		public async Task<IActionResult> GetProcessorsInfoWithAccountAsync()
		{
			var result = await ExchangeManagerAPI.ExchangeManager.PerformQryAsync(HttpContext, $@"
				{{
					processorsWithDistinctKey = company.System.PaymentProcessor.SearchProcessorsWithDistinctKey();
					for (processors:processorsWithDistinctKey)
					{{
						processor = processors;
						print processor.Alias alias;
						print processor.Entity.Name entity;
						print processor.PaymentMethodAsText paymentMethod;
						print processor.Transactions.TransactionsAsText transactionType;
						print processor.CurrencyIso4217Code currencyCode;

						account = guardian.Accounts().SearchBy(processor);
						print account.Id accountId;
					}}
				}}
				");
			if (!(result is OkObjectResult)) throw new GameEngineException($@"Error:{((ObjectResult)result).Value}");
			return result;
		}

		private const string ALL_SELECTED = "all";
		private const string ALL_CASHIERS = "all";
		
		[HttpGet("api/exchange/deposits")]
		[Authorize(Roles = "c6")]
		public async Task<IActionResult> ListDepositsAsync(DateTime startDate, DateTime endDate, string state, string address, string transactionId, string cashierName, string domain, int initialIndex, int amountOfRows)
		{
			if (startDate == default(DateTime)) return BadRequest($"{nameof(startDate)} is required");
			if (endDate == default(DateTime)) return BadRequest($"{nameof(endDate)} is required");
			if (startDate.Hour != 0 || startDate.Minute != 0 || startDate.Second != 0) return BadRequest($"{nameof(startDate)} {startDate} is not valid");
			if (endDate.Hour != 0 || endDate.Minute != 0 || endDate.Second != 0) return BadRequest($"{nameof(endDate)} {endDate} is not valid");
			if (string.IsNullOrWhiteSpace(state)) return BadRequest($"{nameof(state)} is required");
			if (string.IsNullOrWhiteSpace(address)) return BadRequest($"{nameof(address)} is required");
			if (string.IsNullOrWhiteSpace(transactionId)) return BadRequest($"{nameof(transactionId)} is required");
			if (string.IsNullOrWhiteSpace(cashierName)) return BadRequest($"{nameof(cashierName)} is required");
			if (string.IsNullOrWhiteSpace(domain)) return BadRequest($"{nameof(domain)} is required");
			if (initialIndex < 0) return BadRequest($"{nameof(initialIndex)} {initialIndex} is not valid");
			if (amountOfRows < 0) return BadRequest($"{nameof(amountOfRows)} {amountOfRows} is not valid");

			if (initialIndex == 0 && amountOfRows == 0) 
				amountOfRows = CreditNotes.MAXIMUM_AMOUNT_OF_ROWS_ALLOWED;

			string cashier = cashierName.ToLower().Trim();

			var resultProcessors = await GetProcessorsInfoWithAccountAsync();
			OkObjectResult o = (OkObjectResult)resultProcessors;
			string json = o.Value.ToString();
			var response = JsonConvert.DeserializeObject<DepositsResponse>(json);

			var transactions = Logic.Deposits.FilteredBy(startDate, endDate, state, address, transactionId, cashier, domain, initialIndex, amountOfRows);
			response.Transactions = transactions;
			return Ok(response);
		}

		[HttpPut("api/exchange/drafts/deposits/{id}/voucher")]
        [AllowAnonymous]
        public IActionResult UpdateVoucherUrlInDeposit(int id, [FromBody] VoucherBody body)
		{
			if (body == null) return NotFound("Body is required");
			if (string.IsNullOrWhiteSpace(body.VoucherUrl)) return NotFound($"{nameof(body.VoucherUrl)} is required");
			if (id <= 0) return NotFound($"{nameof(id)} {id} is not valid");

			if (Integration.UseKafka)
			{
				Integration.Kafka.Send(
					true,
					$"{KafkaMessage.VOUCHER_URL_EXCHANGE_CONSUMER_PREFIX}_{Integration.Kafka.TopicForTransacctions}",
					new VoucherUrlMessage(TransactionType.Deposit, id, body.VoucherUrl)
				);
			}
			return Ok();
		}

		private async Task<IActionResult> RetrieveTransactionAsync(int id, string path, string employeeName)
        {
            IActionResult result = await ExchangeManagerAPI.ExchangeManager.PerformChkThenQryAsync(HttpContext, $@"
					existsAgent = marketplace.ExistsAgent('{path}');
					Check(existsAgent) Error 'Agent {path} does not exist.';

					existTransaction = marketplace.ExistsDraftTransaction({id}, '{path}', '{employeeName}');
					Check(existTransaction) Error 'Sorry, transaction {id} does not exist.';

					if (existsAgent && existTransaction)
					{{
						transaction = marketplace.FindDraftTransaction({id}, '{path}', '{employeeName}');
						agent = marketplace.SearchAgent('{path}');
						user = agent.SearchUser('{employeeName}');
						Check(user.IsInRange(transaction.Amount)) Error 'Sorry, you have exceeded your allowed limits, this transaction cannot be processed.';
					}}
				",
				$@"
				{{
					transaction = marketplace.FindDraftTransaction({id}, '{path}', '{employeeName}');
					agent = marketplace.SearchAgent('{path}');
					user = agent.SearchUser('{employeeName}');
					print transaction.Amount.Value value;
					print transaction.Amount.CurrencyCodeAsText currencyCodeAsText;
					print user.IsInRange(transaction.Amount) isItInRange;
					print transaction.AccountNumberIdentificator accountNumberIdentificator;
					print transaction.CustomerIdentificationNumber identificationNumber;
					print transaction.Domain.Id domainId;
					print transaction.Domain.Url domain;
					print transaction.Domain.AgentId domainAgentId;
					print company.Sales.CurrentStore.Id storeId;
					print transaction.Processor.Entity.Id entityId;
					print transaction.Processor.PaymentMethodAsText paymentMethodName;
					print transaction.Processor.PaymentMethodAsString paymentMethodType;
					print transaction.Processor.TransactionTypeAsString transactionType;

				}}


			");
			if (!(result is OkObjectResult))
			{
				return result;
			}

			OkObjectResult o = (OkObjectResult)result;
			string json = o.Value.ToString();
			var agentInRange = JsonConvert.DeserializeObject<AgentValidationInRange>(json);
			return Ok(agentInRange);
		}

		[HttpPost("api/exchange/drafts/deposits/{id}/approval")]
		[Authorize(Roles = "c7")]
		public async Task<IActionResult> ApproveDepositDraftAsync(int id)
		{
			if (id <= 0) return BadRequest($"{nameof(id)} must be greater than 0");

			string path = Security.UserPath(HttpContext);
			string employeeName = Security.UserName(HttpContext);

			var agentInRangeResult = await RetrieveTransactionAsync(id, path, employeeName);
			if (!(agentInRangeResult is OkObjectResult)) return agentInRangeResult;

            var agentInRange = (AgentValidationInRange)((OkObjectResult)agentInRangeResult).Value;

			bool itIsThePresent = true;
			DespositBody despositBody = new DespositBody(
					agentInRange.accountNumberIdentificator,
					agentInRange.identificationNumber,
					agentInRange.value,
					$"{nameof(TransactionType.Deposit)}",
					DateTime.Now,
					string.Empty,
					employeeName,
					new GamesEngine.Domains.Domain(itIsThePresent, agentInRange.domainId, agentInRange.domain, Agents.TEST_BOOK),
					path,
                    agentInRange.paymentMethodType.Value,
					agentInRange.entityId,
					agentInRange.storeId,
					string.Empty,
					string.Empty,
					string.Empty,
					string.Empty,
					string.Empty,
					0
					);

			int agentId = (int)Security.UserAgent(HttpContext);
            DespositResponse despositResponse = PaymentChannels.DepositX(itIsThePresent, agentId, agentInRange.domain, agentInRange.currencyCodeAsText, despositBody);

			switch (despositResponse.Status)
			{
				case TransactionStatus.APPROVED:
					var command = $@"
					{{
						transaction = marketplace.FindDraftTransaction({id}, '{path}', '{employeeName}');
						transactionCompleted = transaction.Approve(Now, itIsThePresent, {despositResponse.AuthorizationId},'{employeeName}');
						result = transactionCompleted.Result;
						print result.Net.Sign netsign;
						print result.Net.Value net;
						print result.Net.ToDisplayFormat() netFormatted;
						print result.Gross.Sign grosssign;
						print result.Gross.Value gross;
						print result.Gross.ToDisplayFormat() grossFormatted;
						print result.Profit.Sign profitsign;
						print result.Profit.Value profit;
						print result.Profit.ToDisplayFormat() profitFormatted;
						print result.Amount.Sign amountsign;
						print result.Amount.Value amount;
						print result.Amount.ToDisplayFormat() amountFormatted;
					}}
				";
					var result = await ExchangeManagerAPI.ExchangeManager.PerformCmdAsync(HttpContext, command);

					if (!(result is OkObjectResult))
					{
						if (result is ContentResult contentResult) return BadRequest($"Error: {contentResult.Content}");
						return BadRequest($"Error:{result}");
					}

					var o = (OkObjectResult)result;
					var json = o.Value.ToString();
					TransactionResult transactionResult = JsonConvert.DeserializeObject<TransactionResult>(json);

					return Ok(transactionResult);
				case TransactionStatus.DENIED:
					return Ok(despositResponse);
				default:
					throw new GameEngineException("No implemented yet.");
			}
		}

		[HttpPost("api/exchange/drafts/deposits/{id}/rejection")]
		[Authorize(Roles = "c8")]
		public async Task<IActionResult> RejectDepositDraftAsync(int id, [FromBody]TransactionRejectionBody body)
		{
			if (id <= 0) return BadRequest($"{nameof(id)} must be greater than 0");
			if (body == null) return BadRequest("Body is required");
			if (string.IsNullOrWhiteSpace(body.Reason)) return BadRequest($"{nameof(body.Reason)} is required");

			string path = Security.UserPath(HttpContext);
			string employeeName = Security.UserName(HttpContext);

			var agentInRangeResult = await RetrieveTransactionAsync(id, path, employeeName);
			if (!(agentInRangeResult is OkObjectResult)) return agentInRangeResult;

            var command = $@"
				{{
					transaction = marketplace.FindDraftTransaction({id}, '{path}', '{employeeName}');
					transaction.Deny(Now, itIsThePresent, '{employeeName}', '{body.Reason}');
				}}
			";
			var result = await ExchangeManagerAPI.ExchangeManager.PerformCmdAsync(HttpContext, command);
			return result;
		}

        public const int DaysToBecomeUseless = 7;
		[HttpPost("api/exchange/drafts/withdrawal")]
		[Authorize(Roles = "c9")]
		public async Task<IActionResult> AddWithdrawalDraftAsync([FromBody]WithdrawalCreationBody body)
		{
			if (body == null) return BadRequest("Body is required");
			if (body.Amount <= 0) return BadRequest($"{nameof(body.Amount)} must be greater than 0");
			if (body.MinerFee < 0) return BadRequest($"{nameof(body.MinerFee)} must be greater or equal than 0");
			if (body.EntityId <= 0) return BadRequest($"{nameof(body.EntityId)} must be greater than 0");
			if (!body.PaymentMethod.HasValue) return BadRequest($"{nameof(body.PaymentMethod)} is required");
			if (string.IsNullOrWhiteSpace(body.FromCurrencyCode)) return BadRequest($"{nameof(body.FromCurrencyCode)} is required");
			if (string.IsNullOrWhiteSpace(body.FromIdentifier)) return BadRequest($"{nameof(body.FromIdentifier)} is required");
			if (string.IsNullOrWhiteSpace(body.RealAccount)) return BadRequest($"{nameof(body.RealAccount)} is required");

			string path = Security.UserPath(HttpContext);
			string employeeName = Security.UserName(HttpContext);
			string batchPath = path + '/' + employeeName;
			string domain = HttpContext.Request.Host.Host;

			string accountCommand = string.IsNullOrWhiteSpace(body.accountNumber) ? 
				$"account = customer.FindAccountByCurrency('{body.FromCurrencyCode}');" : 
				$"account = customer.FindAccount('{body.accountNumber}');";

			var result = await ExchangeManagerAPI.ExchangeManager.PerformChkThenCmdAsync(HttpContext, $@"
				existsCustomer = company.ExistsCustomerByIdentifier('{body.FromIdentifier}');
                Check(existsCustomer) Error 'Customer {body.FromIdentifier} does not exist';
				if (existsCustomer) 
				{{
					customer = company.CustomerByIdentifier('{body.FromIdentifier}');
					Check(customer.IsApproved()) Error 'Customer {body.FromIdentifier} is not approved';
				}}
				domain = company.Sales.DomainFrom('{domain}');
				isTransactionAllowed = domain.AllowedTransactions.ItsEnabled({TransactionType.Withdrawal});
				Check(isTransactionAllowed) Error 'Transaction {TransactionType.Withdrawal} is not enabled';
				existsDomainInMarketplace = company.Sales.CurrentStore.ExistsDomain(domain.Id);
				Check(existsDomainInMarketplace) Error 'Domain {domain} does not belong to marketplace';
				existsBatch = marketplace.Batches.ExistAgentBatch('{batchPath}');
				Check(existsBatch) Error 'Agent batch {batchPath} does not exist.';
				if (existsBatch)
				{{
					agentBatch = marketplace.SearchAgentBatch('{batchPath}');
					Check(agentBatch.BatchTransactions.HasAvailable(Currency('{body.FromCurrencyCode}',{body.Amount}))) Error 'Agent batch {batchPath} does not have enough funds for {body.FromCurrencyCode}{body.Amount}.';
				}}
			",
			$@"
			{{
				domain = company.Sales.DomainFrom('{domain}');
				drivers = company.System.DriverManagers.PaymentProcessorsAndActionsByDomains.SearchForX(domain.AgentId, domain.Url);
				processor = drivers.SearchByX({TransactionType.Withdrawal}, '{body.FromCurrencyCode}', {body.PaymentMethod.Value}, {body.EntityId});

				customer = company.CustomerByIdentifier('{body.FromIdentifier}');
				{accountCommand}
				Eval('transactionNumber =' + marketplace.NewTransationNumber() + ';');
				transaction = marketplace.From(transactionNumber, account, '{path}', '{employeeName}', domain);
				print transaction.Id transactionId;
				print account.Identificator account;
				print customer.AccountNumber customerNumber;
				processorAccount = guardian.Accounts().SearchByProcessor(processor.ProcessorKey);
				print processorAccount.Id processorAccountId;
				print company.Sales.CurrentStore.Id storeId;
				print domain.AgentId agentId;
				print domain.Url domainUrl;
				print domain.Id domainId;
			}}
			");
			if (!(result is OkObjectResult))
			{
				return result;
			}
			var o = (OkObjectResult)result;
			var json = o.Value.ToString();
			var tempTransactionResponse = JsonConvert.DeserializeObject<TempTransactionResponse>(json);

            var useless = DateTime.Now.AddDays(DaysToBecomeUseless);

            var authorizationNumberFromLockBalance = await PaymentChannels.LockBalanceAsync(
				tempTransactionResponse.CustomerNumber,
				body.Amount,
				body.FromCurrencyCode,
				body.Description,
				tempTransactionResponse.TransactionId.ToString(),
				tempTransactionResponse.Account,
				employeeName,
				tempTransactionResponse.StoreId, 
				useless,
                tempTransactionResponse.AgentId,
                tempTransactionResponse.DomainUrl,
                tempTransactionResponse.DomainId,
                body.PaymentMethod.Value,
                body.EntityId
            );

			if (authorizationNumberFromLockBalance == ASITenantDriver.FAKE_TICKET_NUMBER)
			{
				await ExchangeManagerAPI.ExchangeManager.PerformCmdAsync(HttpContext, $@"
				{{
					transaction = marketplace.SearchTemporaryDefinition({tempTransactionResponse.TransactionId}, '{path}', '{employeeName}').Remove();
				}}
				");
			}

			var feeScript = body.MinerFee == 0 ? $"NoFee('{body.FromCurrencyCode}')" : $"MinerFee(Currency('{body.FromCurrencyCode}',{body.MinerFee}))";
			string realAccount = body.RealAccount;
			var errorMessage = "Sorry, your withdrawal cannot be completed at this moment. Please try again. If the problem persists, please contact customer service.";
			result = await ExchangeManagerAPI.ExchangeManager.PerformChkThenCmdAsync(HttpContext, $@"
					Check({authorizationNumberFromLockBalance} != {ASITenantDriver.FAKE_TICKET_NUMBER}) Error '{errorMessage}';
				", $@"
				{{
					domain = company.Sales.DomainFrom('{domain}');
					drivers = company.System.DriverManagers.PaymentProcessorsAndActionsByDomains.SearchForX(domain.AgentId, domain.Url);
					processor = drivers.SearchByX({TransactionType.Withdrawal}, '{body.FromCurrencyCode}', {body.PaymentMethod.Value}, {body.EntityId});

					processorAccountId = guardian.Accounts().SearchBy(processor).Id;
					transaction = marketplace.SearchTemporaryDefinition({tempTransactionResponse.TransactionId}, '{path}', '{employeeName}').Withdraw(Now, itIsThePresent, Currency('{body.FromCurrencyCode}',{body.Amount}), {authorizationNumberFromLockBalance}, '{employeeName}', '{realAccount}', '{body.Description}', {feeScript}, processor, processorAccountId);
					print transactionNumber authorizationId;
					print transaction.BatchTransactions.TransactionsNumber batchNumber;
					print Now now;
				}}");
			if (!(result is OkObjectResult))
			{
				return result;
			}
			o = (OkObjectResult)result;
			json = o.Value.ToString();
			var idResponse = JsonConvert.DeserializeObject<DraftTransactionResponse>(json);

			return Ok(idResponse);
		}

		[HttpGet("api/exchange/withdrawals")]
		[Authorize(Roles = "c10")]
		public async Task<IActionResult> ListWithdrawalsAsync(DateTime startDate, DateTime endDate, string state, string address, string transactionId, string cashierName, string domain, int initialIndex, int amountOfRows)
		{
			if (startDate == default(DateTime)) return BadRequest($"{nameof(startDate)} is required");
			if (endDate == default(DateTime)) return BadRequest($"{nameof(endDate)} is required");
			if (startDate.Hour != 0 || startDate.Minute != 0 || startDate.Second != 0) return BadRequest($"{nameof(startDate)} {startDate} is not valid");
			if (endDate.Hour != 0 || endDate.Minute != 0 || endDate.Second != 0) return BadRequest($"{nameof(endDate)} {endDate} is not valid");
			if (string.IsNullOrWhiteSpace(state)) return BadRequest($"{nameof(state)} is required");
			if (string.IsNullOrWhiteSpace(address)) return BadRequest($"{nameof(address)} is required");
			if (string.IsNullOrWhiteSpace(transactionId)) return BadRequest($"{nameof(transactionId)} is required");
			if (string.IsNullOrWhiteSpace(cashierName)) return BadRequest($"{nameof(cashierName)} is required");
			if (string.IsNullOrWhiteSpace(domain)) return BadRequest($"{nameof(domain)} is required");
			if (initialIndex < 0) return BadRequest($"{nameof(initialIndex)} {initialIndex} is not valid");
			if (amountOfRows < 0) return BadRequest($"{nameof(amountOfRows)} {amountOfRows} is not valid");

			if (initialIndex == 0 && amountOfRows == 0)
				amountOfRows = CreditNotes.MAXIMUM_AMOUNT_OF_ROWS_ALLOWED;

			string cashier = cashierName.ToLower().Trim();

			var resultProcessors = await GetProcessorsInfoWithAccountAsync();
			OkObjectResult o = (OkObjectResult)resultProcessors;
			string json = o.Value.ToString();
			var response = JsonConvert.DeserializeObject<WithdrawalsResponse>(json);

			var transactions = Logic.Withdrawals.FilteredBy(startDate, endDate, state, address, transactionId, cashier, domain, initialIndex, amountOfRows);
			response.Transactions = transactions;
			return Ok(response);
		}

		[HttpPut("api/exchange/drafts/withdrawals/{id}/voucher")]
        [AllowAnonymous]
        public IActionResult UpdateVoucherUrlInWithdrawal(int id, [FromBody] VoucherBody body)
		{
			if (body == null) return NotFound("Body is required");
			if (string.IsNullOrWhiteSpace(body.VoucherUrl)) return NotFound($"{nameof(body.VoucherUrl)} is required");
			if (id <= 0) return NotFound($"{nameof(id)} {id} is not valid");

			if (Integration.UseKafka)
			{
				Integration.Kafka.Send(
					true,
					$"{KafkaMessage.VOUCHER_URL_EXCHANGE_CONSUMER_PREFIX}_{Integration.Kafka.TopicForTransacctions}",
					new VoucherUrlMessage(TransactionType.Withdrawal, id, body.VoucherUrl)
				);
			}
			return Ok();
		}

		[HttpPost("api/exchange/drafts/withdrawals/{id}/approval")]
		[Authorize(Roles = "c11")]
		public async Task<IActionResult> ApproveWithdrawalDraftAsync(int id)
		{
			if (id <= 0) return BadRequest($"{nameof(id)} must be greater than 0");

			string path = Security.UserPath(HttpContext);
			string employeeName = Security.UserName(HttpContext);
			string batchPath = path + '/' + employeeName;
			var agentInRangeResult = await RetrieveTransactionAsync(id, path, employeeName);
			if (!(agentInRangeResult is OkObjectResult)) return agentInRangeResult;

            var agentInRange = (AgentValidationInRange)((OkObjectResult)agentInRangeResult).Value;

			if (!agentInRange.isItInRange) return BadRequest($"Sorry, you have exceeded your allowed limits, this transaction cannot be processed.");

			bool itIsThePresent = true;
			var withdrawalBody = new WithdrawBody(
				agentInRange.identificationNumber,
				agentInRange.value,
				$"{nameof(TransactionType.Withdrawal)}",
				DateTime.Now,
				string.Empty,
				agentInRange.domainId,
				agentInRange.domain,
                agentInRange.paymentMethodType.Value,
				agentInRange.entityId,
				agentInRange.storeId,
				string.Empty,
				string.Empty,
				string.Empty,
				string.Empty,
				new GamesEngine.Domains.Domain(false, agentInRange.domainId, agentInRange.domain, Agents.TEST_BOOK),
				employeeName,
				reference: id.ToString()
            );

            int agentId = (int)Security.UserAgent(HttpContext);
            var withdrawalResponse = PaymentChannels.WithDrawX(itIsThePresent, agentId, agentInRange.domain, agentInRange.currencyCodeAsText, withdrawalBody);

			switch (withdrawalResponse.Status)
			{
				case TransactionStatus.APPROVED:
					
					var result = await ExchangeManagerAPI.ExchangeManager.PerformChkThenCmdAsync(HttpContext, $@"
						existsBatch = marketplace.Batches.ExistAgentBatch('{batchPath}');
						Check(existsBatch) Error 'Agent batch {batchPath} does not exist.';
						if (existsBatch)
						{{
							agentBatch = marketplace.SearchAgentBatch('{batchPath}');
							Check(agentBatch.BatchTransactions.HasAvailable(Currency('{agentInRange.currencyCodeAsText}',{agentInRange.value}))) Error 'Agent batch {batchPath} does not have enough funds for {agentInRange.currencyCodeAsText}{agentInRange.value}.';
						}}

						existsAgent = marketplace.ExistsAgent('{path}');
						Check(existsAgent) Error 'Agent {path} does not exist.';
						if (existsAgent)
						{{
							agent = marketplace.SearchAgent('{path}');
							user = agent.SearchUser('{employeeName}');
							Check(user.IsInRange(Currency('{agentInRange.currencyCodeAsText}',{agentInRange.value}))) Error 'Agent {path} does not have enough funds for {agentInRange.currencyCodeAsText}{agentInRange.value}.';
						}}
					", 
					$@"
						{{
							transaction = marketplace.FindDraftTransaction({id}, '{path}', '{employeeName}');
							transactionCompleted = transaction.Approve(Now, itIsThePresent, '{employeeName}');
							result = transactionCompleted.Result;
							print result.Net.Sign netsign;
							print result.Net.Value net;
							print result.Net.ToDisplayFormat() netFormatted;
							print result.Gross.Sign grosssign;
							print result.Gross.Value gross;
							print result.Gross.ToDisplayFormat() grossFormatted;
							print result.Profit.Sign profitsign;
							print result.Profit.Value profit;
							print result.Profit.ToDisplayFormat() profitFormatted;
							print result.Amount.Sign amountsign;
							print result.Amount.Value amount;
							print result.Amount.ToDisplayFormat() amountFormatted;
							print result.Disbursement.Sign disbursementsign;
							print result.Disbursement.Value disbursement;
							print result.Disbursement.ToDisplayFormat() disbursementFormatted;
							print result.TotalFee.Sign feesign;
							print result.TotalFee.Value fee;
							print result.TotalFee.ToDisplayFormat() feeFormatted;
						}}
					");

					if (!(result is OkObjectResult))
					{
						if (result is ContentResult contentResult) return BadRequest($"Error: {contentResult.Content}");
						return result;
					}

					var o = (OkObjectResult)result;
					var json = o.Value.ToString();
					var transactionResult = JsonConvert.DeserializeObject<WithdrawalTransactionResult>(json);
					return Ok(transactionResult);
				case TransactionStatus.DENIED:
					return Ok(withdrawalResponse);
				default:
					throw new GameEngineException($"No {nameof(TransactionStatus)} {withdrawalResponse.Status} implemented yet.");
			}
		}

		[HttpPost("api/exchange/drafts/withdrawals/{id}/rejection")]
		[Authorize(Roles = "c12")]
		public async Task<IActionResult> RejectWithdrawalDraftAsync(int id, [FromBody]TransactionRejectionBody body)
		{
			if (id <= 0) return BadRequest($"{nameof(id)} must be greater than 0");
			if (body == null) return BadRequest("Body is required");
			if (string.IsNullOrWhiteSpace(body.Reason)) return BadRequest($"{nameof(body.Reason)} is required");

			string path = Security.UserPath(HttpContext);
			string employeeName = Security.UserName(HttpContext);

			var agentInRangeResult = await RetrieveTransactionAsync(id, path, employeeName);
			if (!(agentInRangeResult is OkObjectResult)) return agentInRangeResult;

            var command = $@"
				{{
					transaction = marketplace.FindDraftTransaction({id}, '{path}', '{employeeName}');
					transaction.Deny(Now, itIsThePresent, '{employeeName}', '{body.Reason}');
				}}
			";
			var result = await ExchangeManagerAPI.ExchangeManager.PerformCmdAsync(HttpContext, command);
			return result;
		}

		[HttpPost("api/exchange/drafts/transfer")]
		[Authorize(Roles = "c13")]
		public async Task<IActionResult> AddTransferDraftAsync([FromBody] TransferCreationBody body)
		{
			if (body == null) return BadRequest("Body is required");
			if (body.Amount <= 0) return BadRequest($"{nameof(body.Amount)} must be greater than 0");
			if (body.EntityId <= 0) return BadRequest($"{nameof(body.EntityId)} must be greater than 0");
			if (!body.PaymentMethod.HasValue) return BadRequest($"{nameof(body.PaymentMethod)} is required");
			if (string.IsNullOrWhiteSpace(body.FromCurrencyCode)) return BadRequest($"{nameof(body.FromCurrencyCode)} is required");
			if (string.IsNullOrWhiteSpace(body.ToCurrencyCode)) return BadRequest($"{nameof(body.ToCurrencyCode)} is required");
			if (string.IsNullOrWhiteSpace(body.FromIdentifier)) return BadRequest($"{nameof(body.FromIdentifier)} is required");
			if (string.IsNullOrWhiteSpace(body.ToIdentifier)) return BadRequest($"{nameof(body.ToIdentifier)} is required");
			if (string.IsNullOrWhiteSpace(body.RealAccount)) body.RealAccount = "";

			string path = Security.UserPath(HttpContext);
			string employeeName = Security.UserName(HttpContext);
			string batchPath = path + '/' + employeeName;
			string domain = HttpContext.Request.Host.Host;

			string accountCommand = string.IsNullOrWhiteSpace(body.FromAccountNumber) ?
				$"account = customer.FindAccountByCurrency('{body.FromCurrencyCode}');" :
				$"account = customer.FindAccount('{body.FromAccountNumber}');";

			var result = await ExchangeManagerAPI.ExchangeManager.PerformChkThenCmdAsync(HttpContext, $@"
					existsCustomer = company.ExistsCustomerByIdentifier('{body.FromIdentifier}');
					Check(existsCustomer) Error 'Customer {body.FromIdentifier} does not exist';
					if (existsCustomer) 
					{{
						customer = company.CustomerByIdentifier('{body.FromIdentifier}');
						Check(customer.IsApproved()) Error 'Customer {body.FromIdentifier} is not approved';
					}}

					domain = company.Sales.DomainFrom('{domain}');
					isTransactionAllowed = domain.AllowedTransactions.ItsEnabled({TransactionType.Transfer});
					Check(isTransactionAllowed) Error 'Transaction {TransactionType.Transfer} is not enabled';
					existsDomainInMarketplace = company.Sales.CurrentStore.ExistsDomain(domain.Id);
					Check(existsDomainInMarketplace) Error 'Domain {domain} does not belong to marketplace';

					existsBatch = marketplace.Batches.ExistAgentBatch('{batchPath}');
					Check(existsBatch) Error 'Agent batch {batchPath} does not exist.';
					if (existsBatch)
					{{
						agentBatch = marketplace.SearchAgentBatch('{batchPath}');
						Check(agentBatch.BatchTransactions.HasAvailable(Currency('{body.FromCurrencyCode}',{body.Amount}))) Error 'Agent batch {batchPath} does not have enough funds for {body.FromCurrencyCode}{body.Amount}.';
					}}

					existsAgent = marketplace.ExistsAgent('{path}');
					Check(existsAgent) Error 'Agent {path} does not exist.';
					if (existsAgent)
					{{
						agent = marketplace.SearchAgent('{path}');
						user = agent.SearchUser('{employeeName}');
						Check(user.IsInRange(Currency('{body.FromCurrencyCode}',{body.Amount}))) Error 'Agent {path} does not have enough funds for {body.FromCurrencyCode}{body.Amount}.';
					}}
				", $@"
				{{
					domain = company.Sales.DomainFrom('{domain}');
					customer = company.CustomerByIdentifier('{body.FromIdentifier}');
					{accountCommand}
					
					drivers = company.System.DriverManagers.PaymentProcessorsAndActionsByDomains.SearchForX(domain.AgentId, domain.Url);
					processor = drivers.SearchByX({TransactionType.Transfer}, '{body.FromCurrencyCode}', {body.PaymentMethod.Value}, {body.EntityId});

					Eval('transactionNumber =' + marketplace.NewTransationNumber() + ';');
					transaction = marketplace.From(transactionNumber, account, '{path}', '{employeeName}', domain);
					print transaction.Id transactionId;
					print account.Identificator account;
					processorAccount = guardian.Accounts().SearchByProcessor(processor.ProcessorKey);
					print processorAccount.Id processorAccountId;
					print customer.AccountNumber customerNumber;
					print company.Sales.CurrentStore.Id storeId;
					print domain.AgentId agentId;
					print domain.Url domainUrl;
					print domain.Id domainId;
				}}
			");
			if (!(result is OkObjectResult))
			{
				return result;
			}
			var o = (OkObjectResult)result;
			var json = o.Value.ToString();
			TempTransactionResponse tempTransactionResponse = JsonConvert.DeserializeObject<TempTransactionResponse>(json);

    

            var useless = DateTime.Now.AddDays(DaysToBecomeUseless);
            var authorizationNumberFromLockBalance = await PaymentChannels.LockBalanceAsync(
				tempTransactionResponse.CustomerNumber,
				body.Amount,
				body.FromCurrencyCode,
				body.Description,
				tempTransactionResponse.TransactionId.ToString(),
				tempTransactionResponse.Account,
				employeeName,
				tempTransactionResponse.StoreId,
				useless,
                tempTransactionResponse.AgentId,
                tempTransactionResponse.DomainUrl,
                tempTransactionResponse.DomainId,
                body.PaymentMethod.Value,
                body.EntityId
            );

			if (authorizationNumberFromLockBalance == ASITenantDriver.FAKE_TICKET_NUMBER)
			{
				await ExchangeManagerAPI.ExchangeManager.PerformCmdAsync(HttpContext, $@"
					{{
						transaction = marketplace.SearchTemporaryDefinition({tempTransactionResponse.TransactionId}, '{path}', '{employeeName}').Remove();
					}}
					");
			}

			accountCommand = string.IsNullOrWhiteSpace(body.ToAccountNumber) ?
				$"toAccount = targetCustomer.FindAccountByCurrency('{body.ToCurrencyCode}');" :
				$"toAccount = targetCustomer.FindAccount('{body.ToAccountNumber}');";

			var errorMessage = "Sorry, your transfer cannot be completed at this moment. Please try again. If the problem persists, please contact customer service.";
			result = await ExchangeManagerAPI.ExchangeManager.PerformChkThenCmdAsync(HttpContext, $@"
					Check({authorizationNumberFromLockBalance} != {ASITenantDriver.FAKE_TICKET_NUMBER}) Error '{errorMessage}';
				", $@"
				{{	
					targetCustomer = company.CustomerByIdentifier('{body.ToIdentifier}');
					{accountCommand}
					domain = company.Sales.DomainFrom('{domain}');
					
					drivers = company.System.DriverManagers.PaymentProcessorsAndActionsByDomains.SearchForX(domain.AgentId, domain.Url);
					processor = drivers.SearchByX({TransactionType.Transfer}, '{body.FromCurrencyCode}', {body.PaymentMethod.Value}, {body.EntityId});

					processorAccountId = guardian.Accounts().SearchBy(processor).Id;
					transaction = marketplace.SearchTemporaryDefinition({tempTransactionResponse.TransactionId}, '{path}', '{employeeName}').TransferTo(Now, itIsThePresent, Currency('{body.FromCurrencyCode}',{body.Amount}), {authorizationNumberFromLockBalance}, toAccount, '{employeeName}', '{body.RealAccount}', '{body.Description}', processor, processorAccountId);
					print transactionNumber authorizationId;
					print transaction.BatchTransactions.TransactionsNumber batchNumber;
					print Now now;
				}}
			");
			if (!(result is OkObjectResult))
			{
				return result;
			}
			o = (OkObjectResult)result;
			json = o.Value.ToString();
			DraftTransactionResponse idResponse = JsonConvert.DeserializeObject<DraftTransactionResponse>(json);

			return Ok(idResponse);
		}

		[HttpGet("api/exchange/transfers")]
		[Authorize(Roles = "c14")]
		public async Task<IActionResult> ListTransfersAsync(DateTime startDate, DateTime endDate, string state, string address, string transactionId, string cashierName, string domain, int initialIndex, int amountOfRows)
		{
			if (startDate == default(DateTime)) return BadRequest($"{nameof(startDate)} is required");
			if (endDate == default(DateTime)) return BadRequest($"{nameof(endDate)} is required");
			if (startDate.Hour != 0 || startDate.Minute != 0 || startDate.Second != 0) return BadRequest($"{nameof(startDate)} {startDate} is not valid");
			if (endDate.Hour != 0 || endDate.Minute != 0 || endDate.Second != 0) return BadRequest($"{nameof(endDate)} {endDate} is not valid");
			if (string.IsNullOrWhiteSpace(state)) return BadRequest($"{nameof(state)} is required");
			if (string.IsNullOrWhiteSpace(address)) return BadRequest($"{nameof(address)} is required");
			if (string.IsNullOrWhiteSpace(transactionId)) return BadRequest($"{nameof(transactionId)} is required");
			if (string.IsNullOrWhiteSpace(cashierName)) return BadRequest($"{nameof(cashierName)} is required");
			if (string.IsNullOrWhiteSpace(domain)) return BadRequest($"{nameof(domain)} is required");
			if (initialIndex < 0) return BadRequest($"{nameof(initialIndex)} {initialIndex} is not valid");
			if (amountOfRows < 0) return BadRequest($"{nameof(amountOfRows)} {amountOfRows} is not valid");

			if (initialIndex == 0 && amountOfRows == 0)
				amountOfRows = CreditNotes.MAXIMUM_AMOUNT_OF_ROWS_ALLOWED;

			string cashier = cashierName.ToLower().Trim();

			var resultProcessors = await GetProcessorsInfoWithAccountAsync();
			OkObjectResult o = (OkObjectResult)resultProcessors;
			string json = o.Value.ToString();
			var response = JsonConvert.DeserializeObject<TransfersResponse>(json);
			
			var transactions = Logic.Transfers.FilteredBy(startDate, endDate, state, address, transactionId, cashier, domain, initialIndex, amountOfRows);
			response.Transactions = transactions;
			return Ok(transactions);
		}

		[HttpPut("api/exchange/drafts/transfers/{id}/voucher")]
        [AllowAnonymous]
        public IActionResult UpdateVoucherUrlInTransfer(int id, [FromBody] VoucherBody body)
		{
			if (body == null) return NotFound("Body is required");
			if (string.IsNullOrWhiteSpace(body.VoucherUrl)) return NotFound($"{nameof(body.VoucherUrl)} is required");
			if (id <= 0) return NotFound($"{nameof(id)} {id} is not valid");

			if (Integration.UseKafka)
			{
				Integration.Kafka.Send(
					true,
					$"{KafkaMessage.VOUCHER_URL_EXCHANGE_CONSUMER_PREFIX}_{Integration.Kafka.TopicForTransacctions}",
					new VoucherUrlMessage(TransactionType.Transfer, id, body.VoucherUrl)
				);
			}
			return Ok();
		}

		[HttpPost("api/exchange/drafts/transfers/{id}/approval")]
		[Authorize(Roles = "c15")]
		public async Task<IActionResult> ApproveTransferDraftAsync(int id)
		{
			if (id <= 0) return BadRequest($"{nameof(id)} must be greater than 0");

			string path = Security.UserPath(HttpContext);
			string employeeName = Security.UserName(HttpContext);

			var agentInRangeResponse = await RetrieveTransactionAsync(id, path, employeeName);
			if (!(agentInRangeResponse is OkObjectResult)) return agentInRangeResponse;

            var agentInRange = (AgentValidationInRange)((OkObjectResult)agentInRangeResponse).Value;

            var command = $@"
				{{
					transaction = marketplace.FindDraftTransaction({id}, '{path}', '{employeeName}');
					transactionCompleted = transaction.Approve(Now, itIsThePresent, '{employeeName}');
					result = transactionCompleted.Result;
					print result.Net.Sign netsign;
					print result.Net.Value net;
					print result.Net.ToDisplayFormat() netFormatted;
					print result.Gross.Sign grosssign;
					print result.Gross.Value gross;
					print result.Gross.ToDisplayFormat() grossFormatted;
					print result.Profit.Sign profitsign;
					print result.Profit.Value profit;
					print result.Profit.ToDisplayFormat() profitFormatted;
					print result.Amount.Sign amountsign;
					print result.Amount.Value amount;
					print result.Amount.ToDisplayFormat() amountFormatted;
				}}
			";
			var result = await ExchangeManagerAPI.ExchangeManager.PerformCmdAsync(HttpContext, command);

			if (!(result is OkObjectResult))
			{
				if (result is ContentResult contentResult) return BadRequest($"Error: {contentResult.Content}");
				return BadRequest($"Error:{result}");
			}

            DespositBody despositBody = new DespositBody(
                    agentInRange.accountNumberIdentificator,
                    agentInRange.identificationNumber,
                    agentInRange.value,
                    $"{nameof(TransactionType.Deposit)}",
                    DateTime.Now,
                    string.Empty,
                    employeeName,
                    new GamesEngine.Domains.Domain(true, agentInRange.domainId, agentInRange.domain, Agents.TEST_BOOK),
                    path,
                    agentInRange.paymentMethodType.Value,
                    agentInRange.entityId,
                    agentInRange.storeId,
                    string.Empty,
                    string.Empty,
                    string.Empty,
                    string.Empty,
                    string.Empty,
                    0
            );

            int agentId = (int)Security.UserAgent(HttpContext);
            DespositResponse despositResponse = PaymentChannels.TransferX(true, agentId, agentInRange.domain, agentInRange.currencyCodeAsText, despositBody);

            var o = (OkObjectResult)result;
			var json = o.Value.ToString();
			TransactionResult transactionResult = JsonConvert.DeserializeObject<TransactionResult>(json);

			return Ok(transactionResult);
		}

		[HttpPost("api/exchange/drafts/transfers/{id}/rejection")]
		[Authorize(Roles = "c16")]
		public async Task<IActionResult> RejectTransferDraftAsync(int id, [FromBody]TransactionRejectionBody body)
		{
			if (id <= 0) return BadRequest($"{nameof(id)} must be greater than 0");
			if (body == null) return BadRequest("Body is required");
			if (string.IsNullOrWhiteSpace(body.Reason)) return BadRequest($"{nameof(body.Reason)} is required");

			string path = Security.UserPath(HttpContext);
			string employeeName = Security.UserName(HttpContext);

			var agentInRangeResult = await RetrieveTransactionAsync(id, path, employeeName);
			if (!(agentInRangeResult is OkObjectResult)) return agentInRangeResult;

            var command = $@"
				{{
					transaction = marketplace.FindDraftTransaction({id}, '{path}', '{employeeName}');
					transaction.Deny(Now, itIsThePresent, '{employeeName}', '{body.Reason}');
				}}
			";
			var result = await ExchangeManagerAPI.ExchangeManager.PerformCmdAsync(HttpContext, command);
			return result;
		}

		[HttpPost("api/exchange/drafts/creditNote")]
		[Authorize(Roles = "c17")]
		public async Task<IActionResult> AddCreditNoteDraftAsync([FromBody]CreditOrDebitNoteCreationBody body)
		{
			if (body == null) return BadRequest("Body is required");
			if (body.Amount <= 0) return BadRequest($"{nameof(body.Amount)} must be greater than 0");
			if (body.ReferenceId <= 0) return BadRequest($"{nameof(body.ReferenceId)} must be greater than 0");
			if (string.IsNullOrWhiteSpace(body.CurrencyCode)) return BadRequest($"{nameof(body.CurrencyCode)} is required");
			if (string.IsNullOrWhiteSpace(body.ToIdentifier)) return BadRequest($"{nameof(body.ToIdentifier)} is required");
			if (string.IsNullOrWhiteSpace(body.AccountNumber)) return BadRequest($"{nameof(body.AccountNumber)} is required");
			if (string.IsNullOrWhiteSpace(body.Description)) return BadRequest($"{nameof(body.Description)} is required");
			if (body.BatchNumber <= 0) return BadRequest($"{nameof(body.BatchNumber)} is required");
			if (body.EntityId <= 0) return BadRequest($"{nameof(body.EntityId)} must be greater than 0");
			if (!body.PaymentMethod.HasValue) return BadRequest($"{nameof(body.PaymentMethod)} is required");

			string path = Security.UserPath(HttpContext);
			string employeeName = Security.UserName(HttpContext);
			string domain = HttpContext.Request.Host.Host;

			var noAttachments = body.Attachments.Count == 0;
			var attachments = noAttachments ? string.Empty : string.Join(",", body.Attachments.Select(url => $"'{url}'").ToArray());
			var commandForNote = noAttachments ? $".CreditNote(Now, itIsThePresent, Currency('{body.CurrencyCode}',{body.Amount}), '{employeeName}', '{body.Description}', {body.ReferenceId}, {body.BatchNumber}, processor, processorAccountId);" :
				$".CreditNote(Now, itIsThePresent, Currency('{body.CurrencyCode}',{body.Amount}), '{employeeName}', '{body.Description}', {body.ReferenceId}, {body.BatchNumber}, processor, processorAccountId, {{{attachments}}});";

			var result = await ExchangeManagerAPI.ExchangeManager.PerformChkThenCmdAsync(HttpContext, $@"
				existsCustomer = company.ExistsCustomerByIdentifier('{body.ToIdentifier}');
				Check(existsCustomer) Error 'Customer {body.ToIdentifier} does not exist';
				if (existsCustomer) 
				{{
					customer = company.CustomerByIdentifier('{body.ToIdentifier}');
					Check(customer.IsApproved()) Error 'Customer {body.ToIdentifier} is not approved';
				}}
				domain = company.Sales.DomainFrom('{domain}');
				isTransactionAllowed = domain.AllowedTransactions.ItsEnabled({TransactionType.CreditNote});
				Check(isTransactionAllowed) Error 'Transaction {TransactionType.CreditNote} is not enabled';
				existsDomainInMarketplace = company.Sales.CurrentStore.ExistsDomain(domain.Id);
				Check(existsDomainInMarketplace) Error 'Domain {domain} does not belong to marketplace';
				existsTransaction = marketplace.ExistsDraftTransaction({body.ReferenceId}, '{path}', '{employeeName}');
				Check(!existsTransaction) Error 'Sorry, the transaction reference number {body.ReferenceId} is pending to approve. Please check if it is the right number. Only approved and rejected transactions can be used as reference.';
			", $@"
				{{
					domain = company.Sales.DomainFrom('{domain}');
					customer = company.CustomerByIdentifier('{body.ToIdentifier}');
					account = customer.FindAccount('{body.AccountNumber}');
					Eval('transactionNumber =' + marketplace.NewTransationNumber() + ';');
					
					drivers = company.System.DriverManagers.PaymentProcessorsAndActionsByDomains.SearchForX(domain.AgentId, domain.Url);
					processor = drivers.SearchByX({TransactionType.CreditNote}, '{body.CurrencyCode}', {body.PaymentMethod.Value}, {body.EntityId});
					
					processorAccountId = guardian.Accounts().SearchBy(processor).Id;
					transaction = marketplace.From(transactionNumber, account, '{path}', '{employeeName}', domain){commandForNote}
					print transactionNumber authorizationId;
					print transaction.BatchTransactions.TransactionsNumber batchNumber;
					print Now now;
				}}
			");

			if (!(result is OkObjectResult))
			{
				return result;
			}

			var o = (OkObjectResult)result;
			var json = o.Value.ToString();
			DraftTransactionResponse idResponse = JsonConvert.DeserializeObject<DraftTransactionResponse>(json);

			return Ok(idResponse);
		}

		[HttpPost("api/exchange/drafts/debitNote")]
		[Authorize(Roles = "c18")]
		public async Task<IActionResult> AddDebitNoteDraftAsync([FromBody]CreditOrDebitNoteCreationBody body)
		{
			if (body == null) return BadRequest("Body is required");
			if (body.Amount <= 0) return BadRequest($"{nameof(body.Amount)} must be greater than 0");
			if (body.ReferenceId <= 0) return BadRequest($"{nameof(body.ReferenceId)} must be greater than 0");
			if (string.IsNullOrWhiteSpace(body.CurrencyCode)) return BadRequest($"{nameof(body.CurrencyCode)} is required");
			if (string.IsNullOrWhiteSpace(body.ToIdentifier)) return BadRequest($"{nameof(body.ToIdentifier)} is required");
			if (string.IsNullOrWhiteSpace(body.AccountNumber)) return BadRequest($"{nameof(body.AccountNumber)} is required");
			if (string.IsNullOrWhiteSpace(body.Description)) return BadRequest($"{nameof(body.Description)} is required");
			if (body.BatchNumber <= 0) return BadRequest($"{nameof(body.BatchNumber)} is required");
			if (body.EntityId <= 0) return BadRequest($"{nameof(body.EntityId)} must be greater than 0");
			if (!body.PaymentMethod.HasValue) return BadRequest($"{nameof(body.PaymentMethod)} is required");

			string path = Security.UserPath(HttpContext);
			string employeeName = Security.UserName(HttpContext);

			string domain = HttpContext.Request.Host.Host;

			var noAttachments = body.Attachments.Count == 0;
			var attachments = noAttachments ? string.Empty : string.Join(",", body.Attachments.Select(url => $"'{url}'").ToArray());
			var commandForNote = noAttachments ? $".DebitNote(Now, itIsThePresent, Currency('{body.CurrencyCode}',{body.Amount}), '{employeeName}', '{body.Description}', {body.ReferenceId}, {body.BatchNumber}, processor, processorAccountId);" :
				$".DebitNote(Now, itIsThePresent, Currency('{body.CurrencyCode}',{body.Amount}), '{employeeName}', '{body.Description}', {body.ReferenceId}, {body.BatchNumber}, processor, processorAccountId, {{{attachments}}});";

			var result = await ExchangeManagerAPI.ExchangeManager.PerformChkThenCmdAsync(HttpContext, $@"
				existsCustomer = company.ExistsCustomerByIdentifier('{body.ToIdentifier}');
				Check(existsCustomer) Error 'Customer {body.ToIdentifier} does not exist';
				if (existsCustomer) 
				{{
					customer = company.CustomerByIdentifier('{body.ToIdentifier}');
					Check(customer.IsApproved()) Error 'Customer {body.ToIdentifier} is not approved';
				}}
				domain = company.Sales.DomainFrom('{domain}');
				isTransactionAllowed = domain.AllowedTransactions.ItsEnabled({TransactionType.DebitNote});
				Check(isTransactionAllowed) Error 'Transaction {TransactionType.DebitNote} is not enabled';
				existsDomainInMarketplace = company.Sales.CurrentStore.ExistsDomain(domain.Id);
				Check(existsDomainInMarketplace) Error 'Domain {domain} does not belong to marketplace';
				existsTransaction = marketplace.ExistsDraftTransaction({body.ReferenceId}, '{path}', '{employeeName}');
				Check(!existsTransaction) Error 'Sorry, the transaction reference number {body.ReferenceId} is pending to approve. Please check if it is the right number. Only approved and rejected transactions can be used as reference.';
			", $@"
			{{
				domain = company.Sales.DomainFrom('{domain}');
				customer = company.CustomerByIdentifier('{body.ToIdentifier}');
				account = customer.FindAccount('{body.AccountNumber}');
				Eval('transactionNumber =' + marketplace.NewTransationNumber() + ';');
				
				drivers = company.System.DriverManagers.PaymentProcessorsAndActionsByDomains.SearchForX(domain.AgentId, domain.Url);
				processor = drivers.SearchByX({TransactionType.DebitNote}, '{body.CurrencyCode}', {body.PaymentMethod.Value}, {body.EntityId});

				processorAccountId = guardian.Accounts().SearchBy(processor).Id;
				transaction = marketplace.From(transactionNumber, account, '{path}', '{employeeName}', domain){commandForNote}
				print transactionNumber authorizationId;
				print transaction.BatchTransactions.TransactionsNumber batchNumber;
				print Now now;
			}}");

			if (!(result is OkObjectResult))
			{
				return result;
			}

			var o = (OkObjectResult)result;
			var json = o.Value.ToString();
			DraftTransactionResponse idResponse = JsonConvert.DeserializeObject<DraftTransactionResponse>(json);

			return Ok(idResponse);
		}

		[HttpGet("api/exchange/lastTransaction")]
		[Authorize(Roles = "c19")]
		public IActionResult LastTransactionFor(string identificationNumber)
		{
			if (string.IsNullOrWhiteSpace(identificationNumber)) return BadRequest($"{nameof(identificationNumber)} is required");

			var result = Logic.Transactions.LastTransactionFor(identificationNumber);
			if (result == null) return BadRequest($"No transactions for {nameof(identificationNumber)} '{identificationNumber}'");
			return Ok(result);
		}

		[HttpGet("api/exchange/lastTransactions")]
        [Authorize(Roles = "devops")]//VERIFY_ROLES
        public IActionResult LastTransactionsFor(string identificationNumber, int initialIndex, int amountOfRows)
		{
			if (string.IsNullOrWhiteSpace(identificationNumber)) return BadRequest($"{nameof(identificationNumber)} is required");
			if (initialIndex < 0) return BadRequest($"{nameof(initialIndex)} {initialIndex} is not valid");
			if (amountOfRows <= 0) return BadRequest($"{nameof(amountOfRows)} {amountOfRows} is not valid");

			var result = Logic.Transactions.LastTransactionsFor(identificationNumber, initialIndex, amountOfRows);
			return Ok(result);
		}

		[HttpGet("api/exchange/transactionsPerDay")]
		[Authorize(Roles = "c56,player")]
		public IActionResult TransactionsPerDay(DateTime date, string identificationNumber, string currencyCode, string account, string state, int initialIndex, int amountOfRows)
		{
			if (date == default(DateTime)) return BadRequest($"{nameof(date)} is required");
			if (string.IsNullOrWhiteSpace(identificationNumber)) return BadRequest($"{nameof(identificationNumber)} is required");
			if (initialIndex < 0) return BadRequest($"{nameof(initialIndex)} {initialIndex} is not valid");
			if (amountOfRows <= 0) return BadRequest($"{nameof(amountOfRows)} {amountOfRows} is not valid");

			if (string.IsNullOrWhiteSpace(currencyCode))
            {
				currencyCode = ALL_SELECTED;
			}
			if (string.IsNullOrWhiteSpace(account))
			{
				account = ALL_SELECTED;
			}
			if (string.IsNullOrWhiteSpace(state))
			{
				state = ALL_SELECTED;
			}

			var result = Logic.Transactions.TransactionsPerDay(date, identificationNumber, currencyCode, account, state, initialIndex, amountOfRows);
			return Ok(result);
		}

		[HttpGet("api/exchange/creditNotes")]
		[Authorize(Roles = "c20")]
		public async Task<IActionResult> ListCreditNotesAsync(DateTime startDate, DateTime endDate, string state, string address, string transactionId, string currencyCode, string cashierName, string domain, 
			int initialIndex, int amountOfRows)
		{
			if (startDate == default(DateTime)) return BadRequest($"{nameof(startDate)} is required");
			if (endDate == default(DateTime)) return BadRequest($"{nameof(endDate)} is required");
			if (string.IsNullOrWhiteSpace(state)) return BadRequest($"{nameof(state)} is required");
			if (string.IsNullOrWhiteSpace(address)) return BadRequest($"{nameof(address)} is required");
			if (string.IsNullOrWhiteSpace(transactionId)) return BadRequest($"{nameof(transactionId)} is required");
			if (string.IsNullOrWhiteSpace(currencyCode)) return BadRequest($"{nameof(currencyCode)} is required");
			if (string.IsNullOrWhiteSpace(cashierName)) return BadRequest($"{nameof(cashierName)} is required");
			if (string.IsNullOrWhiteSpace(domain)) return BadRequest($"{nameof(domain)} is required");
			if (initialIndex < 0) return BadRequest($"{nameof(initialIndex)} {initialIndex} is not valid");
			if (amountOfRows <= 0) return BadRequest($"{nameof(amountOfRows)} {amountOfRows} is not valid");

			string cashier = cashierName.ToLower().Trim();

			var resultProcessors = await GetProcessorsInfoWithAccountAsync();
			OkObjectResult o = (OkObjectResult)resultProcessors;
			string json = o.Value.ToString();
			var response = JsonConvert.DeserializeObject<CreditNotesResponse>(json);

			var transactions = Logic.CreditNotes.FilteredBy(startDate, endDate, state, address, transactionId, currencyCode, cashier, domain, initialIndex, amountOfRows);
			response.Transactions = transactions;
			return Ok(transactions);
		}

		[HttpGet("api/exchange/debitNotes")]
		[Authorize(Roles = "c21")]
		public async Task<IActionResult> ListDebitNotesAsync(DateTime startDate, DateTime endDate, string state, string address, string transactionId, string currencyCode, string cashierName, string domain, 
			int initialIndex, int amountOfRows)
		{
			if (startDate == default(DateTime)) return BadRequest($"{nameof(startDate)} is required");
			if (endDate == default(DateTime)) return BadRequest($"{nameof(endDate)} is required");
			if (string.IsNullOrWhiteSpace(state)) return BadRequest($"{nameof(state)} is required");
			if (string.IsNullOrWhiteSpace(address)) return BadRequest($"{nameof(address)} is required");
			if (string.IsNullOrWhiteSpace(transactionId)) return BadRequest($"{nameof(transactionId)} is required");
			if (string.IsNullOrWhiteSpace(currencyCode)) return BadRequest($"{nameof(currencyCode)} is required");
			if (string.IsNullOrWhiteSpace(cashierName)) return BadRequest($"{nameof(cashierName)} is required");
			if (string.IsNullOrWhiteSpace(domain)) return BadRequest($"{nameof(domain)} is required");
			if (initialIndex < 0) return BadRequest($"{nameof(initialIndex)} {initialIndex} is not valid");
			if (amountOfRows <= 0) return BadRequest($"{nameof(amountOfRows)} {amountOfRows} is not valid");

			string cashier = cashierName.ToLower().Trim();

			var resultProcessors = await GetProcessorsInfoWithAccountAsync();
			OkObjectResult o = (OkObjectResult)resultProcessors;
			string json = o.Value.ToString();
			var response = JsonConvert.DeserializeObject<DebitNotesResponse>(json);

			var transactions = Logic.DebitNotes.FilteredBy(startDate, endDate, state, address, transactionId, currencyCode, cashier, domain, initialIndex, amountOfRows);
			response.Transactions = transactions;
			return Ok(transactions);
		}

		[HttpPut("api/exchange/drafts/creditNotes/{id}/voucher")]
        [AllowAnonymous]
        public IActionResult UpdateVoucherUrlInCreditNote(int id, [FromBody] VoucherBody body)
		{
			if (body == null) return NotFound("Body is required");
			if (string.IsNullOrWhiteSpace(body.VoucherUrl)) return NotFound($"{nameof(body.VoucherUrl)} is required");
			if (id <= 0) return NotFound($"{nameof(id)} {id} is not valid");

			if (Integration.UseKafka)
			{
				Integration.Kafka.Send(
					true,
					$"{KafkaMessage.VOUCHER_URL_EXCHANGE_CONSUMER_PREFIX}_{Integration.Kafka.TopicForTransacctions}",
					new VoucherUrlMessage(TransactionType.CreditNote, id, body.VoucherUrl)
				);
			}
			return Ok();
		}

		[HttpPut("api/exchange/drafts/debitNotes/{id}/voucher")]
        [AllowAnonymous]
        public IActionResult UpdateVoucherUrlInDebitNote(int id, [FromBody] VoucherBody body)
		{
			if (body == null) return NotFound("Body is required");
			if (string.IsNullOrWhiteSpace(body.VoucherUrl)) return NotFound($"{nameof(body.VoucherUrl)} is required");
			if (id <= 0) return NotFound($"{nameof(id)} {id} is not valid");

			if (Integration.UseKafka)
			{
				Integration.Kafka.Send(
					true,
					$"{KafkaMessage.VOUCHER_URL_EXCHANGE_CONSUMER_PREFIX}_{Integration.Kafka.TopicForTransacctions}",
					new VoucherUrlMessage(TransactionType.DebitNote, id, body.VoucherUrl)
				);
			}
			return Ok();
		}

		[HttpPost("api/exchange/drafts/creditNotes/{id}/approval")]
		[Authorize(Roles = "c22")]
		public async Task<IActionResult> ApproveCreditNoteDraftAsync(int id)
		{
			if (id <= 0) return BadRequest($"{nameof(id)} must be greater than 0");

			string path = Security.UserPath(HttpContext);
			string employeeName = Security.UserName(HttpContext);

			var validAgentResponse = await RetrieveTransactionAsync(id, path, employeeName);
			if (!(validAgentResponse is OkObjectResult)) return validAgentResponse;

            var validAgent = (AgentValidationInRange)((OkObjectResult)validAgentResponse).Value;

			var result = await ExchangeManagerAPI.ExchangeManager.PerformCmdAsync(HttpContext, $@"
				{{
					transaction = marketplace.FindDraftTransaction({id}, '{path}', '{employeeName}');
					transactionCompleted = transaction.Approve(Now, itIsThePresent, '{employeeName}');
					result = transactionCompleted.Result;
					print result.Net.Sign netsign;
					print result.Net.Value net;
					print result.Gross.Sign grosssign;
					print result.Gross.Value gross;
					print result.Profit.Sign profitsign;
					print result.Profit.Value profit;
					print result.Amount.Sign amountsign;
					print result.Amount.Value amount;
				}}
			");

			if (!(result is OkObjectResult))
			{
				if (result is ContentResult contentResult) return BadRequest($"Error: {contentResult.Content}");
				return BadRequest($"Error:{result}");
			}

			var o = (OkObjectResult)result;
			var json = o.Value.ToString();
			TransactionResult transactionResult = JsonConvert.DeserializeObject<TransactionResult>(json);
            
            DespositBody despositBody = new DespositBody(
                    validAgent.accountNumberIdentificator,
                    validAgent.identificationNumber,
                    validAgent.value,
                    $"{nameof(TransactionType.CreditNote)}",
                    DateTime.Now,
                    string.Empty,
                    employeeName,
                    new GamesEngine.Domains.Domain(false, validAgent.domainId, validAgent.domain, Agents.TEST_BOOK),
                    path,
                    validAgent.paymentMethodType.Value,
                    validAgent.entityId,
                    validAgent.storeId,
                    string.Empty,
                    string.Empty,
                    string.Empty,
                    string.Empty,
                    string.Empty,
                    0
                    );

            int agentId = (int)Security.UserAgent(HttpContext);
            DespositResponse creditNotResponse = PaymentChannels.CreditNoteX(true, validAgent.domainAgentId, validAgent.domain, validAgent.currencyCodeAsText , despositBody);
            
			if(creditNotResponse.Status != TransactionStatus.APPROVED)
			{
				return BadRequest("The transaction was not approve");
			}

			return Ok(transactionResult);			
		}

		[HttpPost("api/exchange/drafts/debitNotes/{id}/approval")]
		[Authorize(Roles = "c23")]
		public async Task<IActionResult> ApproveDebitNoteDraftAsync(int id)
		{
			if (id <= 0) return BadRequest($"{nameof(id)} must be greater than 0");

			string path = Security.UserPath(HttpContext);
			string employeeName = Security.UserName(HttpContext);
			string batchPath = path + '/' + employeeName;

			var retrieveTransactionResponse = await RetrieveTransactionAsync(id, path, employeeName);
			if (!(retrieveTransactionResponse is OkObjectResult)) return retrieveTransactionResponse;

            var retrieveTransaction = (AgentValidationInRange)((OkObjectResult)retrieveTransactionResponse).Value;

			if (!retrieveTransaction.isItInRange) return BadRequest($"Sorry, you have exceeded your allowed limits, this transaction cannot be processed.");

			var result = await ExchangeManagerAPI.ExchangeManager.PerformChkThenCmdAsync(HttpContext, $@"
					existsBatch = marketplace.Batches.ExistAgentBatch('{batchPath}');
					Check(existsBatch) Error 'Agent batch {batchPath} does not exist.';
					if (existsBatch)
					{{
						agentBatch = marketplace.SearchAgentBatch('{batchPath}');
						Check(agentBatch.BatchTransactions.HasAvailable(Currency('{retrieveTransaction.currencyCodeAsText}',{retrieveTransaction.value}))) Error 'Agent batch {batchPath} does not have enough funds for {retrieveTransaction.currencyCodeAsText}{retrieveTransaction.value}.';
					}}
				", $@"
				{{
					transaction = marketplace.FindDraftTransaction({id}, '{path}', '{employeeName}');
					transactionCompleted = transaction.Approve(Now, itIsThePresent, '{employeeName}');
					result = transactionCompleted.Result;
					print result.Net.Sign netsign;
					print result.Net.Value net;
					print result.Gross.Sign grosssign;
					print result.Gross.Value gross;
					print result.Profit.Sign profitsign;
					print result.Profit.Value profit;
					print result.Amount.Sign amountsign;
					print result.Amount.Value amount;
				}}
			");

			if (!(result is OkObjectResult))
			{
				if (result is ContentResult contentResult) return BadRequest($"Error: {contentResult.Content}");
				return result;
			}

			var o = (OkObjectResult)result;
			var json = o.Value.ToString();
			TransactionResult transactionResult = JsonConvert.DeserializeObject<TransactionResult>(json);

            var withdrawalBody = new WithdrawBody(
                retrieveTransaction.identificationNumber,
                retrieveTransaction.value,
                $"{nameof(TransactionType.DebitNote)}",
                DateTime.Now,
                string.Empty,
                retrieveTransaction.domainId,
                retrieveTransaction.domain,
                retrieveTransaction.paymentMethodType.Value,
                retrieveTransaction.entityId,
                retrieveTransaction.storeId,
                string.Empty,
                string.Empty,
                string.Empty,
                string.Empty,
                new GamesEngine.Domains.Domain(false, retrieveTransaction.domainId, retrieveTransaction.domain, Agents.TEST_BOOK),
                employeeName,
                reference: id.ToString()
            );

            int agentId = (int)Security.UserAgent(HttpContext);
            var debitNoteResponse = PaymentChannels.DebitNoteX(true, agentId, retrieveTransaction.domain, retrieveTransaction.currencyCodeAsText, withdrawalBody);


            if (debitNoteResponse.Status == TransactionStatus.APPROVED)
            {
                return Ok(transactionResult);
            }
            else if (debitNoteResponse.Status == TransactionStatus.DENIED)
            {
                return BadRequest("");
            }

            return Ok(transactionResult);
		}

		[HttpPost("api/exchange/drafts/creditNotes/{id}/rejection")]
		[Authorize(Roles = "c24")]
		public async Task<IActionResult> RejectCreditNoteDraftAsync(int id, [FromBody]TransactionRejectionBody body)
		{
			if (id <= 0) return BadRequest($"{nameof(id)} must be greater than 0");
			if (body == null) return BadRequest("Body is required");
			if (string.IsNullOrWhiteSpace(body.Reason)) return BadRequest($"{nameof(body.Reason)} is required");

			string path = Security.UserPath(HttpContext);
			string employeeName = Security.UserName(HttpContext);

			var agentInRangeResult = await RetrieveTransactionAsync(id, path, employeeName);
			if (!(agentInRangeResult is OkObjectResult)) return agentInRangeResult;

            var command = $@"
				{{
					transaction = marketplace.FindDraftTransaction({id}, '{path}', '{employeeName}');
					transaction.Deny(Now, itIsThePresent, '{employeeName}', '{body.Reason}');
				}}
			";
			var result = await ExchangeManagerAPI.ExchangeManager.PerformCmdAsync(HttpContext, command);
			return result;
		}

		[HttpPost("api/exchange/drafts/debitNotes/{id}/rejection")]
		[Authorize(Roles = "c25")]
		public async Task<IActionResult> RejectDebitNoteDraftAsync(int id, [FromBody]TransactionRejectionBody body)
		{
			if (id <= 0) return BadRequest($"{nameof(id)} must be greater than 0");
			if (body == null) return BadRequest("Body is required");
			if (string.IsNullOrWhiteSpace(body.Reason)) return BadRequest($"{nameof(body.Reason)} is required");

			string path = Security.UserPath(HttpContext);
			string employeeName = Security.UserName(HttpContext);

			var agentInRangeResult = await RetrieveTransactionAsync(id, path, employeeName);
			if (!(agentInRangeResult is OkObjectResult)) return agentInRangeResult;

            var command = $@"
				{{
					transaction = marketplace.FindDraftTransaction({id}, '{path}', '{employeeName}');
					transaction.Deny(Now, itIsThePresent, '{employeeName}', '{body.Reason}');
				}}
			";
			var result = await ExchangeManagerAPI.ExchangeManager.PerformCmdAsync(HttpContext, command);
			return result;
		}

		string commonAgentInfo = $@"
			print mainAgent.Name agentName;
			print mainAgent.FullName agentPath;
			if (mainAgent.Name != marketplace.Name)
			{{
				print mainAgent.ManagersCount managersCount;
				print mainAgent.CashiersCount cashiersCount;
				if (mainAgent.HasCashiers)
				{{
					for (cashiers : mainAgent.Cashiers)
					{{
						cashier = cashiers;
						print cashier.Name name;
					}}
				}}
				if (mainAgent.HasManagers)
				{{
					for (managers : mainAgent.Managers)
					{{
						manager = managers;
						print manager.Name name;
					}}
				}}
			}}
			if (mainAgent.IsInternalAgent)
			{{
				for(agent : mainAgent.AllAgents())
				{{
					print agent.Name agentName;
					print agent.FullName agentPath;
					print agent.ManagersCount managersCount;
					print agent.CashiersCount cashiersCount;
					if (agent.HasCashiers)
					{{
						for (cashiers : agent.Cashiers)
						{{
							cashier = cashiers;
							print cashier.Name name;
						}}
					}}
					if (agent.HasManagers)
					{{
						for (managers : agent.Managers)
						{{
							manager = managers;
							print manager.Name name;
						}}
					}}
				}}
			}}
		";

		[HttpGet("api/exchange/agents")]
		[Authorize(Roles = "c52")]
		public async Task<IActionResult> ListOfAgentsAsync(string fullName)
		{
			IActionResult result;
			if (string.IsNullOrWhiteSpace(fullName))
			{
				string path = Security.UserPath(HttpContext);
				result = await ExchangeManagerAPI.ExchangeManager.PerformQryAsync(HttpContext, $@"
					{{
						mainAgent = marketplace.SearchAgent('{path}');
						{commonAgentInfo}
					}}
				");
			}
			else
			{
				result = await ExchangeManagerAPI.ExchangeManager.PerformQryAsync(HttpContext, $@"
					{{
						mainAgent = marketplace.SearchAgent('{fullName}');
						{commonAgentInfo}
					}}
				");
			}
			return result;
		}

		[HttpGet("api/marketplace/agents")]
		[Authorize(Roles = "c52")]
		public async Task<IActionResult> ListAllAgentsInMarketplaceAsync()
		{
			string path = Security.UserPath(HttpContext);
			var result = await ExchangeManagerAPI.ExchangeManager.PerformQryAsync(HttpContext, $@"
				{{
					mainAgent = marketplace.SearchAgent('{path}');
					{commonAgentInfo}
				}}
			");
			return result;
		}

		[HttpGet("api/exchange/customer/{customerId}/accounts")]
		[Authorize(Roles = "c53,e2,b4,c38,player")]
		public async Task<IActionResult> CustomerAccountsAsync(string customerId, string currencyCode)
		{
			if (string.IsNullOrWhiteSpace(customerId)) return BadRequest($"{nameof(customerId)} is required");

			string commandToListAccounts;
			if (currencyCode == null)
            {
				commandToListAccounts = "customer.ListAccounts()";
			}
			else
            {
				commandToListAccounts = $"customer.FindAccountsByCurrency('{currencyCode}')";
			}

			string command = $@"
				{{
					exists = company.ExistsCustomer('{customerId}');
					print exists existsCustomer;
					if(exists)
					{{
						customer = company.CustomerByAccountNumber('{customerId}');
						for( accounts : {commandToListAccounts})
						{{
							account = accounts;
							print account.CurrencyCodeAsText currencyCode;
							print account.Identificator number;
							print account.Alias alias;
							print customer.Identifier customer;
							print customer.IsOneOfMyDefaultAccount(account) isDefault;
						}}
					}}
				}}
			";
			IActionResult result = await ExchangeManagerAPI.ExchangeManager.PerformQryAsync(HttpContext, command);

			if (!(result is OkObjectResult))
			{
				if (result is ContentResult contentResult) return BadRequest($"Error: {contentResult.Content}");
				return BadRequest($"Error:{result}");
			}

			return result;
		}

		[HttpGet("api/exchange/customer/{customerId}/defaultAccounts")]
		[Authorize(Roles = "c53,e2")]
		public async Task<IActionResult> DefaultCustomerAccountsAsync(string customerId)
		{
			if (string.IsNullOrWhiteSpace(customerId)) return BadRequest($"{nameof(customerId)} is required");

			IActionResult result = await ExchangeManagerAPI.ExchangeManager.PerformQryAsync(HttpContext, $@"
				{{
					exists = company.ExistsCustomerByIdentifier('{customerId}');
					print exists existsCustomer;
					if(exists)
					{{
						customer = company.CustomerByIdentifier('{customerId}');
						for( accounts : customer.DefaultAccounts())
						{{
							print accounts.CurrencyCodeAsText currencyCode;
							print accounts.Identificator number;
							print accounts.Alias alias;
							print customer.Identifier customer;
						}}
					}}
				}}
			");

			if (!(result is OkObjectResult))
			{
				if (result is ContentResult contentResult) return BadRequest($"Error: {contentResult.Content}");
				return BadRequest($"Error:{result}");
			}

			return result;
		}

		[HttpGet("api/exchange/customer/{identificationNumber}/accounts/favorites")]
        [Authorize(Roles = "devops")]//VERIFY_ROLES
        public async Task<IActionResult> CustomerFavoriteAccountsAsync(string identificationNumber, int initialIndex, int amountOfRows)
		{
			if (string.IsNullOrWhiteSpace(identificationNumber)) return BadRequest($"{nameof(identificationNumber)} is required");
			if (initialIndex < 0) return BadRequest($"{nameof(initialIndex)} {initialIndex} is not valid");
			if (amountOfRows <= 0) return BadRequest($"{nameof(amountOfRows)} {amountOfRows} is not valid");

			IActionResult result = await ExchangeManagerAPI.ExchangeManager.PerformQryAsync(HttpContext, $@"
				{{
					exists = company.ExistsCustomerByIdentifier('{identificationNumber}');
					print exists existsCustomer;
					if(exists)
					{{
						customer = company.CustomerByIdentifier('{identificationNumber}');
						for(favorites : customer.GetFavoriteAccounts({initialIndex}, {amountOfRows}))
						{{
							favorite = favorites;
							print favorite.CurrencyCodeAsText currencyCode;
							print favorite.AccountNumber number;
							print favorite.CustomerIdentifier customer;
							print favorite.Alias alias;
							print favorite.Type() type;
						}}
					}}
				}}
			");

			if (!(result is OkObjectResult))
			{
				if (result is ContentResult contentResult) return BadRequest($"Error: {contentResult.Content}");
				return BadRequest($"Error:{result}");
			}

			return result;
		}

		[HttpPost("api/exchange/customer/{identificationNumber}/accounts/favorite/internal")]
        [Authorize(Roles = "devops")]//VERIFY_ROLES
        public async Task<IActionResult> AddCustomerFavoriteInternalAccountAsync(string identificationNumber, [FromBody]FavoriteInternalAccountBody body)
		{
			if (body == null) return BadRequest($"Body is required");
			if (string.IsNullOrWhiteSpace(body.AccountNumber)) return BadRequest($"{nameof(body.AccountNumber)} is required");
			if (string.IsNullOrWhiteSpace(body.Alias)) return BadRequest($"{nameof(body.Alias)} is required");
			if (string.IsNullOrWhiteSpace(identificationNumber)) return BadRequest($"{nameof(identificationNumber)} is required");

			var result = await ExchangeManagerAPI.ExchangeManager.PerformQryAsync(HttpContext, $@"
                {{
                    existsCustomer = company.ExistsCustomerByIdentifier('{identificationNumber}');
                    print existsCustomer existsCustomer;
					existsAccountInMarketplace = marketplace.AccountAlreadyExists('{body.AccountNumber}');
					print existsAccountInMarketplace existsAccountInMarketplace;
                }}
            ");

			if (!(result is OkObjectResult))
			{
				if (result is ContentResult contentResult) return BadRequest($"Error: {contentResult.Content}");
				return BadRequest($"Error:{result}");
			}

			OkObjectResult o = (OkObjectResult)result;
			string json = o.Value.ToString();
			var validation = JsonConvert.DeserializeObject<CustomerExistenceAndAccountInMarketplace>(json);

			if (validation.ExistsCustomer && validation.ExistsAccountInMarketplace)
			{
				string command = $@"
					{{
						customer = company.CustomerByIdentifier('{identificationNumber}');
						account = marketplace.AccountByNumber('{body.AccountNumber}');
						customer.AddInternalAccountAsFavorite(account,'{body.Alias}');
					}}
				";

				result = await ExchangeManagerAPI.ExchangeManager.PerformCmdAsync(HttpContext, command);
				if (!(result is OkObjectResult))
				{
					if (result is ContentResult contentResult) return BadRequest($"Error: {contentResult.Content}");
					return BadRequest($"Error:{result}");
				}
				return result;
			}
			else
            {
				string messageError;
				if (!validation.ExistsCustomer)
				{
					messageError = $"Sorry, customer '{identificationNumber}' does not exist, account cannot be added as favorite.";
				}
				else
				{
					messageError = $"Sorry, marketplace does not have the account '{body.AccountNumber}' registered.";
				}
				return PrefabBadRequest(messageError);
			}
		}

		[HttpPost("api/exchange/customer/{identificationNumber}/accounts/favorite/external")]
        [Authorize(Roles = "devops")]//VERIFY_ROLES
        public async Task<IActionResult> AddCustomerFavoriteExternalAccountAsync(string identificationNumber, [FromBody]FavoriteExternalAccountBody body)
		{
			if (body == null) return BadRequest($"Body is required");
			if (string.IsNullOrWhiteSpace(body.AccountNumber)) return BadRequest($"{nameof(body.AccountNumber)} is required");
			if (string.IsNullOrWhiteSpace(body.Identifier)) return BadRequest($"{nameof(body.Identifier)} is required");
			if (string.IsNullOrWhiteSpace(body.Alias)) return BadRequest($"{nameof(body.Alias)} is required");
			if (string.IsNullOrWhiteSpace(body.CurrencyCode)) return BadRequest($"{nameof(body.CurrencyCode)} is required");
			if (string.IsNullOrWhiteSpace(identificationNumber)) return BadRequest($"{nameof(identificationNumber)} is required");

			var result = await ExchangeManagerAPI.ExchangeManager.PerformQryAsync(HttpContext, $@"
                {{
                    existsCustomer = company.ExistsCustomerByIdentifier('{identificationNumber}');
                    print existsCustomer existsCustomer;
                }}
            ");

			if (!(result is OkObjectResult))
			{
				if (result is ContentResult contentResult) return BadRequest($"Error: {contentResult.Content}");
				return BadRequest($"Error:{result}");
			}

			OkObjectResult o = (OkObjectResult)result;
			string json = o.Value.ToString();
			var validation = JsonConvert.DeserializeObject<CustomerExistence>(json);

			if (validation.existsCustomer)
			{
				string command = $@"
					{{
						customer = company.CustomerByIdentifier('{identificationNumber}');
						customer.AddExternalAccountAsFavorite('{body.CurrencyCode}','{body.AccountNumber}','{body.Identifier}','{body.Alias}');
					}}
				";

				result = await ExchangeManagerAPI.ExchangeManager.PerformCmdAsync(HttpContext, command);
				if (!(result is OkObjectResult))
				{
					if (result is ContentResult contentResult) return BadRequest($"Error: {contentResult.Content}");
					return BadRequest($"Error:{result}");
				}
				return result;
			}
			else
			{
				return PrefabBadRequest($"Sorry, customer '{identificationNumber}' does not exist, account cannot be added as favorite.");
			}
		}

		[HttpDelete("api/exchange/customer/{identificationNumber}/accounts/favorite/internal/{accountNumber}")]
        [Authorize(Roles = "devops")]//VERIFY_ROLES
        public async Task<IActionResult> RemoveCustomerFavoriteInternalAccountAsync(string identificationNumber, string accountNumber)
		{
			if (string.IsNullOrWhiteSpace(identificationNumber)) return BadRequest($"{nameof(identificationNumber)} is required");
			if (string.IsNullOrWhiteSpace(accountNumber)) return BadRequest($"{nameof(accountNumber)} is required");

			var result = await ExchangeManagerAPI.ExchangeManager.PerformQryAsync(HttpContext, $@"
                {{
                    existsCustomer = company.ExistsCustomerByIdentifier('{identificationNumber}');
                    print existsCustomer existsCustomer;
					if (existsCustomer) 
					{{
						customer = company.CustomerByIdentifier('{identificationNumber}');
						print customer.HasFavoriteInternalAccount('{accountNumber}') hasAccount;
					}}
					else 
					{{
						print false hasAccount;
					}}
                }}
            ");

			if (!(result is OkObjectResult))
			{
				if (result is ContentResult contentResult) return BadRequest($"Error: {contentResult.Content}");
				return BadRequest($"Error:{result}");
			}

			OkObjectResult o = (OkObjectResult)result;
			string json = o.Value.ToString();
			var validation = JsonConvert.DeserializeObject<CustomerAndAccountExistence>(json);

			if (validation.ExistsCustomer && validation.HasAccount)
			{
				string command = $@"
					{{
						customer = company.CustomerByIdentifier('{identificationNumber}');
						customer.RemoveInternalAccountAsFavorite('{accountNumber}');
					}}
				";

				result = await ExchangeManagerAPI.ExchangeManager.PerformCmdAsync(HttpContext, command);
				if (!(result is OkObjectResult))
				{
					if (result is ContentResult contentResult) return BadRequest($"Error: {contentResult.Content}");
					return BadRequest($"Error:{result}");
				}
				return result;
			}
			else
			{
				string messageError;
				if (!validation.ExistsCustomer)
				{
					messageError = $"Sorry, customer '{identificationNumber}' does not exist, favorite account cannot be removed.";
				}
				else
				{
					messageError = $"Sorry, customer does not have the account '{accountNumber}' as favorite.";
				}
				return PrefabBadRequest(messageError);
			}
		}

		[HttpDelete("api/exchange/customer/{identificationNumber}/accounts/favorite/external/{accountNumber}")]
        [Authorize(Roles = "devops")]//VERIFY_ROLES
        public async Task<IActionResult> RemoveCustomerFavoriteExternalAccountAsync(string identificationNumber, string accountNumber)
		{
			if (string.IsNullOrWhiteSpace(identificationNumber)) return BadRequest($"{nameof(identificationNumber)} is required");
			if (string.IsNullOrWhiteSpace(accountNumber)) return BadRequest($"{nameof(accountNumber)} is required");

			var result = await ExchangeManagerAPI.ExchangeManager.PerformQryAsync(HttpContext, $@"
                {{
                    existsCustomer = company.ExistsCustomerByIdentifier('{identificationNumber}');
                    print existsCustomer existsCustomer;
					if (existsCustomer) 
					{{
						customer = company.CustomerByIdentifier('{identificationNumber}');
						print customer.HasFavoriteExternalAccount('{accountNumber}') hasAccount;
					}}
					else 
					{{
						print false hasAccount;
					}}
                }}
            ");

			if (!(result is OkObjectResult))
			{
				if (result is ContentResult contentResult) return BadRequest($"Error: {contentResult.Content}");
				return BadRequest($"Error:{result}");
			}

			OkObjectResult o = (OkObjectResult)result;
			string json = o.Value.ToString();
			var validation = JsonConvert.DeserializeObject<CustomerAndAccountExistence>(json);

			if (validation.ExistsCustomer && validation.HasAccount)
			{
				string command = $@"
					{{
						customer = company.CustomerByIdentifier('{identificationNumber}');
						customer.RemoveExternalAccountAsFavorite('{accountNumber}');
					}}
				";

				result = await ExchangeManagerAPI.ExchangeManager.PerformCmdAsync(HttpContext, command);
				if (!(result is OkObjectResult))
				{
					if (result is ContentResult contentResult) return BadRequest($"Error: {contentResult.Content}");
					return BadRequest($"Error:{result}");
				}
				return result;
			}
			else
			{
				string messageError;
				if (!validation.ExistsCustomer)
				{
					messageError = $"Sorry, customer '{identificationNumber}' does not exist, favorite account cannot be removed.";
				}
				else
				{
					messageError = $"Sorry, customer does not have the account '{accountNumber}' as favorite.";
				}
				return PrefabBadRequest(messageError);
			}
		}

		[HttpPost("api/exchange/customer/{customerId}/account/{currencyCode}")]
		[Authorize(Roles = "c54")]
		public async Task<IActionResult> CreateCustomerAccountAsync(string customerId, string currencyCode)
		{
			if (string.IsNullOrWhiteSpace(customerId)) return BadRequest($"{nameof(customerId)} is required");
			if (string.IsNullOrWhiteSpace(currencyCode)) return BadRequest($"{nameof(currencyCode)} is required");

			var result = await ExchangeManagerAPI.ExchangeManager.PerformQryAsync(HttpContext, $@"
                {{
                    existsCustomer = company.ExistsCustomerByIdentifier('{customerId}');
                    print existsCustomer existsCustomer;
                }}
            ");

			if (!(result is OkObjectResult))
			{
				if (result is ContentResult contentResult) return BadRequest($"Error: {contentResult.Content}");
				return BadRequest($"Error:{result}");
			}

			OkObjectResult o = (OkObjectResult)result;
			string json = o.Value.ToString();
			var customerExistence = JsonConvert.DeserializeObject<CustomerExistence>(json);

			if (customerExistence.existsCustomer)
			{
				string command = $@"
					{{
						customer = company.CustomerByIdentifier('{customerId}');
						account = customer.CreateNewAccountFor('{currencyCode}');
						Eval('id =' + marketplace.NextAccountConsecutive() + ';');
						marketplace.RegisterNewAccount(id, account);
						print account.Identificator number;
					}}
				";

				result = await ExchangeManagerAPI.ExchangeManager.PerformCmdAsync(HttpContext, command);
				if (!(result is OkObjectResult))
				{
					if (result is ContentResult contentResult) return BadRequest($"Error: {contentResult.Content}");
					return BadRequest($"Error:{result}");
				}
			}
			return result;
		}

		[HttpPut("api/exchange/customer/{customerId}/defaultAccount")]
		[Authorize(Roles = "c55")]
		public async Task<IActionResult> UpdateDefaultCustomerAccountAsync(string customerId, [FromBody]DefaultCustomerAccountBody body)
		{
			if (body == null) return BadRequest($"Body is required");
			if (string.IsNullOrWhiteSpace(body.AccountNumber)) return BadRequest($"{nameof(body.AccountNumber)} is required");
			if (string.IsNullOrWhiteSpace(customerId)) return BadRequest($"{nameof(customerId)} is required");

			var result = await ExchangeManagerAPI.ExchangeManager.PerformQryAsync(HttpContext, $@"
                {{
                    existsCustomer = company.ExistsCustomerByIdentifier('{customerId}');
                    print existsCustomer existsCustomer;
                }}
            ");

			if (!(result is OkObjectResult))
			{
				if (result is ContentResult contentResult) return BadRequest($"Error: {contentResult.Content}");
				return BadRequest($"Error:{result}");
			}

			OkObjectResult o = (OkObjectResult)result;
			string json = o.Value.ToString();
			var customerExistence = JsonConvert.DeserializeObject<CustomerExistence>(json);

			if (customerExistence.existsCustomer)
			{
				string command = $@"
					{{
						print true existsCustomer;
						customer = company.CustomerByIdentifier('{customerId}');
						account = customer.FindAccount('{body.AccountNumber}');
						customer.SetDefaultAccount(account);
					}}
				";

				result = await ExchangeManagerAPI.ExchangeManager.PerformCmdAsync(HttpContext, command);
				if (!(result is OkObjectResult))
				{
					if (result is ContentResult contentResult) return BadRequest($"Error: {contentResult.Content}");
					return BadRequest($"Error:{result}");
				}
			}
			return result;
		}

		const byte MAX_ALIAS_LENGTH = 100;

		[HttpPut("api/exchange/customer/{identificationNumber}/accounts/{accountNumber}/alias")]
        [Authorize(Roles = "devops")]//VERIFY_ROLES
        public async Task<IActionResult> UpdateAccountAliasAsync(string identificationNumber, string accountNumber, [FromBody]CustomerAccountAliasBody body)
		{
			if (body == null) return BadRequest($"Body is required");
			if (string.IsNullOrWhiteSpace(accountNumber)) return BadRequest($"{nameof(accountNumber)} is required");
			if (string.IsNullOrWhiteSpace(identificationNumber)) return BadRequest($"{nameof(identificationNumber)} is required");
			if (string.IsNullOrWhiteSpace(body.Alias)) return BadRequest($"{nameof(body.Alias)} is required");
			if (body.Alias.Length > MAX_ALIAS_LENGTH) return BadRequest($"{nameof(body.Alias)} is greater than {MAX_ALIAS_LENGTH}");

			var alias = Validator.StringEscape(body.Alias);
			var result = await ExchangeManagerAPI.ExchangeManager.PerformQryAsync(HttpContext, $@"
                {{
                    existsCustomer = company.ExistsCustomerByIdentifier('{identificationNumber}');
                    print existsCustomer existsCustomer;
					if (existsCustomer) 
					{{
						customer = company.CustomerByIdentifier('{identificationNumber}');
						print customer.HasAccount('{accountNumber}') hasAccount;
						print customer.ExistsAccountAlias('{alias}') existsAccountAlias;
					}}
					else 
					{{
						print false hasAccount;
						print false existsAccountAlias;
					}}
                }}
            ");

			if (!(result is OkObjectResult))
			{
				if (result is ContentResult contentResult) return BadRequest($"Error: {contentResult.Content}");
				return BadRequest($"Error:{result}");
			}

			OkObjectResult o = (OkObjectResult)result;
			string json = o.Value.ToString();
			var customerAndAccountExistence = JsonConvert.DeserializeObject<CustomerAndAccountExistence>(json);

			if (customerAndAccountExistence.ExistsCustomer && customerAndAccountExistence.HasAccount && ! customerAndAccountExistence.ExistsAccountAlias)
			{
				string command = $@"
					{{
						print true existsCustomer;
						print true hasAccount;
						print false existsAccountAlias;
						customer = company.CustomerByIdentifier('{identificationNumber}');
						customer.UpdateAccountAlias('{accountNumber}','{alias}');
					}}
				";

				result = await ExchangeManagerAPI.ExchangeManager.PerformCmdAsync(HttpContext, command);
				if (!(result is OkObjectResult))
				{
					if (result is ContentResult contentResult) return BadRequest($"Error: {contentResult.Content}");
					return BadRequest($"Error:{result}");
				}
			}
			else
			{
				string messageError;
				if (! customerAndAccountExistence.ExistsCustomer)
				{
					messageError = $"Sorry, customer '{identificationNumber}' does not exist, alias cannot be changed.";
				}
				else if (! customerAndAccountExistence.HasAccount)
				{
					messageError = $"Sorry, customer has not the account '{accountNumber}', alias cannot be changed.";
				}
				else if (customerAndAccountExistence.ExistsAccountAlias)
				{
					messageError = $"Sorry, customer already has an account with alias '{body.Alias}', alias cannot be changed.";
				}
				else
				{
					messageError = $"Sorry, alias cannot be changed.";
				}
				return PrefabBadRequest(messageError);
			}
			return result;
		}

		[HttpGet("api/exchange/domains")]
		[Authorize(Roles = "c56")]
		public async Task<IActionResult> DomainsAsync(string status)
		{
			var noStatus = string.IsNullOrWhiteSpace(status);
			IActionResult result;
			string path = Security.UserPath(HttpContext);
			if (noStatus)
            {
				result = await ExchangeManagerAPI.ExchangeManager.PerformQryAsync(HttpContext, $@"
                {{
                    for (domains:company.Sales.AllDomains)
                    {{
						domain = domains;
						print domain.Id id;
                        print domain.Url url;
                        print company.Sales.CurrentStore.IsEnabledOn(domain) enabled;
						lastEntries = domain.LastEntries(5);
						for (log:lastEntries)
						{{
							print log.DateFormattedAsText date;
							print log.Who who;
							print log.Message message;
						}}
                    }}
                }}
            ");
			}
			else
            {
				var enabled = status.ToLowerInvariant().Trim() == "enabled";
				result = await ExchangeManagerAPI.ExchangeManager.PerformQryAsync(HttpContext, $@"
                {{
                    for (domains:company.Sales.AllDomains)
                    {{
                        if (company.Sales.CurrentStore.IsEnabledOn(domains) == {enabled} )
                        {{
                            domain = domains;
							print domain.Id id;
							print domain.Url url;
                            print {enabled} enabled;
							lastEntries = domain.LastEntries(5);
							for (log:lastEntries)
							{{
								print log.DateFormattedAsText date;
								print log.Who who;
								print log.Message message;
							}}
                        }}
                    }}
                }}
                ");
			}
			
			return result;
		}

		[HttpGet("api/stores")]
        [AllowAnonymous]
        public async Task<IActionResult> GetStoresAsync()
		{
			var result = await ExchangeManagerAPI.ExchangeManager.PerformQryAsync(HttpContext, $@"
            {{
                for (stores:company.Sales.AllStores)
                {{
					store = stores;
					print store.Id id;
                    print store.Name name;
					print store.Alias alias;
                }}
            }}
            ");

			return result;
		}

		[HttpGet("api/exchange/domains/{id}")]
        [Authorize(Roles = "devops")]//VERIFY_ROLES
        public async Task<IActionResult> GetDomainAsync(int id)
		{
			if (id <= 0) return BadRequest($"{nameof(id)} must be greater than 0");

			var result = await ExchangeManagerAPI.ExchangeManager.PerformQryAsync(HttpContext, $@"
            {{
				existsDomain = company.Sales.ExistsDomain({id});
				print existsDomain existsDomain;
				isEnabledInMarketplace = company.Sales.CurrentStore.ExistsDomain({id});
				print isEnabledInMarketplace isEnabledInMarketplace;
				if (existsDomain && isEnabledInMarketplace)
				{{
					domain = company.Sales.DomainFrom({id});
					print domain.Id id;
					print domain.Url url;
				}}
            }}
            ");
			return result;
		}

		[HttpPost("api/exchange/domain")]
        [Authorize(Roles = "devops")]//VERIFY_ROLES
        public async Task<IActionResult> CreateDomainAsync([FromBody] DomainBody body)
		{
			if (body == null) return BadRequest("Body is required");
			if (string.IsNullOrWhiteSpace(body.Url)) return BadRequest($"Parameter {nameof(body.Url)} is required");
			if (! body.Agent.HasValue) return BadRequest($"Parameter {nameof(body.Agent)} is required");

			var url = Validator.StringEscape(body.Url);
			string employeeName = Security.UserName(HttpContext);

			var msg = "Created";
			var result = await ExchangeManagerAPI.ExchangeManager.PerformCmdAsync(HttpContext, $@"
				{{
					Eval('domainId =' + company.Sales.NextDomainConsecutive() + ';');
					domain = company.Sales.CreateDomain(ItIsThePresent, domainId, '{url}', {body.Agent.Value});
					domain.AddAnnotation('{msg}', '{employeeName}', Now);
				}}
            ");
			return result;
		}
		 

		[HttpPut("api/exchange/domain")]
        [Authorize(Roles = "devops")]//VERIFY_ROLES
        public async Task<IActionResult> UpdateDomainAsync([FromBody] DomainUpdating body)
		{
			if (body == null) return BadRequest("Body is required");
			if (string.IsNullOrWhiteSpace(body.NewUrl)) return BadRequest($"Parameter {nameof(body.NewUrl)} is required");
			if (body.CurrentId <= 0) return BadRequest($"{nameof(body.CurrentId)} must be greater than 0");

			var result = await ExchangeManagerAPI.ExchangeManager.PerformQryAsync(HttpContext, $@"
            {{
				existsDomainInCompany = company.Sales.ExistsDomain({body.CurrentId});
				print existsDomainInCompany existsDomainInCompany;
				existsDomainInMarketplace = company.Sales.CurrentStore.ExistsDomain({body.CurrentId});
				print existsDomainInMarketplace existsDomainInMarketplace;
            }}
            ");
			if (!(result is OkObjectResult))
			{
				if (result is ContentResult contentResult) return BadRequest($"Error: {contentResult.Content}");
				return BadRequest($"Error:{result}");
			}

			OkObjectResult o = (OkObjectResult)result;
			string json = o.Value.ToString();
			var existence = JsonConvert.DeserializeObject<DomainExistenceInCompanyAndEnabledInMarketplace>(json);

			if (existence.ExistsDomainInCompany && existence.ExistsDomainInMarketplace)
            {
				var newUrl = Validator.StringEscape(body.NewUrl);
				string employeeName = Security.UserName(HttpContext);
				var msg = "Updated";
				result = await ExchangeManagerAPI.ExchangeManager.PerformCmdAsync(HttpContext, $@"
				{{
					domain = company.Sales.DomainFrom({body.CurrentId});
					domain.UpdateMainUrl(itIsThePresent, '{newUrl}');
					domain.AddAnnotation('{msg}', '{employeeName}', Now);
				}}
				");
			}
			else
            {
				if (existence.ExistsDomainInCompany && !existence.ExistsDomainInMarketplace)
				{
					return PrefabBadRequest($"Sorry, domain with id '{body.CurrentId}' is disabled in marketplace");
				}
				else
				{
					return PrefabBadRequest($"Sorry, domain with id '{body.CurrentId}' does not exist");
				}
			}
			return result;
		}

		[HttpPost("api/exchange/domain/enabled")]
		[Authorize(Roles = "c57")]
		public async Task<IActionResult> EnableDomainAsync(int id)
		{
			if (id <= 0) return BadRequest($"{nameof(id)} must be greater than 0");

			string path = Security.UserPath(HttpContext);
			var result = await ExchangeManagerAPI.ExchangeManager.PerformQryAsync(HttpContext, $@"
            {{
				currentStore = company.Sales.CurrentStore;
				existsDomainInCompany = company.Sales.ExistsDomain({id});
				print existsDomainInCompany existsDomainInCompany;
				existsDomainInMarketplace = currentStore.ExistsDomain({id});
				print existsDomainInMarketplace existsDomainInMarketplace;
				if (existsDomainInCompany && existsDomainInMarketplace)
                {{
					agent = marketplace.SearchAgent('{path}');
					domain = company.Sales.DomainFrom({id});
					containsDomain = agent.ContainsDomain(currentStore.Id, domain);
					print containsDomain containsDomain;
					containsStore = agent.ContainsStore(currentStore.Id);
					print containsStore containsStore;
				}}
            }}
            ");
			if (!(result is OkObjectResult))
			{
				return BadRequest($@"Error:{((ObjectResult)result).Value.ToString()}");
			}

			OkObjectResult o = (OkObjectResult)result;
			string json = o.Value.ToString();
			var existence = JsonConvert.DeserializeObject<DomainExistenceInCompanyAndEnabledInMarketplace>(json);

			string lineToAssignStoreAndDomain = string.Empty;
			if (!existence.ContainsDomain)
			{
				lineToAssignStoreAndDomain = $"agent.AssignToCurrentStore(domain);";
			}

			string employeeName = Security.UserName(HttpContext);
			var msg = "Enabled";
			result = await ExchangeManagerAPI.ExchangeManager.PerformCmdAsync(HttpContext, $@"
			{{
				domain = company.Sales.DomainFrom({id});
				domain.Visible = true;
				company.Sales.CurrentStore.EnableDomain(itIsThePresent, domain);
				domain.AddAnnotation('{msg}', '{employeeName}', Now);
			}}
			");
			if (!(result is OkObjectResult))
			{
				return BadRequest($@"Error:{((ObjectResult)result).Value.ToString()}");
			}
			return result;
		}

		[HttpPost("api/exchange/domain/disabled")]
		[Authorize(Roles = "c58")]
		public async Task<IActionResult> RemoveDomainAsync(int id)
		{
			if (id <= 0) return BadRequest($"{nameof(id)} must be greater than 0");

			string path = Security.UserPath(HttpContext);
			var result = await ExchangeManagerAPI.ExchangeManager.PerformQryAsync(HttpContext, $@"
            {{
				currentStore = company.Sales.CurrentStore;
				existsDomainInCompany = company.Sales.ExistsDomain({id});
				print existsDomainInCompany existsDomainInCompany;
				existsDomainInMarketplace = currentStore.ExistsDomain({id});
				print existsDomainInMarketplace existsDomainInMarketplace;
				if (existsDomainInCompany && existsDomainInMarketplace)
                {{
					agent = marketplace.SearchAgent('{path}');
					domain = company.Sales.DomainFrom({id});
					containsDomain = agent.ContainsDomain(currentStore.Id, domain);
					print containsDomain containsDomain;
					containsStore = agent.ContainsStore(currentStore.Id);
					print containsStore containsStore;
				}}
            }}
            ");
			if (!(result is OkObjectResult))
			{
				return BadRequest($@"Error:{((ObjectResult)result).Value.ToString()}");
			}

			OkObjectResult o = (OkObjectResult)result;
			string json = o.Value.ToString();
			var existence = JsonConvert.DeserializeObject<DomainExistenceInCompanyAndEnabledInMarketplace>(json);

			string lineToAssignStoreAndDomain = string.Empty;
			if (!existence.ContainsDomain)
			{
				lineToAssignStoreAndDomain = $"agent.AssignToCurrentStore(domain);";
			}
				
			string employeeName = Security.UserName(HttpContext);
			var msg = "Disabled";
			result = await ExchangeManagerAPI.ExchangeManager.PerformCmdAsync(HttpContext, $@"
			{{
				domain = company.Sales.DomainFrom({id});
				domain.Visible = false;
				company.Sales.CurrentStore.DisableDomain(itIsThePresent, domain);
				domain.AddAnnotation('{msg}', '{employeeName}', Now);
			}}
			");
			if (!(result is OkObjectResult))
			{
				return BadRequest($@"Error:{((ObjectResult)result).Value.ToString()}");
			}
			
			return result;
		}

		[DataContract(Name = "domain")]
		public class DomainBody
		{
			[DataMember(Name = "url")]
			public string Url { get; set; }
			[DataMember(Name = "agent")]
			public Agents? Agent { get; set; }
		}

		[DataContract(Name = "domainUpdating")]
		public class DomainUpdating
		{
			[DataMember(Name = "currentId")]
			public int CurrentId { get; set; }
			[DataMember(Name = "newUrl")]
			public string NewUrl { get; set; }
		}

		[DataContract(Name = "DomainExistenceInCompanyAndEnabledInMarketplace")]
		public class DomainExistenceInCompanyAndEnabledInMarketplace
		{
			[DataMember(Name = "existsDomainInCompany")]
			public bool ExistsDomainInCompany { get; set; }
			[DataMember(Name = "existsDomainInMarketplace")]
			public bool ExistsDomainInMarketplace { get; set; }
			[DataMember(Name = "containsDomain")]
			public bool ContainsDomain { get; set; }
			[DataMember(Name = "containsStore")]
			public bool ContainsStore { get; set; }
		}

		[DataContract(Name = "TempTransactionResponse")]
		public class TempTransactionResponse
		{
			[DataMember(Name = "transactionId")]
			public int TransactionId { get; set; }
			[DataMember(Name = "account")]
			public string Account { get; set; }
			[DataMember(Name = "customerNumber")]
			public string CustomerNumber { get; set; }
			[DataMember(Name = "storeId")]
			public int StoreId { get; set; }
			[DataMember(Name = "processorAccountId")]
			public int ProcessorAccountId { get; set; }
            [DataMember(Name = "agentId")]
            public int AgentId { get; set; }
            [DataMember(Name = "domainId")]
            public int DomainId { get; set; }
            [DataMember(Name = "domainUrl")]
            public string DomainUrl { get; set; }
			[DataMember(Name = "paymentMethodType")]
            public PaymentMethod? PaymentMethodType { get; set; }
        }

		[DataContract(Name = "DraftTransactionResponse")]
		public class DraftTransactionResponse
		{
			[DataMember(Name = "authorizationId")]
			public long AuthorizationId { get; set; }
			[DataMember(Name = "batchNumber")]
			public int BatchNumber { get; set; }
			[DataMember(Name = "agentId")]
			public int AgentId { get; set; }
			[DataMember(Name = "identificator")]
			public string Identificator { get; set; }
			[DataMember(Name = "now")]
			public string Now { get; set; }
			[DataMember(Name = "approval")]
			public TransactionResult Approval { get; set; }
			[DataMember(Name = "rejected")] 
			public bool Rejected { get; set; }
			[DataMember(Name = "customerNumber")]
			public string CustomerNumber { get; set; }
			[DataMember(Name = "domainId")]
			public int DomainId { get; set; }
			[DataMember(Name = "storeId")]
			public int StoreId { get; set; }
			[DataMember(Name = "isGuardianOn")]
			public bool IsGuardianOn { get; set; }
			[DataMember(Name = "isAmountInAgentRange")]
			public bool IsAmountInAgentRange { get; set; }
        }

		[DataContract(Name = "TransactionExistenceAndAgentValidationInRange")]
		public class TransactionExistenceAndAgentValidationInRange
		{
			[DataMember(Name = "existsTransaction")]
			public bool ExistsTransaction { get; set; }
			[DataMember(Name = "isAmountInAgentRange")]
			public bool IsAmountInAgentRange { get; set; }
			[DataMember(Name = "agentHasAvailable")]
			public bool AgentHasAvailable { get; set; }
			[DataMember(Name = "isApprovedCustomer")]
			public bool IsApprovedCustomer { get; set; }
			[DataMember(Name = "transactionItsEnabled")]
			public bool TransactionItsEnabled { get; set; }
			[DataMember(Name = "domainId")]
			public int DomainId { get; set; }
			
		}

		[DataContract(Name = "CustomerAndAccountExistence")]
		public class CustomerAndAccountExistence
		{
			[DataMember(Name = "existsCustomer")]
			public bool ExistsCustomer { get; set; }
			[DataMember(Name = "hasAccount")]
			public bool HasAccount { get; set; }
			[DataMember(Name = "existsAccountAlias")]
			public bool ExistsAccountAlias { get; set; }
		}

		[DataContract(Name = "CustomerExistenceAndAccountInMarketplace")]
		public class CustomerExistenceAndAccountInMarketplace
		{
			[DataMember(Name = "existsCustomer")]
			public bool ExistsCustomer { get; set; }
			[DataMember(Name = "existsAccountInMarketplace")]
			public bool ExistsAccountInMarketplace { get; set; }
		}

		struct StoreData
		{
			public int storeId { get; set; }
			public string storeName { get; set; }
			public string storeAlias { get; set; }
		}

		struct CustomerExistence
		{
			public bool existsCustomer { get; set; }
		}

		private struct AgentValidationInRange
		{
			public bool isItInRange { get; set; }
			public bool hasAvailable { get; set; }
			public decimal value { get; set; }
			public string currencyCodeAsText { get; set; }
			public string accountNumberIdentificator { get; set; }
			public string identificationNumber { get; set; }
			public int domainId { get; set; }
			public int domainAgentId { get; set; }
			public string domain { get; set; }
			public int storeId { get; set; }
			public int entityId { get; set; }
			public string paymentMethodName { get; set; }
			public PaymentMethod? paymentMethodType { get; set; }
		}

		struct AgentInformation
		{
			public string agentName { get; set; }
		}

		struct RiskRatingExistence
		{
			public bool existsRiskRating { get; set; }
			public bool isValidPriority { get; set; }
		}

		struct RiskRatingAndNewNameExistence
		{
			public bool existsRiskRating { get; set; }
			public bool isAlreadyUsedNewName { get; set; }
			public bool isValidPriority { get; set; }
		}

		[DataContract(Name = "ApprovalForWithdrawal")]
		public class ApprovalForTransaction
		{
			[DataMember(Name = "isInRange")]
			public bool IsInRange { get; set; }
			[DataMember(Name = "isGuardianOn")]
			public bool IsGuardianOn { get; set; }
		}

		[DataContract(Name = "CustomerNumberAndAgentValidation")]
		public class CustomerNumberAndAgentValidation
		{
			[DataMember(Name = "customerNumber")]
			public string CustomerNumber { get; set; }
			[DataMember(Name = "existsDomainInMarketplace")]
			public bool ExistsDomainInMarketplace { get; set; }
			[DataMember(Name = "isAmountInAgentRange")]
			public bool IsAmountInAgentRange { get; set; }
			[DataMember(Name = "agentHasAvailable")]
			public bool AgentHasAvailable { get; set; }
			[DataMember(Name = "isApprovedCustomer")]
			public bool IsApprovedCustomer { get; set; }
			[DataMember(Name = "isOnlyOneDriverEnabled")]
			public bool IsOnlyOneDriverEnabled { get; set; }
			[DataMember(Name = "transactionItsEnabled")]
			public bool TransactionItsEnabled { get; set; }
			[DataMember(Name = "domainId")]
			public int DomainId { get; set; }
			[DataMember(Name = "storeId")]
			public int StoreId { get; set; }
		}

		[DataContract(Name = "ExchangeRateBody")]
		public class ExchangeRateBody
		{
			[DataMember(Name = "fromCurrencyCode")]
			public string FromCurrencyCode { get; set; }
			[DataMember(Name = "toCurrencyCode")]
			public string ToCurrencyCode { get; set; }
			[DataMember(Name = "purchasePrice")]
			public decimal PurchasePrice { get; set; }
			[DataMember(Name = "salePrice")]
			public decimal SalePrice { get; set; }
		}

		[DataContract(Name = "DepositCreationBody")]
		public class DepositCreationBody
		{
			[DataMember(Name = "toCurrencyCode")]
			public string ToCurrencyCode { get; set; }
			[DataMember(Name = "amount")]
			public decimal Amount { get; set; }
			[DataMember(Name = "toIdentifier")]
			public string ToIdentifier { get; set; }
			[DataMember(Name = "accountNumber")]
			public string AccountNumber { get; set; }
			[DataMember(Name = "description")]
			public string Description { get; set; }
			[DataMember(Name = "voucher")]
			public string Voucher { get; set; }
			[DataMember(Name = "voucherurl")]
			public string VoucherUrl { get; set; }
			[DataMember(Name = "depositor")]
			public string Depositor { get; set; }
			[DataMember(Name = "paymentMethod")]
			public PaymentMethod? PaymentMethod { get; set; }
			[DataMember(Name = "entityId")]
			public int EntityId { get; set; }
		}

		[DataContract(Name = "TransactionRejectionBody")]
		public class TransactionRejectionBody
		{
			[DataMember(Name = "reason")]
			public string Reason { get; set; }
		}

		[DataContract(Name = "WithdrawalCreationBody")]
		public class WithdrawalCreationBody
		{
			[DataMember(Name = "accountNumber")]
			public string accountNumber { get; set; }

			[DataMember(Name = "fromCurrencyCode")]
			public string FromCurrencyCode { get; set; }
			[DataMember(Name = "amount")]
			public decimal Amount { get; set; }
			[DataMember(Name = "fromIdentifier")]
			public string FromIdentifier { get; set; }
			[DataMember(Name = "description")]
			public string Description { get; set; }
			[DataMember(Name = "realAccount")]
			public string RealAccount { get; set; }
			[DataMember(Name = "minerFee")]
			public decimal MinerFee { get; set; }
			[DataMember(Name = "paymentMethod")]
			public PaymentMethod? PaymentMethod { get; set; }
			[DataMember(Name = "entityId")]
			public int EntityId { get; set; }
		}

		[DataContract(Name = "TransferCreationBody")]
		public class TransferCreationBody
		{

			[DataMember(Name = "toIdentifier")]
			public string ToIdentifier { get; set; }

			[DataMember(Name = "fromIdentifier")]
			public string FromIdentifier { get; set; }

			[DataMember(Name = "fromCurrencyCode")]
			public string FromCurrencyCode { get; set; }
			[DataMember(Name = "toCurrencyCode")]
			public string ToCurrencyCode { get; set; }
			[DataMember(Name = "amount")]
			public decimal Amount { get; set; }
			[DataMember(Name = "description")]
			public string Description { get; set; }
			[DataMember(Name = "realAccount")] 
			public string RealAccount { get; set; }

			[DataMember(Name = "fromAccountNumber")]
			public string FromAccountNumber { get; set; }

			[DataMember(Name = "toAccountNumber")]
			public string ToAccountNumber { get; set; }
			[DataMember(Name = "paymentMethod")]
			public PaymentMethod? PaymentMethod { get; set; }
			[DataMember(Name = "entityId")]
			public int EntityId { get; set; }
		}

		[DataContract(Name = "CreditOrDebitNoteCreationBody")]
		public class CreditOrDebitNoteCreationBody
		{
			[DataMember(Name = "currencyCode")]
			public string CurrencyCode { get; set; }
			[DataMember(Name = "amount")]
			public decimal Amount { get; set; }
			[DataMember(Name = "toIdentifier")]
			public string ToIdentifier { get; set; }
			[DataMember(Name = "accountNumber")]
			public string AccountNumber { get; set; }
			[DataMember(Name = "description")]
			public string Description { get; set; }
			[DataMember(Name = "referenceId")]
			public int ReferenceId { get; set; }
			[DataMember(Name = "batchNumber")]
			public int BatchNumber { get; set; }
			[DataMember(Name = "attachments")]
			public List<string> Attachments { get; set; }
			[DataMember(Name = "paymentMethod")]
			public PaymentMethod? PaymentMethod { get; set; }
			[DataMember(Name = "entityId")]
			public int EntityId { get; set; }
		}

		[DataContract(Name = "VoucherBody")]
		public class VoucherBody
		{
			[DataMember(Name = "voucherUrl")]
			public string VoucherUrl { get; set; }
		}
	}

	public class TransactionResult
	{
		[DataMember(Name = "netsign")]
		public string Netsign { get; set; }
		[DataMember(Name = "net")]
		public decimal net { get; set; }
		[DataMember(Name = "netFormatted")]
		public string netFormatted { get; set; }
		[DataMember(Name = "grosssign")]
		public string Grosssign { get; set; }
		[DataMember(Name = "gross")]
		public decimal gross { get; set; }
		[DataMember(Name = "grossFormatted")]
		public string grossFormatted { get; set; }
		[DataMember(Name = "profitsign")]
		public string profitsign { get; set; }
		[DataMember(Name = "profit")]
		public decimal profit { get; set; }
		[DataMember(Name = "profitFormatted")]
		public string profitFormatted { get; set; }
		[DataMember(Name = "amountsign")]
		public string amountsign { get; set; }
		[DataMember(Name = "amount")]
		public decimal amount { get; set; }
		[DataMember(Name = "amountFormatted")]
		public string amountFormatted { get; set; }
	}

	internal class WithdrawalTransactionResult
	{
		[DataMember(Name = "netsign")]
		public string Netsign { get; set; }
		[DataMember(Name = "net")]
		public decimal net { get; set; }
		[DataMember(Name = "netFormatted")]
		public string netFormatted { get; set; }
		[DataMember(Name = "grosssign")]
		public string Grosssign { get; set; }
		[DataMember(Name = "gross")]
		public decimal gross { get; set; }
		[DataMember(Name = "grossFormatted")]
		public string grossFormatted { get; set; }
		[DataMember(Name = "profitsign")]
		public string profitsign { get; set; }
		[DataMember(Name = "profit")]
		public decimal profit { get; set; }
		[DataMember(Name = "profitFormatted")]
		public string profitFormatted { get; set; }
		[DataMember(Name = "amountsign")]
		public string amountsign { get; set; }
		[DataMember(Name = "amount")]
		public decimal amount { get; set; }
		[DataMember(Name = "amountFormatted")]
		public string amountFormatted { get; set; }
		[DataMember(Name = "disbursementsign")]
		public string disbursementsign { get; set; }
		[DataMember(Name = "disbursement")]
		public decimal disbursement { get; set; }
		[DataMember(Name = "disbursementFormatted")]
		public string disbursementFormatted { get; set; }
		[DataMember(Name = "feesign")]
		public string feesign { get; set; }
		[DataMember(Name = "fee")]
		public decimal fee { get; set; }
		[DataMember(Name = "feeFormatted")]
		public string feeFormatted { get; set; }
	}

	[DataContract(Name = "FavoriteInternalAccountBody")]
	public class FavoriteInternalAccountBody
	{
		[DataMember(Name = "accountNumber")]
		public string AccountNumber { get; set; }
		[DataMember(Name = "alias")]
		public string Alias { get; set; }
	}

	[DataContract(Name = "FavoriteExternalAccountBody")]
	public class FavoriteExternalAccountBody
	{
		[DataMember(Name = "accountNumber")]
		public string AccountNumber { get; set; }
		[DataMember(Name = "identifier")]
		public string Identifier { get; set; }
		[DataMember(Name = "currencyCode")]
		public string CurrencyCode { get; set; }
		[DataMember(Name = "alias")]
		public string Alias { get; set; }
	}

	[DataContract(Name = "DefaultCustomerAccountBody")]
	public class DefaultCustomerAccountBody
	{
		[DataMember(Name = "accountNumber")]
		public string AccountNumber { get; set; }
	}

	[DataContract(Name = "CustomerAccountAliasBody")]
	public class CustomerAccountAliasBody
	{
		[DataMember(Name = "alias")]
		public string Alias { get; set; }
	}

	[DataContract(Name = "RiskRatingBody")]
	public class RiskRatingBody
	{
		[DataMember(Name = "name")]
		public string Name { get; set; }
		[DataMember(Name = "description")]
		public string Description { get; set; }
		[DataMember(Name = "priority")]
		public int Priority { get; set; }
		[DataMember(Name = "enabled")]
		public bool Enabled { get; set; }
	}

	[DataContract(Name = "RiskRatingUpdaterBody")]
	public class RiskRatingUpdaterBody
	{
		[DataMember(Name = "name")]
		public string Name { get; set; }
		[DataMember(Name = "newName")]
		public string NewName { get; set; }
		[DataMember(Name = "description")]
		public string Description { get; set; }
		[DataMember(Name = "priority")]
		public int Priority { get; set; }
		[DataMember(Name = "enabled")]
		public bool Enabled { get; set; }
	}

	[DataContract(Name = "DepositsResponse")]
	public class DepositsResponse
	{
		[DataMember(Name = "transactions")]
		public DepositsDTO Transactions { get; set; }
		[DataMember(Name = "processors")]
		public List<ProcessorWithDistinctKey> Processors { get; set; }
	}

	[DataContract(Name = "WithdrawalsResponse")]
	public class WithdrawalsResponse
	{
		[DataMember(Name = "transactions")]
		public WithdrawalsDTO Transactions { get; set; }
		[DataMember(Name = "processors")]
		public List<ProcessorWithDistinctKey> Processors { get; set; }
	}

	[DataContract(Name = "TransfersResponse")]
	public class TransfersResponse
	{
		[DataMember(Name = "transactions")]
		public TransfersDTO Transactions { get; set; }
		[DataMember(Name = "processors")]
		public List<ProcessorWithDistinctKey> Processors { get; set; }
	}

	[DataContract(Name = "CreditNotesResponse")]
	public class CreditNotesResponse
	{
		[DataMember(Name = "transactions")]
		public CreditNotesDTO Transactions { get; set; }
		[DataMember(Name = "processors")]
		public List<ProcessorWithDistinctKey> Processors { get; set; }
	}

	[DataContract(Name = "DebitNotesResponse")]
	public class DebitNotesResponse
	{
		[DataMember(Name = "transactions")]
		public DebitNotesDTO Transactions { get; set; }
		[DataMember(Name = "processors")]
		public List<ProcessorWithDistinctKey> Processors { get; set; }
	}

	[DataContract(Name = "ProcessorWithoutAccounts")]
	public class ProcessorWithDistinctKey
	{
		[DataMember(Name = "alias")]
		public string Alias { get; set; }
		[DataMember(Name = "entity")]
		public string Entity { get; set; }
		[DataMember(Name = "paymentMethod")]
		public string PaymentMethod { get; set; }
		[DataMember(Name = "transactionType")]
		public string TransactionType { get; set; }
		[DataMember(Name = "currencyCode")]
		public string CurrencyCode { get; set; }
		[DataMember(Name = "accountId")]
		public int AccountId { get; set; }
	}
}
