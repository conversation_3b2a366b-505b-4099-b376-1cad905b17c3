﻿using Connectors.town.connectors.commons;
using GamesEngine.Business;
using GamesEngine.Settings;
using Google.Protobuf.Compiler;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using town.connectors.commons;
using static GamesEngine.Finance.PaymentChannels;

namespace GamesEngine.Finance
{
	[DataContract(Name = "transactionDataModel")]
	public abstract class TransactionDataModel
	{
		private string atAddress = "";
		[DataMember(Name = "atAddress")]
		public string AtAddress
		{
			get
			{
				return atAddress;
			}
			set
			{
				if (string.IsNullOrWhiteSpace(value)) throw new ArgumentNullException(nameof(value));
				atAddress = value;
			}
		}

		private decimal total;
		[DataMember(Name = "total")]
		public decimal Total
		{
			get
			{
				return total;
			}
			set
			{
				if (value < 0) throw new Exception($"{nameof(value)} {value} is not greater or equal than 0");
				total = value;
			}
		}

		private int authorizationNumber;
		[DataMember(Name = "authorizationNumber")]
		public int AuthorizationNumber
		{
			get
			{
				return authorizationNumber;
			}
			set
			{
				if (value < 0 && value != GamesEngine.Business.Accounting.CommonAuthorizationNumberForMultipleAuthorizations) throw new Exception($"{nameof(value)} {value} is not greater or equal than 0");
				authorizationNumber = value;
			}
		}

		private string currency;
		[DataMember(Name = "currency")]
		public string CurrencyCode
		{
			get
			{
				return currency;
			}
			set
			{
				currency = value;
			}
		}

        private DateTime useless;
        [DataMember(Name = "useless")]
        public DateTime Useless
        {
            get
            {
                return useless;
            }
            set
            {
                useless = value;
            }
        }

        private string processorKey;
		[DataMember(Name = "processorKey")]
		public string ProcessorKey
		{
			get
			{
				return processorKey;
			}
			set
			{
				if (string.IsNullOrWhiteSpace(value)) throw new Exception($"{nameof(value)} {value} is not greater or equal than 0");
				processorKey = value;
			}
		}

        public Localization Localization { get; protected private set; }
    }

	public class FragmentCreationResponse : KafkaMessage
	{
		internal FragmentCreationResponse(int theLowestBetId, int theHighestBetId, int theLowestWagerNumber, int theHighestWagerNumber, int authorizationNumber, int orderNumber)
		{
			TheLowestBetId = theLowestBetId;
			TheHighestBetId = theHighestBetId;
			TheLowestWagerNumber = theLowestWagerNumber;
			TheHighestWagerNumber = theHighestWagerNumber;
			AuthorizationNumber = authorizationNumber;
			OrderNumber = orderNumber;
		}

		public FragmentCreationResponse(string message):base(message)
		{
		}

		public int TheLowestBetId { get; private set; }

		public int TheHighestBetId { get; private set; }
		public int TheHighestWagerNumber { get; private set; }

		public int TheLowestWagerNumber { get; private set; }
		public int AuthorizationNumber { get; private set; }
		public int OrderNumber { get; private set; }

		protected override void Deserialize(string [] serializedMessage, out int fieldOrder)
		{
			base.Deserialize(serializedMessage, out fieldOrder);
			TheLowestBetId = int.Parse(serializedMessage[fieldOrder++]);
			TheHighestBetId = int.Parse(serializedMessage[fieldOrder++]);
			TheLowestWagerNumber = int.Parse(serializedMessage[fieldOrder++]);
			TheHighestWagerNumber = int.Parse(serializedMessage[fieldOrder++]);
			AuthorizationNumber = int.Parse(serializedMessage[fieldOrder++]);
			OrderNumber = int.Parse(serializedMessage[fieldOrder++]);
		}

        protected override void InternalSerialize()
        {
			base.InternalSerialize();
			AddProperty(TheLowestBetId).
			AddProperty(TheHighestBetId).
			AddProperty(TheLowestWagerNumber).
			AddProperty(TheHighestWagerNumber).
			AddProperty(AuthorizationNumber).
			AddProperty(OrderNumber);
		}
    }

	[DataContract(Name = "FragmentsCreationDTO")]
	public class FragmentsCreationBodyDTO
    {
        [DataMember(Name = "StoreId")]
        public int StoreId { get; set; }
        [DataMember(Name = "atAddress")]
        public string AtAddress { get; set; }
        [DataMember(Name = "authorizationNumber")]
        public int AuthorizationNumber { get; set; }
        [DataMember(Name = "orderNumber")]
        public int OrderNumber { get; set; }
        [DataMember(Name = "theLowestBetId")]
        public int TheLowestBetId { get; set; }
        [DataMember(Name = "theHighestBetId")]
        public int TheHighestBetId { get; set; }
        [DataMember(Name = "currencyCode")]
        public string CurrencyCode { get; set; }
        [DataMember(Name = "fragments")]
        public Fragment[] Fragments { get; set; }
        [DataMember(Name = "fragmentsChunks")]
        public FragmentsChunks FragmentsChunks { get; set; }
        [DataMember(Name = "total")]
        public decimal Total { get; set; }
        [DataMember(Name = "agentId")]
        public int AgentId { get; set; }
        [DataMember(Name = "date")]
        public DateTime Date { get; set; }
        [DataMember(Name = "useless")]
        public DateTime Useless { get; set; }
		[DataMember(Name = "processorKey")]
        public string ProcessorKey { get; set; }
		[DataMember(Name = "allRisksAreEquals")]
        public bool AllRisksAreEquals { get; set; }
        [DataMember(Name = "localization")]
        public Localization Localization { get; set; }


    }
	public class FragmentsCreationBody : TransactionDataModel
	{
		public FragmentsCreationBody()
		{
		}
		public FragmentsCreationBody(string message)
		{
			var fragmentsMessage = new FragmentCreationKafkaMessage(message);
			this.StoreId = fragmentsMessage.StoreId;
			AtAddress = fragmentsMessage.AtAddress;
			AuthorizationNumber = fragmentsMessage.AuthorizationNumber;
			OrderNumber = fragmentsMessage.OrderNumber;
			TheLowestBetId = fragmentsMessage.TheLowestBetId;
			TheHighestBetId = fragmentsMessage.TheHighestBetId;
			CurrencyCode = fragmentsMessage.Currency;
			Fragments = fragmentsMessage.Fragments;
			FragmentsChunks = fragmentsMessage.FragmentsChunks;
			Total = fragmentsMessage.Total;
			AgentId = (int)fragmentsMessage.Agent;
			Date = fragmentsMessage.Date;
			Useless = fragmentsMessage.Useless;
			ProcessorKey = fragmentsMessage.ProcessorKey;
            AllRisksAreEquals = fragmentsMessage.AllRisksAreEquals;
			Localization = fragmentsMessage.Localization;
			Domain = fragmentsMessage.Domain;
        }

		private Fragment[] fragments = new Fragment[0];
		[DataMember(Name = "fragments")]
		public Fragment[] Fragments
		{
			get
			{
				return fragments;
			}
			set
			{
				if (value == null) throw new Exception($"{nameof(value)} {value} can not be null.");
				this.fragments = value;
			}
		}
		public FragmentsChunks FragmentsChunks { get; internal set; }
        public bool AllRisksAreEquals { get; private set; }

        private int orderNumber;
		[DataMember(Name = "orderNumber")]
		public int OrderNumber
		{
			get
			{
				return orderNumber;
			}
			set
			{
				if (value < 0) throw new Exception($"{nameof(value)} {value} is not greater or equal than 0");
				orderNumber = value;
			}
		}

        private string domain;
        [DataMember(Name = "domain")]
        public string Domain
        {
            get
            {
                return domain;
            }
            set
            {
                if (string.IsNullOrEmpty(value)) throw new Exception($"{nameof(value)} {value} is null");
                domain = value;
            }
        }

        private int agentId;
		[DataMember(Name = "agentId")]
		public int AgentId
		{
			get
			{
				return agentId;
			}
			set
			{
				agentId = value;
			}
		}
		public Agents Agent
		{
			get
			{
				return (Agents)agentId;
			}

		}
		private int lowestBetId;
		[DataMember(Name = "lowestBetId")]
		public int TheLowestBetId
		{
			get
			{
				return lowestBetId;
			}
			set
			{
				if (value < 0) throw new Exception($"{nameof(value)} {value} is not greater or equal than 0");
				lowestBetId = value;
			}
		}

		private int highestBetId;
		[DataMember(Name = "highestBetId")]
		public int TheHighestBetId
		{
			get
			{
				return highestBetId;
			}
			set
			{
				if (value < 0) throw new Exception($"{nameof(value)} {value} is not greater or equal than 0");
				highestBetId = value;
			}
		}

		private int storeId;

		[DataMember(Name = "store")]
		public int StoreId
		{
			get
			{
				return storeId;
			}
			set
			{
				storeId = value;
			}
		}

		private DateTime date;
		public DateTime Date
		{
			get
			{
				return date;
			}
			set
			{
				date = value;
			}
		}

		internal class FragmentCreationKafkaMessage : KafkaMessage
		{
			public string AtAddress { get; private set; }
			public decimal Total { get; private set; }
			public int StoreId { get; private set; }
			private int agentId;
			public Agents Agent { get { return (Agents)agentId; } }
			public int TheLowestBetId { get; private set; }

			public int TheHighestBetId { get; private set; }

			public int AuthorizationNumber { get; private set; }
			public int OrderNumber { get; private set; }
            public bool AllRisksAreEquals { get; private set; } = true;
			public Localization Localization { get; private set; }

			public static readonly string NO_DOMAIN = "N/A";
            public string Domain { get; private set; }

			public string Currency { get { return Coin.Iso4217Code; } }
            public DateTime Date { get; private set; }
			public DateTime Useless { get; private set; }
			public Coin Coin { get; private set; }
			public Fragment[] Fragments { get; private set; } = new Fragment[0];
			public FragmentsChunks FragmentsChunks { get; private set; }
			public string ProcessorKey { get; private set; }

            internal FragmentCreationKafkaMessage(int storeId, string domain, string atAddress, int authorizationNumber, int orderNumber, int theLowestBetId, int theHighestBetId, Coin coin, Fragment[] fragments, decimal total, int agentId, DateTime date, DateTime useless, string processorKey)
			{
				StoreId = storeId;
				AtAddress = atAddress;
				TheLowestBetId = theLowestBetId;
				TheHighestBetId = theHighestBetId;
				AuthorizationNumber = authorizationNumber;
				OrderNumber = orderNumber;
				Coin = coin;
				Total = total;
				this.agentId = agentId;
				Date = date;
				Useless = useless;
				ProcessorKey = processorKey;
				Localization = Integration.Localization;

				Fragments = fragments;
                Domain = domain;
            }

			public FragmentCreationKafkaMessage(string message) : base(message)
			{
			}

            protected override void Deserialize(string [] serializedMessage, out int fieldOrder)
			{
				base.Deserialize(serializedMessage, out fieldOrder);
                Localization = (Localization)int.Parse(serializedMessage[fieldOrder++]);
                StoreId = int.Parse(serializedMessage[fieldOrder++]);

                int month, day, hour, minute, second;
                var isThereDate = int.TryParse(serializedMessage[fieldOrder], out int year) && year.ToString().Length == 4;
				if (isThereDate)
				{
					fieldOrder++;
					month = int.Parse(serializedMessage[fieldOrder++]);
					day = int.Parse(serializedMessage[fieldOrder++]);
					hour = int.Parse(serializedMessage[fieldOrder++]);
					minute = int.Parse(serializedMessage[fieldOrder++]);
					second = int.Parse(serializedMessage[fieldOrder++]);
					int ms = int.Parse(serializedMessage[fieldOrder++]);
					Date = new DateTime(year, month, day, hour, minute, second, ms);
				}
                isThereDate = int.TryParse(serializedMessage[fieldOrder], out year) && year.ToString().Length == 4;
                if (isThereDate)
                {
                    fieldOrder++;
                    month = int.Parse(serializedMessage[fieldOrder++]);
                    day = int.Parse(serializedMessage[fieldOrder++]);
                    hour = int.Parse(serializedMessage[fieldOrder++]);
                    minute = int.Parse(serializedMessage[fieldOrder++]);
                    second = int.Parse(serializedMessage[fieldOrder++]);
                    Useless = new DateTime(year, month, day, hour, minute, second);
                }
                AtAddress = serializedMessage[fieldOrder++];
				AuthorizationNumber = int.Parse(serializedMessage[fieldOrder++]);
				OrderNumber = int.Parse(serializedMessage[fieldOrder++]);
				TheLowestBetId = int.Parse(serializedMessage[fieldOrder++]);
				TheHighestBetId = int.Parse(serializedMessage[fieldOrder++]);
				Coin = Coinage.KafkaProperty2Coin(serializedMessage[fieldOrder++]);
				Total = decimal.Parse(serializedMessage[fieldOrder++]);
				agentId = int.Parse(serializedMessage[fieldOrder++]);
				ProcessorKey = serializedMessage[fieldOrder++];

				Domain = serializedMessage[fieldOrder++];

				List<Fragment> list = new List<Fragment>();
				FragmentsChunks fragmentsChunks = new FragmentsChunks();

				BisDeCompressor decompresser = new BisDeCompressor();
                var tempRisk = string.Empty;
                for (; fieldOrder < serializedMessage.Length ;)
				{
					string description = serializedMessage[fieldOrder++];
					description = decompresser.DecompressBis(description);

					Fragment fragment = new Fragment()
					{
						BetDescription = description,
						Risk = serializedMessage[fieldOrder++],
						ReferenceNumber = serializedMessage[fieldOrder++],
						ToWin = serializedMessage[fieldOrder++],
						Number = serializedMessage[fieldOrder++],
						TicketNumber = serializedMessage[fieldOrder++]
					};

                    if (string.IsNullOrWhiteSpace(tempRisk)) tempRisk = fragment.Risk;
                    if (tempRisk != fragment.Risk)
                    {
                        AllRisksAreEquals = false;
                    }
                    list.Add(fragment);
					fragmentsChunks.Add(fragment);

				}

				Fragments = list.ToArray();
				FragmentsChunks = fragmentsChunks;
			}

            protected override void InternalSerialize()
            {
				base.InternalSerialize();
				AddProperty((int)Localization).
				AddProperty(StoreId).
				AddProperty(Date.Year).
				AddProperty(Date.Month).
				AddProperty(Date.Day).
				AddProperty(Date.Hour).
				AddProperty(Date.Minute).
				AddProperty(Date.Second).
				AddProperty(Date.Millisecond).
				AddProperty(Useless).
				AddProperty(AtAddress).
				AddProperty(AuthorizationNumber).
				AddProperty(OrderNumber).
				AddProperty(TheLowestBetId).
				AddProperty(TheHighestBetId).
				AddProperty(Currency).
				AddProperty(Total).
				AddProperty(agentId).
				AddProperty(string.IsNullOrWhiteSpace(ProcessorKey) ? TransactionMessage.NO_PROCESSOR_KEY : ProcessorKey);

				AddProperty(Domain);

				BisCompressor compresser = new BisCompressor();
				foreach (Fragment fragment in Fragments)
				{
					string description = compresser.CompressBis(fragment.BetDescription);
					AddProperty(description).
					AddProperty(fragment.Risk).
					AddProperty(fragment.ReferenceNumber).
					AddProperty(fragment.ToWin).
					AddProperty(fragment.Number).
					AddProperty(fragment.TicketNumber);
				}
			}
        }
	}

	internal class FragmentSendingToAgentMessage : KafkaMessage
	{
		private int agentId;
		public Agents Agent { get { return (Agents)agentId; } }
		public int AuthorizationNumber { get; private set; }
		public Fragment[] Fragments { get; private set; }
		public FragmentSendingToAgentMessage(int authorizationNumber, int agentId, Fragment[] fragments)
		{
			AuthorizationNumber = authorizationNumber;
			this.agentId = agentId;
			Fragments = fragments;
		}
		public FragmentSendingToAgentMessage(string message) : base(message)
		{
		}

		protected override void Deserialize(string[] serializedMessage, out int fieldOrder)
		{
			base.Deserialize(serializedMessage, out fieldOrder);
			AuthorizationNumber = int.Parse(serializedMessage[fieldOrder++]);
			agentId = int.Parse(serializedMessage[fieldOrder++]);
			List<Fragment> list = new List<Fragment>();
			FragmentsChunks fragmentsChunks = new FragmentsChunks();
			BisDeCompressor decompresser = new BisDeCompressor();
			for (; fieldOrder < serializedMessage.Length;)
			{
				string description = serializedMessage[fieldOrder++];
				description = decompresser.DecompressBis(description);
				Fragment fragment = new Fragment()
				{
					BetDescription = description,
					Risk = serializedMessage[fieldOrder++],
					ReferenceNumber = serializedMessage[fieldOrder++],
					ToWin = serializedMessage[fieldOrder++],
					Number = serializedMessage[fieldOrder++]
				};
				list.Add(fragment);
				fragmentsChunks.Add(fragment);
			}
			Fragments = list.ToArray();
		}

        protected override void InternalSerialize()
        {
			base.InternalSerialize();
			AddProperty(AuthorizationNumber).
			AddProperty(agentId);
			BisCompressor compresser = new BisCompressor();
			foreach (Fragment fragment in Fragments)
			{
				string description = compresser.CompressBis(fragment.BetDescription);
				AddProperty(description).
				AddProperty(fragment.Risk).
				AddProperty(fragment.ReferenceNumber).
				AddProperty(fragment.ToWin).
				AddProperty(fragment.Number);
			}
		}
    }

	public class FragmentsChunks
	{
		private List<Fragments> fragmentsChunks = new List<Fragments>();
		private Fragments current;
		private int totalAmountOfFragments = 0;

		public void Add(Fragment fragment)
		{
			if (current == null)
			{
				current = new Fragments();
				fragmentsChunks.Add(current);
			}
			else if (current.Count >= 100)
			{
				current = new Fragments();
				fragmentsChunks.Add(current);
			}

			current.Add(fragment);
		}

		public IEnumerable<Fragments> List()
		{
			return fragmentsChunks;
		}

		public int TotalAmountOfFragments()
		{
			return totalAmountOfFragments;
		}

		public int AmountOfChunks()
		{
			return fragmentsChunks.Count;
		}

		internal bool AreEquals(FragmentsChunks fragmentsChunks)
		{
			if (this.fragmentsChunks.Count != fragmentsChunks.fragmentsChunks.Count) return false;

			for (int i = 0; i < this.fragmentsChunks.Count; i++)
			{
				if (AreNotTheSame(this.fragmentsChunks[i].BetDescriptions, fragmentsChunks.fragmentsChunks[i].BetDescriptions)
					|| this.fragmentsChunks[i].Count != fragmentsChunks.fragmentsChunks[i].Count
					|| AreNotTheSame(this.fragmentsChunks[i].FragmentsReferences, fragmentsChunks.fragmentsChunks[i].FragmentsReferences)
					|| this.fragmentsChunks[i].FragmentsRisk != fragmentsChunks.fragmentsChunks[i].FragmentsRisk
					|| this.fragmentsChunks[i].FragmentsTowin != fragmentsChunks.fragmentsChunks[i].FragmentsTowin
					|| this.fragmentsChunks[i].HasSameRiskAmounts != fragmentsChunks.fragmentsChunks[i].HasSameRiskAmounts
					|| this.fragmentsChunks[i].HasSameToWinAmounts != fragmentsChunks.fragmentsChunks[i].HasSameToWinAmounts
					|| AreNotTheSame(this.fragmentsChunks[i].Risks, fragmentsChunks.fragmentsChunks[i].Risks)
					|| AreNotTheSame(this.fragmentsChunks[i].ToWins, fragmentsChunks.fragmentsChunks[i].ToWins))
				{
					return false;
				}
			}

			return true;
		}

		private bool AreNotTheSame(List<string> array1, List<string> array2)
		{
			if (array1.Count != array2.Count) return true;

			for (int i = 0; i < array1.Count; i++)
			{
				if (array1[i] != array2[i])
				{
					return true;
				}
			}

			return false;
		}

		private bool AreNotTheSame(Fragment[] fragments1, Fragment[] fragments2)
		{
			if (fragments1.Length != fragments2.Length) return true;

			for (int i = 0; i < fragments1.Length; i++)
			{
				if (fragments1[i].BetDescription != fragments2[i].BetDescription
					|| fragments1[i].Number != fragments2[i].Number
					|| fragments1[i].Risk != fragments2[i].Risk
					|| fragments1[i].ReferenceNumber != fragments2[i].ReferenceNumber
					|| fragments1[i].ToWin != fragments2[i].ToWin)
				{
					return true;
				}
			}

			return false;
		}
	}

	public class Fragments
	{
		public decimal FragmentsRisk { get; private set; }
		public decimal FragmentsTowin { get; private set; }

		public bool HasSameToWinAmounts { get; private set; }
		public bool HasSameRiskAmounts { get; private set; }
		public List<string> FragmentsReferences { get;}
		public List<string> BetDescriptions { get;}
		public List<string> ToWins { get;}
		public List<string> Risks { get;}
        public List<string> TicketNumbers { get; }
        public int Count { get; private set; }


		public Fragments()
		{
			FragmentsRisk = decimal.MinValue;
			FragmentsTowin = decimal.MinValue;
			HasSameToWinAmounts = true;
			HasSameRiskAmounts = true;
			FragmentsReferences = new List<string>();
			BetDescriptions = new List<string>();
			ToWins = new List<string>();
			Risks = new List<string>();
			TicketNumbers = new List<string>();
			Count = 0;
		}

		public void Add(Fragment fragment)
		{
			bool itsTheFirstItem = FragmentsRisk == decimal.MinValue;
			decimal currentFragmentRisk = decimal.Parse(fragment.Risk);
			decimal currentFragmentTowin = decimal.Parse(fragment.ToWin);

			if (itsTheFirstItem)
			{
				FragmentsRisk = currentFragmentRisk;
				FragmentsTowin = currentFragmentTowin;
			}
			else
			{
				HasSameToWinAmounts = HasSameToWinAmounts && FragmentsTowin == currentFragmentTowin;
				HasSameRiskAmounts = HasSameRiskAmounts && FragmentsRisk == currentFragmentRisk;
			}

			FragmentsReferences.Add(fragment.ReferenceNumber);
			BetDescriptions.Add(fragment.BetDescription);
			ToWins.Add(currentFragmentTowin.ToString("F"));
			Risks.Add(currentFragmentRisk.ToString("F"));
			TicketNumbers.Add(fragment.TicketNumber);
			Count++;
		}
	}

	public class ToWinByDrawAndNumber : Objeto
	{
		public string drawDate { get; set; }
		public string drawHour { get; set; }
		public decimal toWin { get; set; }
		public decimal risk { get; set; }
		public int pick { get; set; }
		public string number { get; set; }
		public string draw { get; set; }
		public string type { get; set; }
		public string description { get; set; }
		public string status { get; set; }
		public string ticketId { get; set; }
		public bool freePlay { get; set; }
	}

	[DataContract(Name = "ExternalMultiLockBalanceData")]
	public class ExternalMultiLockBalanceData : LockBalanceData
    {
		[DataMember(Name = "toWinsByDrawAndNumber")]
		public List<ToWinByDrawAndNumber> ToWinsByDrawAndNumber { get; set; }

        [DataMember(Name = "useLocalAuthorization")]
        public bool UseLocalAuthorization { get; set; }
    }

	public abstract class CashierResponse : KafkaMessage
	{
		public enum CashierResponseType { LOCK_BALANCE, PAY_FRAGMENT_WITH_PROBLEMS }
		private readonly CashierResponseType type;

		internal CashierResponse(CashierResponseType type)
		{
			this.ResponseType = type;
		}

		public CashierResponse(CashierResponseType type, string message):base(message)
		{
			this.ResponseType = type;//TODO xxxx Revisar con Cris
		}

		public CashierResponseType ResponseType { get; protected set; }
	}

	[DataContract(Name = "lockBalanceResponse")]
	public sealed class LockBalanceResponse : CashierResponse, IPaymentChannelResponse
    {
		private int transactionNumber;
		public LockBalanceResponse() : base(CashierResponseType.LOCK_BALANCE)
		{
		}

		[DataMember(Name = "authorization")]
		public int AuthorizationNumber
		{
			get { return transactionNumber; }
			set { transactionNumber = value; }
		}

		[DataMember(Name = "reason")]
		public string Reason { get; set; }

        [DataMember(Name = "code")]
        public AuthorizationResponseCode Code { get; set; }
        public string Url { get; set; }
        public string Response { get; set; }

        [DataMember(Name = "toWinsByDrawAndNumber")]
        public List<ToWinByDrawAndNumber> ToWinsByDrawAndNumber { get; set; }

        protected override void Deserialize(string [] serializedMessage, out int fieldOrder)
		{
			base.Deserialize(serializedMessage, out fieldOrder);
			this.ResponseType = (CashierResponseType)int.Parse(serializedMessage[fieldOrder++]);
			this.AuthorizationNumber = int.Parse(serializedMessage[fieldOrder++]);
		}

        protected override void InternalSerialize()
        {
			base.InternalSerialize();
			AddProperty((int)ResponseType).
			AddProperty(transactionNumber);
		}
    }

	[DataContract(Name = "BalanceFPResponse")]
	public sealed class BalanceFPResponse
	{
		[DataMember(Name = "balance")]
		public decimal Balance { get; set; }
	}

	public sealed class FragmentPaymentsWithProblemsResponse : CashierResponse
	{
		private List<int> fragments = new List<int>();
		private List<string> authorizations = new List<string>();
		public FragmentPaymentsWithProblemsResponse() : base(CashierResponseType.PAY_FRAGMENT_WITH_PROBLEMS)
		{
		}

		public FragmentPaymentsWithProblemsResponse(string msg) : base(CashierResponseType.PAY_FRAGMENT_WITH_PROBLEMS, msg)
		{
		}

		protected override void Deserialize(string [] serializedMessage, out int fieldOrder)
		{
			base.Deserialize(serializedMessage, out fieldOrder);
			ResponseType = (CashierResponseType)int.Parse(serializedMessage[fieldOrder++]);
			BisDeCompressor decompresser = new BisDeCompressor();

			for (; fieldOrder < serializedMessage.Length;)
			{
				string authorizator = serializedMessage[fieldOrder++];
				authorizator = decompresser.DecompressBis(authorizator);
				authorizations.Add(authorizator);
				fragments.Add(int.Parse(serializedMessage[fieldOrder++]));
			}
		}

		public void Add(string authorization, int fragment)
		{
			if (string.IsNullOrEmpty(authorization)) throw new GameEngineException(nameof(authorization));
            if (fragment <= 0) throw new GameEngineException($"{nameof(fragment)} {fragment} is not valid");

            authorizations.Add(authorization);
			fragments.Add(fragment);
		}

		public bool HasItems()
		{
			return authorizations.Count > 0;
		}

		internal IEnumerable<AuthorizationAndFragmentPair> ListItems()
		{
			List<AuthorizationAndFragmentPair> items = new List<AuthorizationAndFragmentPair>();

			for (int i = 0; i < authorizations.Count; i++)
			{
				items.Add(
					new AuthorizationAndFragmentPair(authorizations[i], fragments[i], string.Empty)
					);
			}

			return items;
		}

        protected override void InternalSerialize()
        {
			base.InternalSerialize();
			AddProperty((int)ResponseType);

			BisCompressor compresser = new BisCompressor();
			for (int i = 0; i < authorizations.Count; i++)
			{
				string authorizator = compresser.CompressBis(authorizations[i]);
				AddProperty(authorizator);
				AddProperty(fragments[i]);
			}
		}
    }

    public class FragmentsWithProblemsResponse
    {
        public List<AuthorizationAndFragmentPair> FragmentsWithProblems { get; set; } = new List<AuthorizationAndFragmentPair>();

        public bool HasItems { get; set; } = false;

        public FragmentsWithProblemsResponse() { }

        public void Add(string ticketNumber, int wagerNumber)
        {
            FragmentsWithProblems.Add(new AuthorizationAndFragmentPair(ticketNumber, wagerNumber, string.Empty));
            HasItems = true;
        }
    }

    public class AuthorizationAndFragmentPair
    {
        internal AuthorizationAndFragmentPair(string authorization, int fragment, string initialGradingStatus)
        {
            if (string.IsNullOrEmpty(authorization)) throw new GameEngineException(nameof(authorization));
			if (fragment <= 0) throw new GameEngineException($"{nameof(fragment)} {fragment} is not valid");

            Authorization = authorization;
            Fragment = fragment;
            InitialGradingStatus = initialGradingStatus;
        }

        public string Authorization { get; }
        public int Fragment { get; }
        public string InitialGradingStatus { get; set; }
    }


    [DataContract(Name = "TicketForRefunding")]
    public class TicketForRefunding : Objeto
    {
        [DataMember(Name = "accountNumber")]
        public string AccountNumber { get; set; }
        [DataMember(Name = "authorizationNumber")]
        public string AuthorizationNumber { get; set; }

        [DataMember(Name = "wagerNumber")]
        public int WagerNumber { get; set; }
        [DataMember(Name = "adjustedWinAmount")]
        public decimal AdjustedWinAmount { get; set; }
        [DataMember(Name = "adjustedLossAmount")]
        public decimal AdjustedLossAmount { get; set; }
        [DataMember(Name = "agentId")]
        public int AgentId { get; set; }
        [DataMember(Name = "currencyCode")]
        public string CurrencyCode { get; set; }
        [DataMember(Name = "domainUrl")]
        public string DomainUrl { get; set; }
        [DataMember(Name = "gradingStatus")]
        public string GradingStatus { get; set; }
    }

    [DataContract(Name = "TicketNumberResult")]
    public class TicketNumberResult : Objeto
    {
        [DataMember(Name = "ticketNumber")]
        public string TicketNumber { get; set; }
        [DataMember(Name = "gradingStatus")]
        public string GradingStatus { get; set; }
    }

    public sealed class FragmentPaymentMessages
	{
		private Dictionary<string, string> atAddressByAuthorizationNumber = new Dictionary<string, string>();
		private Dictionary<Coin, List<PayFragmentsMessage>> payFragmentsMessageByCurrencies = new Dictionary<Coin, List<PayFragmentsMessage>>();
        public Localization Localization { get; }
        public string Owner { get; }
		public int StoreId { get; }
		public int ProcessorId { get; }
		public bool SometimeWereTicketsSentToAccounting { get; }
		public string Concept { get; }

		public FragmentPaymentMessages(string msg)
		{
			var expandedMsg = FragmentPaymentCompressor.FragmentSplit(msg);
			string[] messages = KafkaMessages.Split(expandedMsg);
            Localization = (Localization)int.Parse(messages[0]);
            Owner = messages[1];
			StoreId = int.Parse(messages[2]);
			ProcessorId = int.Parse(messages[3]);
			int indexToStart = 4;
			if (bool.TryParse(messages[4], out bool sometimeWereTicketsSentToAccounting))
			{
				indexToStart = 5;
				SometimeWereTicketsSentToAccounting = sometimeWereTicketsSentToAccounting;
			}

			for (int i = indexToStart; i < messages.Length; i++)
			{
				string message = messages[i];

				FragmentPaymentMessage payFragment = new FragmentPaymentMessage(message);

				atAddressByAuthorizationNumber.TryAdd(payFragment.AuthorizationNumber.ToString(), payFragment.AtAddress.Trim());

				var isWinningPrizeExactlyRisk = payFragment.AdjustedWinAmount == 0 && payFragment.AdjustedLossAmount == 0 && payFragment.Status == WagerStatus.W;
				var isAdjustWinAmountRequired = payFragment.AdjustedWinAmount != 0 && payFragment.Status == WagerStatus.W;
				var isAdjustLossAmountRequired = isWinningPrizeExactlyRisk || (payFragment.AdjustedLossAmount != 0 && payFragment.Status == WagerStatus.L);

				PayFragmentsMessage messageToSend = new PayFragmentsMessage()
				{
					AdjustedLossAmount = isAdjustLossAmountRequired ? payFragment.AdjustedLossAmount.ToString() : string.Empty,
					AdjustedWinAmount = isAdjustWinAmountRequired ? payFragment.AdjustedWinAmount.ToString() : "0",
					DailyFigureDate_YYYYMMDD = payFragment.Now.ToString("yyyyMMdd"),
					IsValidTicketNumber = true,
					Outcome = isWinningPrizeExactlyRisk ? WagerStatus.L.ToString() : payFragment.Status.ToString(),
					TicketNumber = payFragment.AuthorizationNumber.ToString(),
					WagerNumber = payFragment.WagerNumber,
					AgentId = (int)payFragment.Agent,
					DomainUrl = payFragment.DomainUrl
				};

				if (messageToSend.WagerNumber != 0)//Arregla el Id 463395 de la BD pues fue una compra sin wagers
				{
					List<PayFragmentsMessage> payFragmentsOfThisCurrency;
					if (!payFragmentsMessageByCurrencies.ContainsKey(payFragment.Currency))
					{
						payFragmentsOfThisCurrency = new List<PayFragmentsMessage>();
						payFragmentsMessageByCurrencies.Add(payFragment.Currency, payFragmentsOfThisCurrency);
					}
					else
					{
						payFragmentsOfThisCurrency = payFragmentsMessageByCurrencies[payFragment.Currency];
					}

					payFragmentsOfThisCurrency.Add(messageToSend);
				}

			}
		}

		public IEnumerable<Coin> Currencies()
		{
			return payFragmentsMessageByCurrencies.Keys;
		}

		public List<PayFragmentsMessage> MessagesBy(Coin currencyCode)
		{
			return payFragmentsMessageByCurrencies[currencyCode];
		}

        public List<PayFragmentsWithAtAddressMessage> WithAtAddressMessageBy(Coin currencyCode)
        {
			List<PayFragmentsWithAtAddressMessage> result = new List<PayFragmentsWithAtAddressMessage>();
			foreach (var payFragmentsMessage in payFragmentsMessageByCurrencies[currencyCode])
			{
				string atAddress = GetAtAddressForThisAuthorizationNumber(payFragmentsMessage.TicketNumber);
                var message = PayFragmentsWithAtAddressMessage.ConverterWithAtAddress(atAddress, payFragmentsMessage);
                result.Add(message);
            }
			return result;
        }

        public string GetAtAddressForThisAuthorizationNumber(string authorizationNumber)
		{
			return atAddressByAuthorizationNumber[authorizationNumber];
		}

	}

	public sealed class FragmentPaymentSendingToAgentMessages : KafkaMessage
	{
		private List<PayFragmentsMessage> payFragmentsMessage = new List<PayFragmentsMessage>();
		public IEnumerable<PayFragmentsMessage> Fragments
		{
			get
			{
				return payFragmentsMessage;
			}
		}
		public FragmentPaymentSendingToAgentMessages(List<PayFragmentsMessage> payFragmentsMessage)
		{
			this.payFragmentsMessage = payFragmentsMessage;
		}
		public FragmentPaymentSendingToAgentMessages(string message) : base(message)
		{
		}
		
		protected override void Deserialize(string[] serializedMessage, out int fieldOrder)
		{
			base.Deserialize(serializedMessage, out fieldOrder);
			for (; fieldOrder < serializedMessage.Length ;)
			{
				var fragment = new PayFragmentsMessage()
				{
					DailyFigureDate_YYYYMMDD = serializedMessage[fieldOrder++],
					IsValidTicketNumber = true,
					Outcome = serializedMessage[fieldOrder++],
					TicketNumber = serializedMessage[fieldOrder++],
					WagerNumber = int.Parse(serializedMessage[fieldOrder++]),
					AgentId = int.Parse(serializedMessage[fieldOrder++]),
                    DomainUrl = serializedMessage[fieldOrder++],
                    AdjustedWinAmount = serializedMessage[fieldOrder++]
				};
				payFragmentsMessage.Add(fragment);
			}
		}

        protected override void InternalSerialize()
        {
			base.InternalSerialize();
			foreach (var fragment in Fragments)
			{
				var adjustedWinAmount = string.IsNullOrWhiteSpace(fragment.AdjustedWinAmount) ? "0" : fragment.AdjustedWinAmount;
				AddProperty(fragment.DailyFigureDate_YYYYMMDD).
				AddProperty(fragment.Outcome).
				AddProperty(fragment.TicketNumber).
				AddProperty(fragment.WagerNumber).
				AddProperty(fragment.AgentId).
                AddProperty(fragment.DomainUrl).
                AddProperty(adjustedWinAmount);
			}
		}
    }

	public sealed class FragmentPaymentCompressor : KafkaMessagesBuffer
	{
		public const char ACCOUNTS_SEPARATOR = '\uFFFC';
		private const int MAX_SIZE = 10000;
		private Message message = new Message();
		public FragmentPaymentCompressor(bool itIsThePresent, string topic) : base(itIsThePresent, topic, MAX_SIZE)
		{
		}

		private FragmentPaymentMessage lastFragmentPayment = null;

		override public void Send(KafkaMessage kafkamessage)
		{
			FragmentPaymentMessage fragmentPayment = (FragmentPaymentMessage)kafkamessage;
			bool itsNOTheSameTNAndAccountOfPrevoiusFragment =
				lastFragmentPayment == null ||
				lastFragmentPayment.AtAddress != fragmentPayment.AtAddress ||
				lastFragmentPayment.AuthorizationNumber != fragmentPayment.AuthorizationNumber;

			if (itsNOTheSameTNAndAccountOfPrevoiusFragment)
			{
				message.AddProperty(ACCOUNTS_SEPARATOR).
				AddProperty(fragmentPayment.AtAddress).
				AddProperty(fragmentPayment.AuthorizationNumber).
				AddProperty(fragmentPayment.Currency.Iso4217Code).
				AddProperty((int)fragmentPayment.Agent).
                AddProperty(fragmentPayment.DomainUrl);
			}

			message.AddProperty(fragmentPayment.WagerNumber).
			AddProperty(fragmentPayment.AdjustedWinAmount.ToString()).
			AddProperty(fragmentPayment.AdjustedLossAmount.ToString()).
			AddProperty(fragmentPayment.Status.ToString());

			lastFragmentPayment = fragmentPayment;

			if (message.Size() >= MAX_SIZE)
			{
				base.Send(message);
				lastFragmentPayment = null;
				message.Clear();
			}
		}

		override public void Dispose()
		{
			if (!message.ItsEmpty())
			{
				base.Send(message);
				lastFragmentPayment = null;
				message.Clear();
			}
			base.Dispose();
		}

		public static string Expand(string message2)
		{
			if (message2.StartsWith(ACCOUNTS_SEPARATOR))
			{
				return Message.Expand(message2);
			}
			else
			{
				return message2;
			}
		}

		public static string FragmentSplit(string message2)
		{
			if (message2.StartsWith(ACCOUNTS_SEPARATOR))
			{
				return Message.Expand(message2);
			}
			else
			{
				return message2;
			}
		}

		internal Action<KafkaMessages> GenerateHeader(string employeeName, int storeId, int procesorId, bool sometimeWereTicketsSentToAccounting, DateTime now)
		{
			return (KafkaMessages kafkaMessages) =>
			{
				kafkaMessages.Add(ACCOUNTS_SEPARATOR);
                kafkaMessages.Add((int)Integration.Localization);
                kafkaMessages.Add(employeeName);
				kafkaMessages.Add(storeId);
				kafkaMessages.Add(procesorId);
				kafkaMessages.Add(sometimeWereTicketsSentToAccounting);
				kafkaMessages.Add(now.Year);
				kafkaMessages.Add(now.Month);
				kafkaMessages.Add(now.Day);
			};
		}

		class Message : KafkaMessage
		{
			internal Message()
			{

			}
			internal Message(string msg) : base(msg)
			{
				
			}

			internal static string Expand(string msg)
			{
				StringBuilder outputBuilder = new StringBuilder();

				string[] messages = KafkaMessages.Split(msg);// ignore ACCOUNTS_SEPARATOR
				int index = 1;
                int localizationId = int.Parse(messages[index++]);
                string employeeName = messages[index++];
				string storeSerialized = messages[index++];
				string processorIdSerialized = messages[index++];
				bool sometimeWereTicketsSentToAccounting = bool.Parse(messages[index++]);
				string year = messages[index++];
				string month = messages[index++];
				string day = messages[index++];
				string[] serializedMessage = KafkaMessage.Split(messages[index++]);

                outputBuilder.Append(localizationId);
                outputBuilder.Append(KafkaMessages.MESSAGE_SEPARATOR);
                outputBuilder.Append(employeeName);
				outputBuilder.Append(KafkaMessages.MESSAGE_SEPARATOR);
				outputBuilder.Append(storeSerialized);
				outputBuilder.Append(KafkaMessages.MESSAGE_SEPARATOR);
				outputBuilder.Append(processorIdSerialized);
				outputBuilder.Append(KafkaMessages.MESSAGE_SEPARATOR);
				outputBuilder.Append(sometimeWereTicketsSentToAccounting);
				outputBuilder.Append(KafkaMessages.MESSAGE_SEPARATOR);
				string token = ACCOUNTS_SEPARATOR.ToString();
				index = 1;
				while (index < serializedMessage.Length)
				{
					string atAddress = serializedMessage[index++];
					string authorizationNumber = serializedMessage[index++];
					string currencySerialized = serializedMessage[index++];
					string agentSerialized = serializedMessage[index++];
					string domainSerialized = serializedMessage[index++];
					while (index < serializedMessage.Length)
					{
						string fragmentNumberOrToken = serializedMessage[index++];

						bool itsANewAccount = fragmentNumberOrToken == token;
						if (itsANewAccount) break;

						string wagerNumber = fragmentNumberOrToken;
						string adjustedWinAmountSerialized = serializedMessage[index++];
						string adjustedLossAmountSerialized = serializedMessage[index++];
						string statusSerialized = serializedMessage[index++];

						outputBuilder.Append(atAddress);
						outputBuilder.Append(KafkaMessage.PROPERTIES_SEPARATOR);
						outputBuilder.Append(authorizationNumber);
						outputBuilder.Append(KafkaMessage.PROPERTIES_SEPARATOR);
						outputBuilder.Append(wagerNumber);
						outputBuilder.Append(KafkaMessage.PROPERTIES_SEPARATOR);
						outputBuilder.Append(statusSerialized);
						outputBuilder.Append(KafkaMessage.PROPERTIES_SEPARATOR);
						outputBuilder.Append(year);
						outputBuilder.Append(KafkaMessage.PROPERTIES_SEPARATOR);
						outputBuilder.Append(month);
						outputBuilder.Append(KafkaMessage.PROPERTIES_SEPARATOR);
						outputBuilder.Append(day);
						outputBuilder.Append(KafkaMessage.PROPERTIES_SEPARATOR);
						outputBuilder.Append(adjustedWinAmountSerialized);
						outputBuilder.Append(KafkaMessage.PROPERTIES_SEPARATOR);
						outputBuilder.Append(adjustedLossAmountSerialized);
						outputBuilder.Append(KafkaMessage.PROPERTIES_SEPARATOR);
						outputBuilder.Append(currencySerialized);
						outputBuilder.Append(KafkaMessage.PROPERTIES_SEPARATOR);
						outputBuilder.Append(agentSerialized);
						outputBuilder.Append(KafkaMessage.PROPERTIES_SEPARATOR);
                        outputBuilder.Append(domainSerialized);
                        outputBuilder.Append(KafkaMessages.MESSAGE_SEPARATOR);
                    }
				}

				outputBuilder.Length--;
				return outputBuilder.ToString();
			}

			protected override void Deserialize(string [] message, out int fieldOrder)
			{
				base.Deserialize(message, out fieldOrder);
                Localization localization = (Localization)int.Parse(message[fieldOrder++]);
                string employeeName = message[fieldOrder++];
				string storeSerialized = message[fieldOrder++];
				string processorIdSerialized = message[fieldOrder++];
				bool sometimeWereTicketsSentToAccounting = bool.Parse(message[fieldOrder++]);
				string year = message[fieldOrder++];
				string month = message[fieldOrder++];
				string day = message[fieldOrder++];
				string[] serializedMessage = KafkaMessage.Split(message[fieldOrder++]);

				string token = ACCOUNTS_SEPARATOR.ToString();
				fieldOrder = 1;
				while (fieldOrder < serializedMessage.Length)
				{
					string atAddress = serializedMessage[fieldOrder++];
					string authorizationNumber = serializedMessage[fieldOrder++];
					string currencySerialized = serializedMessage[fieldOrder++];

					while (fieldOrder < serializedMessage.Length)
					{
						string fragmentNumberOrToken = serializedMessage[fieldOrder++];

						bool itsANewAccount = fragmentNumberOrToken == token;
						if (itsANewAccount) break;

						string wagerNumber = fragmentNumberOrToken;
						string adjustedWinAmountSerialized = serializedMessage[fieldOrder++];
						string adjustedLossAmountSerialized = serializedMessage[fieldOrder++];
						string statusSerialized = serializedMessage[fieldOrder++];
					}
				}
			}

		}
	}
	public sealed class FragmentPaymentMessage : KafkaMessage
	{
		public string AtAddress { get; set; }
		public int AuthorizationNumber { get; set; }
		public int WagerNumber { get; set; }
		public WagerStatus Status { get; set; }
		public DateTime Now { get; set; }
		public decimal AdjustedWinAmount { get; set; }
		public decimal AdjustedLossAmount { get; set; }
		private int agentId;
		public Agents Agent { get { return (Agents)agentId; } }
		public string DomainUrl { get; private set; }
		public Coin Currency { get; set; }
		public PayFragmentsMessage PayFragmentsMessage { get; internal set; }

		internal FragmentPaymentMessage(Coin currencyCodes,  string atAddress, int authorizationNumber, int wagerNumber, WagerStatus status, DateTime now, decimal adjustedWinAmount, decimal adjustedLossAmount, int agentId, string domain)
		{
			AtAddress = atAddress;
			AuthorizationNumber = authorizationNumber;
			WagerNumber = wagerNumber;
			Status = status;
			Now = now;
			AdjustedWinAmount = adjustedWinAmount;
			AdjustedLossAmount = adjustedLossAmount;
			Currency = currencyCodes;
			this.agentId = agentId;
			this.DomainUrl = domain;
		}

		public FragmentPaymentMessage(string message) : base(message) { }

		protected override void Deserialize(string [] serializedMessage, out int fieldOrder)
		{
			base.Deserialize(serializedMessage, out fieldOrder);
			AtAddress = serializedMessage[fieldOrder++];
			AuthorizationNumber = int.Parse(serializedMessage[fieldOrder++]);
			WagerNumber = int.Parse(serializedMessage[fieldOrder++]);
			Status = (WagerStatus)Enum.Parse(typeof(WagerStatus), serializedMessage[fieldOrder++]);
			Now = new DateTime(
				int.Parse(serializedMessage[fieldOrder++]),
				int.Parse(serializedMessage[fieldOrder++]),
				int.Parse(serializedMessage[fieldOrder++]));
			AdjustedWinAmount = decimal.Parse(serializedMessage[fieldOrder++]);
			AdjustedLossAmount = decimal.Parse(serializedMessage[fieldOrder++]);

			int currencyEnumId;
			string currencyIsoCode = int.TryParse(serializedMessage[fieldOrder], out currencyEnumId) ?
					((Currencies.CODES)currencyEnumId).ToString() :
					serializedMessage[fieldOrder];
			fieldOrder++;
			Currency = Coinage.Coin(currencyIsoCode);
			agentId = int.Parse(serializedMessage[fieldOrder++]);
			DomainUrl = serializedMessage[fieldOrder++];
        }

		protected override void InternalSerialize()
        {
			base.InternalSerialize();
			AddProperty(AtAddress).
			AddProperty(AuthorizationNumber).
			AddProperty(WagerNumber).
			AddProperty(Status.ToString()).
			AddProperty(Now.Year).
			AddProperty(Now.Month).
			AddProperty(Now.Day).
			AddProperty(AdjustedWinAmount).
			AddProperty(AdjustedLossAmount).
			AddProperty(Currency.Iso4217Code).
			AddProperty(agentId);
			AddProperty(DomainUrl);
		}
    }

	[DataContract(Name = "BalancesResponse")]
	public class BalancesResponse
	{
		[DataMember(Name = "balance")]
		public List<BalanceResponse> Balances { get; set; } = new List<BalanceResponse>();

		public void Update(BalancesResponse response)
		{
			if (response == null) throw new ArgumentNullException(nameof(response));

			foreach (var balance in response.Balances)
			{
				if (Balances.Exists(b => b.AtAddress == balance.AtAddress && b.CurrencyCode == balance.CurrencyCode)) throw new GameEngineException($"A balance with code '{balance.CurrencyCode}' already was added.");
				Balances.Add(balance);
			}
		}

		public void Add(BalanceResponse response)
		{
			if (response == null) throw new ArgumentNullException(nameof(response));

			if (Balances.Exists(b => b.AtAddress == response.AtAddress && b.CurrencyCode == response.CurrencyCode)) throw new GameEngineException($"A balance with code '{response.CurrencyCode}' already was added.");
			Balances.Add(response);
		}
	}

	[DataContract(Name = "balanceResponse")]
	public class BalanceResponse : BalanceResponseBase
	{
		[DataMember(Name = "alias")]
		public string Alias { get; set; }
		[DataMember(Name = "atAddress")]
		public string AtAddress { get; set; }
		[DataMember(Name = "type")]
		public string Type
		{
			get
			{
				return Coinage.Coin(CurrencyCodeAsText).Name;
			}
		}

		[DataMember(Name = "code")]
		public string CurrencyCodeAsText { get; set; }
		public override string CurrencyCode
		{
			get
			{
				return Coinage.Coin(CurrencyCodeAsText).Iso4217Code;
			}
		}
		[DataMember(Name = "lockedFormatted")]
		public string LockedFormatted
		{
			get
			{
				return Currency.Factory(CurrencyCode, Locked).ToDisplayFormat();
			}
		}
		[DataMember(Name = "balanceFormatted")]
		public string BalanceFormatted
		{
			get
			{
				return Currency.Factory(CurrencyCode, Balance).ToDisplayFormat();
			}
		}

        [DataMember(Name = "unicode")]
        public string Unicode
        {
            get
            {
                return Currency.Unicode(CurrencyCode);
            }
        }

        [DataMember(Name = "accounts")]
		public List<AccountResponse> Accounts { get; set; } = new List<AccountResponse>();
	}

	[DataContract(Name = "AccountResponse")]
	public class AccountResponse
	{
		[DataMember(Name = "accountNumber")]
		public string AccountNumber { get; set; }
		[DataMember(Name = "code")]
		public string CurrencyCodeAsText { get; set; }
		internal Coin CurrencyCode
		{
			get
			{
				return Coinage.Coin(CurrencyCodeAsText);
			}
		}

		[DataMember(Name = "available")]
		public decimal Available { get; set; }
		[DataMember(Name = "availableFormatted")]
		public string AvailableFormatted
		{
			get
			{
				return Currency.Factory(CurrencyCode.Iso4217Code, Available).ToDisplayFormat();
			}
		}

        [DataMember(Name = "unicode")]
        public string Unicode
        {
            get
            {
                return Currency.Unicode(CurrencyCode.Iso4217Code);
            }
        }
    }

	[DataContract(Name = "ValidatePlayerResponse")]
	public class ValidatePlayerResponse
	{
		[DataMember(Name = "error")]
		public string Error { get; set; }
		[DataMember(Name = "message")]
		public string Message { get; set; }
		
        public ValidatePlayerResponse()
        {

        }
        public ValidatePlayerResponse(string error, string message)
        {
			Error = error;
			Message = message;
		}

		internal bool IsValid()
        {
			return string.IsNullOrWhiteSpace(Message);
        }
	}

	public sealed class MovementsDeserialitor
	{
		public int StoreId { get; private set; }
		private List<Movement> movements = new List<Movement>();
		NewMovementMessage movementMessages = new NewMovementMessage();

		public void Separate(string msg)
		{
			var isMovementCompressedMessage = int.TryParse(msg[0].ToString(), out int storeId);
			if (isMovementCompressedMessage)
			{
				var messages = KafkaMessages.Separate(msg);

				if (!messages.Any()) throw new GameEngineException($"At least 1 movement is required.");
				foreach (var message in messages)
				{
					movementMessages.Separate(message);
					Who = movementMessages.Who;
					StoreId = movementMessages.StoreId;
				}
				movements = movementMessages.Movements;
			}
			else
			{
				string[] messages = KafkaMessages.Split(msg);

				if (messages.Length < 2) throw new GameEngineException($"At least 1 who and 1 movement its required.");

				Who = messages[0];

				for (int i = 1; i < messages.Length; i++)
				{
					string message = messages[i];
					var movementMessage = new OldMovementMessage(Who, message);
					movements.Add(movementMessage.Movement);

					if (i == 1)
					{
						StoreId = movementMessage.Movement.StoreId;
					}
					else
					{
						if (StoreId != movementMessage.Movement.StoreId) throw new GameEngineException($"All movements must have the same Store in a message. Stored: {StoreId}, Current: {movementMessage.Movement.StoreId} index:{i}");
					}
				}
			}
		}

		public void Clear()
		{
			movements.Clear();
			movementMessages.Clear();
		}

		public string Who { get; private set; }
		public IEnumerable<Movement> Movements { get { return movements; } }
	}

	public sealed class NewMovementMessage : KafkaMessage
	{
		public List<Movement> Movements { get; } = new List<Movement>();
		public int StoreId { get; private set; }
		public string Who { get; private set; }
		public Coin CurrencyCode { get; private set; }
        public int SourceId { get; private set; }
        public string Concept { get; private set; }
		public string AtAddress { get; private set; }
		public DateTime Day { get; private set; }
		public decimal CurrentAmount { get; private set; }
		public Currency CurrencyForCurrentAmount { get; private set; }
		public decimal NewBalance { get; private set; }
		public string DocumentNumber { get; private set; }
		public string ReferenceNumber { get; private set; }
		public decimal NewLock { get; private set; }
		public string AccountNumber { get; private set; }
		public int ProcessorId { get; private set; }
		public int CountMovements { get; private set; }
		public bool IsPaired { get; private set; }
		public Movement.type Type1 { get; private set; }
		public Movement.type Type2 { get; private set; }

		const string NoData = "-";
		const string DefaultConcept = "Wager(s) placed";

		public NewMovementMessage()
		{

		}
		public NewMovementMessage(List<Movement> movements, string who)
		{
			if (movements == null || movements.Count == 0) throw new ArgumentNullException(nameof(movements));
			Movements = movements;

			var firstMovement = Movements.First();
			StoreId = firstMovement.StoreId;
			Who = who;
			CurrencyCode = firstMovement.Currency;
			SourceId = firstMovement.Source;
			Concept = firstMovement.Concept == DefaultConcept ? NoData : firstMovement.Concept;
			AtAddress = firstMovement.AtAddress;
			Day = firstMovement.Day;
			CurrentAmount = firstMovement.CurrentAmount;
			NewBalance = firstMovement.NewBalance;
			DocumentNumber = firstMovement.DocumentNumber;
			ReferenceNumber = firstMovement.Reference;
			NewLock = firstMovement.NewLock;
			AccountNumber = firstMovement.AccountNumber;
			ProcessorId = firstMovement.ProcessorId;
			CountMovements = movements.Count;
			IsPaired = movements.Count > 1 && movements[0].Type != movements[1].Type;
			Type1 = movements[0].Type;
			if (IsPaired)
			{
				Type2 = movements[1].Type;
			}

			AllMovementsWithTheSameProperties();
		}

		public NewMovementMessage(Movement movement)
		{
			if (movement == null) throw new ArgumentNullException(nameof(movement));
			Movements.Add(movement);

			StoreId = movement.StoreId;
			Who = movement.Who;
			CurrencyCode = movement.Currency;
			SourceId = movement.Source;
			Concept = movement.Concept == DefaultConcept ? NoData : movement.Concept;
			AtAddress = movement.AtAddress;
			Day = movement.Day;
			CurrentAmount = movement.CurrentAmount;
			NewBalance = movement.NewBalance;
			DocumentNumber = movement.DocumentNumber;
			ReferenceNumber = movement.Reference;
			NewLock = movement.NewLock;
			AccountNumber = movement.AccountNumber;
			ProcessorId = movement.ProcessorId;
			CountMovements = 1;
			Type1 = movement.Type;
		}

		private NewMovementMessage(string message) : base(message)
		{
		}

		[Conditional("DEBUG")]
		void AllMovementsWithTheSameProperties()
		{
			foreach (var movement in Movements)
			{
				if (StoreId != movement.StoreId) throw new GameEngineException($"{nameof(movement)} has a different {nameof(StoreId)}. Expected: {StoreId} actual: {movement.StoreId}");
				if (CurrencyCode != movement.Currency) throw new GameEngineException($"{nameof(movement)} has a different {nameof(CurrencyCode)}. Expected: {CurrencyCode} actual: {movement.Currency}");

			}
		}

		internal void Separate(string message)
		{
			string[] attributes = KafkaMessage.Split(message);

			Deserialize(attributes, out _);
		}

		public new void Clear()
		{
			Movements.Clear();
			base.Clear();
		}

		protected override void Deserialize(string[] serializedMessage, out int fieldOrder)
		{
			base.Deserialize(serializedMessage, out fieldOrder);
			var storeId = int.Parse(serializedMessage[fieldOrder++]);
			StoreId = storeId;
			var who = serializedMessage[fieldOrder++];
			Who = who;
			var currencyCode = Coinage.KafkaProperty2Coin(serializedMessage[fieldOrder++]);
			var sourceId = int.Parse(serializedMessage[fieldOrder++]);
			var concept = serializedMessage[fieldOrder++];
			var atAddress = serializedMessage[fieldOrder++];
			var day = new DateTime(
				int.Parse(serializedMessage[fieldOrder++]),
				int.Parse(serializedMessage[fieldOrder++]),
				int.Parse(serializedMessage[fieldOrder++]),
				int.Parse(serializedMessage[fieldOrder++]),
				int.Parse(serializedMessage[fieldOrder++]),
				int.Parse(serializedMessage[fieldOrder++]),
				int.Parse(serializedMessage[fieldOrder++]));

			var currentAmount = decimal.Parse(serializedMessage[fieldOrder++]);
			Currency currencyForCurrentAmount = new Currency(currencyCode, currentAmount);

			var newBalance = decimal.Parse(serializedMessage[fieldOrder++]);
			var documentNumber = serializedMessage[fieldOrder++];
			var referenceNumber = serializedMessage[fieldOrder++];

			var newlock = decimal.Parse(serializedMessage[fieldOrder++]);
			string accountNumber = serializedMessage[fieldOrder++];
			var processorId = int.Parse(serializedMessage[fieldOrder++]);

			if (concept == NoData) concept = DefaultConcept;
			if (referenceNumber == NoData) referenceNumber = string.Empty;

			var countMovements = int.Parse(serializedMessage[fieldOrder++]);
			var isPaired = bool.Parse(serializedMessage[fieldOrder++]);
			var type1 = (Movement.type)int.Parse(serializedMessage[fieldOrder++]);
			if (isPaired)
			{
				var type2 = (Movement.type)int.Parse(serializedMessage[fieldOrder++]);
				for (int index = 0; index < countMovements; index += 2)
				{
					var balance = newBalance;
					var movement = GetMovement(type1, sourceId, balance, atAddress, day, currencyForCurrentAmount, who, documentNumber, storeId, concept, referenceNumber, newlock, accountNumber, processorId);
					Movements.Add(movement);
					movement = GetMovement(type2, sourceId, balance - currentAmount, atAddress, day, currencyForCurrentAmount, who, documentNumber, storeId, concept, referenceNumber, newlock, accountNumber, processorId);
					Movements.Add(movement);
				}
			}
			else
			{
				var balance = newBalance;
				for (int index = 0; index < countMovements; index++)
				{
					var movement = GetMovement(type1, sourceId, balance, atAddress, day, currencyForCurrentAmount, who, documentNumber, storeId, concept, referenceNumber, newlock, accountNumber, processorId);
					Movements.Add(movement);
					balance += currentAmount;
				}
			}
		}

		Movement GetMovement(Movement.type type, int source, decimal balance, string atAddress, DateTime day, Currency currency, string who, string documentNumber, int storeId, string concept, string reference, 
			decimal newLock, string accountNumber, int processorId)
		{
			Movement movement;
			switch (type)
			{
				case Movement.type.Debit:
					if (source == Sources.NO_SOURCE_ID)
					{
						movement = new WithDrawWithOutSourceMovement(
						atAddress,
						day,
						currency,
						balance,
						who,
						documentNumber,
						storeId,
						concept,
						reference,
						newLock,
						accountNumber,
						processorId
						);
					}
					else
					{
						movement = new WithDrawMovement(source, atAddress, day, currency, balance, who, documentNumber, storeId, concept, reference, newLock, accountNumber, processorId);
					}
					break;
				case Movement.type.Unlock:
                    if (source == Sources.NO_SOURCE_ID)
                    {
                        movement = new UnLockWithOutSourceMovement(
						atAddress,
						day,
						currency,
						balance,
						who,
						documentNumber,
						storeId,
						concept,
						reference,
						newLock,
						accountNumber,
						processorId
						);
					}
                    else
                    {
                        movement = new UnLockMovement(source, atAddress, day, currency, balance, who, documentNumber, storeId, concept, reference, newLock, accountNumber, processorId);
                    }
					break;
				case Movement.type.Lock:
                    if (source == Sources.NO_SOURCE_ID)
                    {
                        movement = new LockWithOutSourceMovement(
						atAddress,
						day,
						currency,
						balance,
						who,
						documentNumber,
						storeId,
						concept,
						reference,
						newLock,
						accountNumber,
						processorId
						);
					}
                    else
                    {
                        movement = new LockMovement(source, atAddress, day, currency, balance, who, documentNumber, storeId, concept, reference, newLock, accountNumber, processorId);
                    }
					break;
				case Movement.type.Credit:
                    if (source == Sources.NO_SOURCE_ID)
                    {
                        movement = new AccreditWithOutSourceMovement(
						atAddress,
						day,
						currency,
						balance,
						who,
						documentNumber,
						storeId,
						concept,
						reference,
						newLock,
						accountNumber,
						processorId
						);
					}
                    else
                    {
                        movement = new AccreditMovement(source, atAddress, day, currency, balance, who, documentNumber, storeId, concept, reference, newLock, accountNumber, processorId);
                    }
					break;
				default:
					throw new GameEngineException($"{nameof(type)} {type} does not exist");
			}

			return movement;
		}

		protected override void InternalSerialize()
        {
			base.InternalSerialize();
			AddProperty((int)StoreId).
			AddProperty(Who).
			AddProperty(CurrencyCode.Iso4217Code).
			AddProperty(SourceId);
			if (string.IsNullOrWhiteSpace(Concept))
			{
				AddProperty(NoData);
			}
			else
			{
				AddProperty(Concept);
			}
			AddProperty(AtAddress).
			AddProperty(Day.Year).
			AddProperty(Day.Month).
			AddProperty(Day.Day).
			AddProperty(Day.Hour).
			AddProperty(Day.Minute).
			AddProperty(Day.Second).
			AddProperty(Day.Millisecond).
			AddProperty(CurrentAmount).
			AddProperty(NewBalance).
			AddProperty(DocumentNumber);
			if (string.IsNullOrWhiteSpace(ReferenceNumber))
			{
				AddProperty(NoData);
			}
			else
			{
				AddProperty(ReferenceNumber);
			}
			AddProperty(NewLock).
			AddProperty(AccountNumber).
			AddProperty(ProcessorId);
			AddProperty(CountMovements).
			AddProperty(IsPaired).
			AddProperty((int)Type1);
			if (IsPaired) AddProperty((int)Type2);
		}
    }

	public sealed class OldMovementMessage : KafkaMessage
	{
		private Movement movement;
		private string who;
		private const string NO_DATA = "NA";
		public Movement Movement
		{
			get { return movement; }
		}

		internal OldMovementMessage(Movement movement)
		{
			this.movement = movement;
		}

		public OldMovementMessage(string who, string message):base(who + PROPERTIES_SEPARATOR + message)
		{

		}

		protected override void Deserialize(string [] serializedMessage, out int fieldOrder)
		{
			base.Deserialize(serializedMessage, out fieldOrder);
			this.who = serializedMessage[fieldOrder++];
			var type = (Movement.type)Enum.Parse(typeof(Movement.type), serializedMessage[fieldOrder++]);
			var sourceNumber = int.Parse(serializedMessage[fieldOrder++]);
			var atAddress = serializedMessage[fieldOrder++];
			var date = new DateTime(
				int.Parse(serializedMessage[fieldOrder++]),
				int.Parse(serializedMessage[fieldOrder++]),
				int.Parse(serializedMessage[fieldOrder++]),
				int.Parse(serializedMessage[fieldOrder++]),
				int.Parse(serializedMessage[fieldOrder++]),
				int.Parse(serializedMessage[fieldOrder++]),
				int.Parse(serializedMessage[fieldOrder++]));
			var currencyCode = serializedMessage[fieldOrder++];
			var currentAmount = decimal.Parse(serializedMessage[fieldOrder++]);
			var newAmount = decimal.Parse(serializedMessage[fieldOrder++]);

			var documentNumber = serializedMessage[fieldOrder++];
			var storeId = int.Parse(serializedMessage[fieldOrder++]);
			var concept = serializedMessage[fieldOrder++];
			var reference = serializedMessage[fieldOrder++];

			var newlock = decimal.Parse(serializedMessage[fieldOrder++]);
			string accountNumber = serializedMessage[fieldOrder++];

			if (concept	== NO_DATA) concept = "";
			if (reference == NO_DATA) reference = "";

			string isoCode = Coinage.Coin(currencyCode).Iso4217Code;
			Currency currency = Currency.Factory(isoCode, currentAmount);

			switch (type)
			{
				case Movement.type.Debit:
					if (sourceNumber == Sources.NO_SOURCE_ID)
					{
						this.movement = new WithDrawWithOutSourceMovement(
							atAddress,
							date,
							currency,
							newAmount,
							who,
							documentNumber,
							storeId,
							concept,
							reference,
							newlock,
							accountNumber,
							WholePaymentProcessor.NoPaymentProcessor
							);
					}
					else
					{
						this.movement = new WithDrawMovement(
							sourceNumber,
							atAddress,
							date,
							currency,
							newAmount,
							who,
							documentNumber,
							storeId,
							concept,
							reference,
							newlock,
							accountNumber,
							WholePaymentProcessor.NoPaymentProcessor
							);
					}

					break;
				case Movement.type.Unlock:
					if (sourceNumber == Sources.NO_SOURCE_ID)
					{
						this.movement = new UnLockWithOutSourceMovement(
							atAddress,
							date,
							currency,
							newAmount,
							who,
							documentNumber,
							storeId,
							concept,
							reference,
							newlock,
							accountNumber,
							WholePaymentProcessor.NoPaymentProcessor
							);
					}
					else
					{
						this.movement = new UnLockMovement(
							sourceNumber,
							atAddress,
							date,
							currency,
							newAmount,
							who,
							documentNumber,
							storeId,
							concept,
							reference,
							newlock,
							accountNumber,
							WholePaymentProcessor.NoPaymentProcessor
							);
					}
					break;
				case Movement.type.Lock:
					if (sourceNumber == Sources.NO_SOURCE_ID)
					{
						this.movement = new LockWithOutSourceMovement(
							atAddress,
							date,
							currency,
							newAmount,
							who,
							documentNumber,
							storeId,
							concept,
							reference,
							newlock,
							accountNumber,
							WholePaymentProcessor.NoPaymentProcessor
							);
					}
					else
					{
						this.movement = new LockMovement(
							sourceNumber,
							atAddress,
							date,
							currency,
							newAmount,
							who,
							documentNumber,
							storeId,
							concept,
							reference,
							newlock,
							accountNumber,
							WholePaymentProcessor.NoPaymentProcessor
							);
					}
					break;
				case Movement.type.Credit:
					if (sourceNumber == Sources.NO_SOURCE_ID)
					{
						this.movement = new AccreditWithOutSourceMovement(
							atAddress,
							date,
							currency,
							newAmount,
							who,
							documentNumber,
							storeId,
							concept,
							reference,
							newlock,
							accountNumber,
							WholePaymentProcessor.NoPaymentProcessor
							);
					}
					else
					{
						this.movement = new AccreditMovement(
							sourceNumber,
							atAddress,
							date,
							currency,
							newAmount,
							who,
							documentNumber,
							storeId,
							concept,
							reference,
							newlock,
							accountNumber,
							WholePaymentProcessor.NoPaymentProcessor
							);
					}
					break;
				default:
					throw new GameEngineException($"{nameof(type)} {type} does not exist");
			}
		}

        protected override void InternalSerialize()
        {
			base.InternalSerialize();
			AddProperty(movement.Type.ToString()).
			AddProperty(movement.Source).
			AddProperty(movement.AtAddress).
			AddProperty(movement.Day).
			AddProperty(movement.Day.Millisecond).
			AddProperty(movement.CurrencyAsText).
			AddProperty(movement.CurrentAmount).
			AddProperty(movement.NewBalance).
			AddProperty(movement.DocumentNumber).
			AddProperty(movement.StoreId);

			if (String.IsNullOrEmpty(movement.Concept))
			{
				AddProperty(NO_DATA);
			}
			else
			{
				AddProperty(movement.Concept);
			}

			if (String.IsNullOrEmpty(movement.Reference))
			{
				AddProperty(NO_DATA);
			}
			else
			{
				AddProperty(movement.Reference);
			}

			AddProperty(movement.NewLock).
			AddProperty(movement.AccountNumber);
		}
    }

    public abstract class TransactionMessage : KafkaMessage
	{
		private string atAddress;
		private int sourceNumber;
		private Coin coin;
		private int storeId;
		private string domain;
		private decimal amount;
		private string who;
		private string description;
		private string reference;
		private string accountNumber;
		private Agents agent;
		private const string NO_DESCRIPTION = "N/A";
		public static string NO_SOURCE_NAME = "N/A";
        public static string NO_PROCESSOR_KEY = "-";
		public static string NO_DOMAIN = "N/A";
		public static int NO_SOURCE = 0;
        internal string SourceName { get; private set; }
		internal int ProcessorId { get; set; }
        internal string ProcessorKey { get; private set; }

        internal TransactionMessage(string atAddress, int sourceNumber, Coin coin, int storeId, string domain, decimal amount, string who, string description, string reference, string accountNumber, int processorId, string processorKey,
            Agents agent, string sourceName)
		{
			if (amount <= 0) throw new GameEngineException($"Amount {amount} must be bigger than 0.");
			if (String.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(accountNumber));
			if (String.IsNullOrWhiteSpace(atAddress)) throw new ArgumentNullException(nameof(atAddress));
			if (String.IsNullOrWhiteSpace(who)) throw new ArgumentNullException(nameof(who));
			if (String.IsNullOrWhiteSpace(reference)) throw new ArgumentNullException(nameof(reference));
			if (description == null) throw new ArgumentNullException(nameof(description));
			if (reference.Length > Movement.MAX_REFERENCE_LENGTH) throw new GameEngineException($"Reference {reference} its longer than 30 characters.");

			this.atAddress = atAddress;
			this.sourceNumber = sourceNumber;
			this.coin = coin;
			this.storeId = storeId;
			this.domain = domain;
			this.amount = amount;
			this.who = who;
			this.description = description;
			this.reference = reference;
			this.accountNumber = accountNumber;
			ProcessorId = processorId;
			ProcessorKey = processorKey;
			this.agent=agent;
			SourceName = sourceName;
		}

		public TransactionMessage(string serializedMessage):base(serializedMessage)
		{
			
		}

		protected override void Deserialize(string [] serializedMessage, out int fieldOrder)
		{
			if (serializedMessage.Length <= 0) throw new GameEngineException("Invalid format to make a deposit");

			base.Deserialize(serializedMessage, out fieldOrder);
			this.atAddress = serializedMessage[fieldOrder++];
			this.sourceNumber = int.Parse(serializedMessage[fieldOrder++]);
			this.coin = Coinage.KafkaProperty2Coin(serializedMessage[fieldOrder++]);
			this.storeId = int.Parse(serializedMessage[fieldOrder++]);
			decimal amount = decimal.Parse(serializedMessage[fieldOrder++]);
			this.amount = amount;
			this.who = serializedMessage[fieldOrder++];
			this.description = serializedMessage[fieldOrder++];
			this.reference = serializedMessage[fieldOrder++];
			this.accountNumber = serializedMessage[fieldOrder++];
			ProcessorId = int.Parse(serializedMessage[fieldOrder++]);
            ProcessorKey = serializedMessage[fieldOrder++];
            if (this.description == NO_DESCRIPTION) this.description = "";
			this.agent = (Agents)int.Parse(serializedMessage[fieldOrder++]);
			if (serializedMessage.Length > 10) SourceName = serializedMessage[fieldOrder++];
			if (serializedMessage.Length > 11) this.domain = serializedMessage[fieldOrder++];
		}

		protected override void InternalSerialize()
		{
			base.InternalSerialize();
			AddProperty(AtAddress)
			.AddProperty(SourceNumber)
			.AddProperty(Currency)
			.AddProperty(StoreId)
			.AddProperty(Amount)
			.AddProperty(Who)
			.AddProperty((string.IsNullOrEmpty(Description)) ? NO_DESCRIPTION : Description)
			.AddProperty(Reference)
			.AddProperty(AccountNumber)
			.AddProperty(ProcessorId)
			.AddProperty(string.IsNullOrWhiteSpace(ProcessorKey) ? NO_PROCESSOR_KEY : ProcessorKey)
			.AddProperty((int)Agent)
			.AddProperty(string.IsNullOrWhiteSpace(SourceName) ? NO_SOURCE_NAME : SourceName)
			.AddProperty(Domain);
		}

		public bool NotifyPlayer { get; set; } = false;

		public Agents Agent
		{
			get
			{
				return this.agent;
			}
		}
		public string AtAddress
		{
			get
			{
				return this.atAddress;
			}
		}

		public int SourceNumber
		{
			get
			{
				return this.sourceNumber;
			}
		}
		public Coin Coin
		{
			get
			{
				return this.coin;
			}
		}
		public string Currency
		{
			get
			{
				return this.coin.Iso4217Code;
			}
		}

		public int StoreId
		{
			get
			{
				return this.storeId;
			}
		}

        public string Domain
        {
            get
            {
                return this.domain;
            }
        }

        public decimal Amount
		{
			get
			{
				return this.amount;
			}
		}

		public string Who
		{
			get
			{
				return this.who;
			}
		}

		public string Description
		{
			get
			{
				return this.description;
			}
		}
		public string Reference
		{
			get
			{
				return this.reference;
			}
		}

		public string AccountNumber
		{
			get
			{
				return this.accountNumber;
			}
		}

		public bool HasSource()
		{
			return sourceNumber != NO_SOURCE;
		}
	}

	public sealed class DepositMessage : TransactionMessage
	{
		private string msg;

        public DepositMessage(string atAddress, int sourceNumber, Coin currency, int storeId, string domain, decimal amount, string who, string description, string reference, string accountNumber, int processorId, string processorKey, Agents agent, string sourceName)
            : base(atAddress, sourceNumber, currency, storeId, domain, amount, who, description, reference, accountNumber, processorId, processorKey, agent, sourceName)
        {
        }
        public DepositMessage(string atAddress, int sourceNumber, Coin currency, int storeId, decimal amount, string who, string description, string reference, string accountNumber, int processorId, string processorKey, Agents agent, string sourceName)
			: base(atAddress, sourceNumber, currency, storeId, NO_DOMAIN, amount, who, description, reference, accountNumber, processorId, processorKey, agent, sourceName)
		{
		}
		public DepositMessage(string atAddress, Coin currency, int storeId, decimal amount, string who, string description, string reference, string accountNumber, int processorId, string processorKey, Agents agent)
			: base(atAddress, NO_SOURCE, currency, storeId, NO_DOMAIN, amount, who, description, reference, accountNumber, processorId, processorKey, agent, NO_SOURCE_NAME)
		{
		}
        public DepositMessage(string atAddress, Coin currency, int storeId, string domain, decimal amount, string who, string description, string reference, int processorId, string processorKey, Agents agent)
            : base(atAddress, NO_SOURCE, currency, storeId, domain, amount, who, description, reference, currency.Iso4217Code.ToString(), processorId, processorKey, agent, NO_SOURCE_NAME)
        {
        }

        public DepositMessage(string msg)
			: base(msg)
		{
			this.msg = msg;
		}

        protected override void InternalSerialize()
        {
			base.InternalSerialize();
		}
    }

	public sealed class WithdrawMessage : TransactionMessage
	{
		private string msg;

		public WithdrawMessage(string msg)
			: base(msg)
		{
			this.msg = msg;
		}

        public WithdrawMessage(string atAddress, int sourceNumber, Coin currency, int storeId, string domain, decimal amount, string who, string description, string reference, string accountNumber, int processorId, string processorKey, Agents agent)
            : base(atAddress, sourceNumber, currency, storeId, domain, amount, who, description, reference, accountNumber, processorId, processorKey, agent, NO_SOURCE_NAME)
        {
        }
        public WithdrawMessage(string atAddress, int sourceNumber, Coin currency, int storeId, decimal amount, string who, string description, string reference, string accountNumber, int processorId, string processorKey, Agents agent)
			: base(atAddress, sourceNumber, currency, storeId, NO_DOMAIN, amount, who, description, reference, accountNumber, processorId, processorKey, agent, NO_SOURCE_NAME)
		{
		}
		public WithdrawMessage(string atAddress, Coin currency, int storeId, decimal amount, string who, string description, string reference, string accountNumber, int processorId, string processorKey, Agents agent)
			: base(atAddress, NO_SOURCE, currency, storeId, NO_DOMAIN, amount, who, description, reference, accountNumber, processorId, processorKey, agent, NO_SOURCE_NAME)
		{
		}

        public WithdrawMessage(string atAddress, Coin currency, int storeId, string domain, decimal amount, string who, string description, string reference, int processorId, string processorKey, Agents agent)
            : base(atAddress, NO_SOURCE, currency, storeId, domain, amount, who, description, reference, currency.Iso4217Code.ToString(), processorId, processorKey, agent, NO_SOURCE_NAME)
        {
        }

        protected override void InternalSerialize()
        {
			base.InternalSerialize();
		}
    }
}
