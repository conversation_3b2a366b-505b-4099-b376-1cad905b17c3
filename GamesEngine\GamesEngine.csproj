﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <SignAssembly>false</SignAssembly>
    <AssemblyOriginatorKeyFile>GamesEngine.snk</AssemblyOriginatorKeyFile>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="PaymentAuthorizators\**" />
    <EmbeddedResource Remove="PaymentAuthorizators\**" />
    <None Remove="PaymentAuthorizators\**" />
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="Properties\AssemblyInfo.cs" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="App.config" />
    <None Remove="lottohistoricalsettings.json" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="CassandraCSharpDriver" Version="3.22.0" />
    <PackageReference Include="ClickHouse.Client" Version="7.14.0" />
    <PackageReference Include="Confluent.Kafka" Version="2.8.0" />
    <PackageReference Include="Elastic.Apm" Version="1.31.0" />
    <PackageReference Include="Elastic.Clients.Elasticsearch" Version="8.17.1" />
    <PackageReference Include="ESAClient" Version="1.0.11" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Core" Version="2.3.0" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR.Client" Version="9.0.2" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.2" />
    <PackageReference Include="Microsoft.Extensions.Logging.Debug" Version="9.0.2" />
    <PackageReference Include="Microsoft.VisualStudio.Threading" Version="17.13.2" />
    <PackageReference Include="Microsoft.VisualStudio.Threading.Analyzers" Version="17.13.2">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="System.Collections.Concurrent" Version="4.3.0" />
    <PackageReference Include="System.Data.SqlClient" Version="4.9.0" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.6.0" />
  </ItemGroup>
  
  <ItemGroup>
    <ProjectReference Include="..\ExternalServices\ExternalServices.csproj" />
    <ProjectReference Include="..\Puppeteer\Puppeteer.csproj" />
  </ItemGroup>
  
  <ItemGroup>
    <Folder Include="Exchange\town.connectors.drivers\artemis\processors\" />
  </ItemGroup>

</Project>