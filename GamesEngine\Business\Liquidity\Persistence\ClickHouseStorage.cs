using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using ClickHouse.Client.ADO;
using ClickHouse.Client.ADO.Readers;
using ClickHouse.Client.ADO.Parameters;
using ClickHouse.Client.Copy;
using ClickHouse.Client.Utility; // For InsertAsync extension method
using GamesEngine.Settings;
using Puppeteer.EventSourcing;
using static GamesEngine.Business.Liquidity.Persistence.DataModel;

namespace GamesEngine.Business.Liquidity.Persistence
{
    public class ClickHouseStorage : IStorage
    {
        private readonly string _connectionString;

        private const string BaseDepositTableName = "deposit";
        private const string BaseJarTableName = "jar";
        private const string BaseTankTableName = "tank";
        private const string BaseTankerTableName = "tanker";
        private const string BaseJarDetailTableName = "jardetail";
        private const string BaseTankDetailTableName = "tankdetail";
        private const string BaseTankerDetailTableName = "tankerdetail";
        private const string BaseWithdrawalTableName = "withdrawal";
        private const string BaseBottleTableName = "bottle";
        private const string BaseDispenserTableName = "dispenser";
        private const string BaseBottleDetailTableName = "bottledetail";
        private const string BaseDispenserDetailTableName = "dispenserdetail";

        public ClickHouseStorage(string connectionString)
        {
            _connectionString = connectionString ?? throw new ArgumentNullException(nameof(connectionString));
        }

        private string GetDynamicTableName(string baseName, string kind)
        {
            if (string.IsNullOrWhiteSpace(kind)) throw new ArgumentException("Kind cannot be null or whitespace for dynamic table naming.", nameof(kind));

            var sanitizedKind = new string(kind.Where(char.IsLetterOrDigit).ToArray());
            if (string.IsNullOrWhiteSpace(sanitizedKind)) throw new ArgumentException("Sanitized kind results in an empty string.", nameof(kind));

            return $"`{baseName}_{sanitizedKind}`";
        }

        private async Task ExecuteNonQueryAsync(string sql, IEnumerable<ClickHouseDbParameter> parameters = null)
        {
            try
            {
                using (var connection = new ClickHouseConnection(_connectionString))
                {
                    await connection.OpenAsync();
                    using (var command = connection.CreateCommand())
                    {
                        command.CommandText = sql;
                        if (parameters != null)
                        {
                            foreach (var p in parameters)
                            {
                                command.Parameters.Add(p);
                            }
                        }
                        await command.ExecuteNonQueryAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                Loggers.GetIntance().Db.Error($"ClickHouse Error executing non-query: {sql}", ex);
                ErrorsSender.Send(ex, sql);
                throw;
            }
        }

        private async Task<T> ExecuteScalarAsync<T>(string sql, IEnumerable<ClickHouseDbParameter> parameters = null)
        {
            try
            {
                using (var connection = new ClickHouseConnection(_connectionString))
                {
                    await connection.OpenAsync();
                    using (var command = connection.CreateCommand())
                    {
                        command.CommandText = sql;
                        if (parameters != null)
                        {
                            foreach (var p in parameters)
                            {
                                command.Parameters.Add(p);
                            }
                        }
                        var result = await command.ExecuteScalarAsync();
                        if (result == null || result == DBNull.Value) return default(T);
                        return (T)Convert.ChangeType(result, typeof(T));
                    }
                }
            }
            catch (Exception ex)
            {
                Loggers.GetIntance().Db.Error($"ClickHouse Error executing scalar: {sql}", ex);
                ErrorsSender.Send(ex, sql);
                throw;
            }
        }

        // Synchronous wrappers for interface compatibility if strict async is not desired throughout.
        // Consider making the IStorage interface async if possible.
        private void ExecuteNonQuery(string sql, IEnumerable<ClickHouseDbParameter> parameters = null)
        {
            ExecuteNonQueryAsync(sql, parameters).GetAwaiter().GetResult();
        }

        private T ExecuteScalar<T>(string sql, IEnumerable<ClickHouseDbParameter> parameters = null)
        {
            return ExecuteScalarAsync<T>(sql, parameters).GetAwaiter().GetResult();
        }


        public void CreateTablesIfNotExists(string kind)
        {
            string depositTableName = GetDynamicTableName(BaseDepositTableName, kind);
            string createDepositTableSql = $@"
                CREATE TABLE IF NOT EXISTS {depositTableName} (
                    `Id` Int64,
                    `DocumentNumber` String,
                    `Amount` Decimal(16, 8),
                    `Date` DateTime64(3),
                    `Store` UInt8,
                    `AccountNumber` String,
                    `DomainId` Int32,
                    `Address` String,
                    `Created` DateTime64(3)
                ) ENGINE = MergeTree()
                ORDER BY Id;";
            ExecuteNonQuery(createDepositTableSql);

            string jarTableName = GetDynamicTableName(BaseJarTableName, kind);
            string createJarTableSql = $@"
                CREATE TABLE IF NOT EXISTS {jarTableName} (
                    `Version` Int64,
                    `Description` Nullable(String),
                    `Created` DateTime64(3)
                ) ENGINE = MergeTree()
                ORDER BY Version;";
            ExecuteNonQuery(createJarTableSql);

            string tankTableName = GetDynamicTableName(BaseTankTableName, kind);
            string createTankTableSql = $@"
                CREATE TABLE IF NOT EXISTS {tankTableName} (
                    `Id` Int64,
                    `Description` Nullable(String),
                    `Created` DateTime64(3)
                ) ENGINE = MergeTree()
                ORDER BY Id;";
            ExecuteNonQuery(createTankTableSql);

            string tankerTableName = GetDynamicTableName(BaseTankerTableName, kind);
            string createTankerTableSql = $@"
                CREATE TABLE IF NOT EXISTS {tankerTableName} (
                    `Id` Int64,
                    `Description` Nullable(String),
                    `Created` DateTime64(3)
                ) ENGINE = MergeTree()
                ORDER BY Id;";
            ExecuteNonQuery(createTankerTableSql);

            string jarDetailTableName = GetDynamicTableName(BaseJarDetailTableName, kind);
            string createJarDetailTableSql = $@"
                CREATE TABLE IF NOT EXISTS {jarDetailTableName} (
                    `JarVersion` Int64,
                    `DepositId` Int64, 
                    `Created` DateTime64(3)
                ) ENGINE = MergeTree()
                ORDER BY (JarVersion, DepositId);";
            ExecuteNonQuery(createJarDetailTableSql);

            string tankDetailTableName = GetDynamicTableName(BaseTankDetailTableName, kind);
            string createTankDetailTableSql = $@"
                CREATE TABLE IF NOT EXISTS {tankDetailTableName} (
                    `TankId` Int64,
                    `DepositId` Int64,
                    `Created` DateTime64(3)
                ) ENGINE = MergeTree()
                ORDER BY (TankId, DepositId);";
            ExecuteNonQuery(createTankDetailTableSql);

            string tankerDetailTableName = GetDynamicTableName(BaseTankerDetailTableName, kind);
            string createTankerDetailTableSql = $@"
                CREATE TABLE IF NOT EXISTS {tankerDetailTableName} (
                    `TankerId` Int64,
                    `DepositId` Int64,
                    `Created` DateTime64(3)
                ) ENGINE = MergeTree()
                ORDER BY (TankerId, DepositId);";
            ExecuteNonQuery(createTankerDetailTableSql);

            string withdrawalTableName = GetDynamicTableName(BaseWithdrawalTableName, kind);
            string createWithdrawalTableSql = $@"
                CREATE TABLE IF NOT EXISTS {withdrawalTableName} (
                    `Id` Int64,
                    `DocumentNumber` String,
                    `Amount` Decimal(16, 8),
                    `Date` DateTime64(3),
                    `Store` UInt8,
                    `AccountNumber` String,
                    `DomainId` Int32,
                    `Address` String,
                    `Created` DateTime64(3)
                ) ENGINE = MergeTree()
                ORDER BY Id;";
            ExecuteNonQuery(createWithdrawalTableSql);

            string bottleTableName = GetDynamicTableName(BaseBottleTableName, kind);
            string createBottleTableSql = $@"
                CREATE TABLE IF NOT EXISTS {bottleTableName} (
                    `Id` Int64,
                    `Description` Nullable(String),
                    `Created` DateTime64(3)
                ) ENGINE = MergeTree()
                ORDER BY Id;";
            ExecuteNonQuery(createBottleTableSql);

            string dispenserTableName = GetDynamicTableName(BaseDispenserTableName, kind);
            string createDispenserTableSql = $@"
                CREATE TABLE IF NOT EXISTS {dispenserTableName} (
                    `Id` Int64,
                    `Description` Nullable(String),
                    `Created` DateTime64(3)
                ) ENGINE = MergeTree()
                ORDER BY Id;";
            ExecuteNonQuery(createDispenserTableSql);

            string bottleDetailTableName = GetDynamicTableName(BaseBottleDetailTableName, kind);
            string createBottleDetailTableSql = $@"
                CREATE TABLE IF NOT EXISTS {bottleDetailTableName} (
                    `WithdrawalId` Int64,
                    `BottleId` Int64,
                    `Created` DateTime64(3)
                ) ENGINE = MergeTree()
                ORDER BY (WithdrawalId, BottleId);";
            ExecuteNonQuery(createBottleDetailTableSql);

            string dispenserDetailTableName = GetDynamicTableName(BaseDispenserDetailTableName, kind);
            string createDispenserDetailTableSql = $@"
                CREATE TABLE IF NOT EXISTS {dispenserDetailTableName} (
                    `WithdrawalId` Int64,
                    `DispenserId` Int64,
                    `Created` DateTime64(3)
                ) ENGINE = MergeTree()
                ORDER BY (WithdrawalId, DispenserId);";
            ExecuteNonQuery(createDispenserDetailTableSql);
        }

        public void CreateDeposit(string kind, Deposit depositDetails)
        {
            string depositTableName = GetDynamicTableName(BaseDepositTableName, kind);
            string insertSql = $@"
                INSERT INTO {depositTableName}
                (`Id`, `DocumentNumber`, `Amount`, `Date`, `Store`, `AccountNumber`, `DomainId`, `Address`, `Created`)
                VALUES
                (@Id, @DocumentNumber, @Amount, @Date, @Store, @AccountNumber, @DomainId, @Address, @Created);";

            var parameters = new List<ClickHouseDbParameter>
            {
                new ClickHouseDbParameter { ParameterName = "Id", Value = depositDetails.Id, DbType = DbType.Int64 },
                new ClickHouseDbParameter { ParameterName = "DocumentNumber", Value = depositDetails.DocumentNumber, DbType = DbType.String },
                new ClickHouseDbParameter { ParameterName = "Amount", Value = depositDetails.Amount, DbType = DbType.Decimal },
                new ClickHouseDbParameter { ParameterName = "Date", Value = depositDetails.Date, DbType = DbType.DateTime }, 
                new ClickHouseDbParameter { ParameterName = "Store", Value = depositDetails.StoreId, DbType = DbType.Byte },
                new ClickHouseDbParameter { ParameterName = "AccountNumber", Value = depositDetails.AccountNumber, DbType = DbType.String },
                new ClickHouseDbParameter { ParameterName = "DomainId", Value = depositDetails.DomainId, DbType = DbType.Int32 },
                new ClickHouseDbParameter { ParameterName = "Address", Value = depositDetails.Address, DbType = DbType.String },
                new ClickHouseDbParameter { ParameterName = "Created", Value = depositDetails.Created, DbType = DbType.DateTime } 
            };
            ExecuteNonQuery(insertSql, parameters);
        }

        public void CreateJar(string kind, long version, string description, DateTime created)
        {
            string jarTableName = GetDynamicTableName(BaseJarTableName, kind);
            string insertSql = $"INSERT INTO {jarTableName} (`Version`, `Description`, `Created`) VALUES (@Version, @Description, @Created);";
            var parameters = new List<ClickHouseDbParameter>
            {
                new ClickHouseDbParameter { ParameterName = "Version", Value = version, DbType = DbType.Int64 },
                new ClickHouseDbParameter { ParameterName = "Description", Value = (object)description ?? DBNull.Value, DbType = DbType.String },
                new ClickHouseDbParameter { ParameterName = "Created", Value = created, DbType = DbType.DateTime }
            };
            ExecuteNonQuery(insertSql, parameters);
        }

        public void CreateTank(string kind, Tank tank)
        {
            string tankTableName = GetDynamicTableName(BaseTankTableName, kind);
            string insertSql = $"INSERT INTO {tankTableName} (`Id`, `Description`, `Created`) VALUES (@Id, @Description, @Created);";
            var parameters = new List<ClickHouseDbParameter>
            {
                new ClickHouseDbParameter { ParameterName = "Id", Value = tank.Id, DbType = DbType.Int64 },
                new ClickHouseDbParameter { ParameterName = "Description", Value = (object)tank.Description ?? DBNull.Value, DbType = DbType.String },
                new ClickHouseDbParameter { ParameterName = "Created", Value = tank.Created, DbType = DbType.DateTime }
            };
            ExecuteNonQuery(insertSql, parameters);
        }

        public void CreateTanker(string kind, Tanker tanker)
        {
            string tankerTableName = GetDynamicTableName(BaseTankerTableName, kind);
            string insertSql = $"INSERT INTO {tankerTableName} (`Id`, `Description`, `Created`) VALUES (@Id, @Description, @Created);";
            var parameters = new List<ClickHouseDbParameter>
            {
                new ClickHouseDbParameter { ParameterName = "Id", Value = tanker.Id, DbType = DbType.Int64 },
                new ClickHouseDbParameter { ParameterName = "Description", Value = (object)tanker.Description ?? DBNull.Value, DbType = DbType.String },
                new ClickHouseDbParameter { ParameterName = "Created", Value = tanker.Created, DbType = DbType.DateTime }
            };
            ExecuteNonQuery(insertSql, parameters);
        }

        private void CreateDetailIfNotExistsGeneric(string tableName, string key1Name, long key1Value, string key2Name, long key2Value, DateTime created, string kindForLog, string entityTypeForLog)
        {
            string selectSql = $"SELECT count() FROM {tableName} WHERE `{key1Name}` = @Key1Value AND `{key2Name}` = @Key2Value;";
            var selectParams = new List<ClickHouseDbParameter>
            {
                new ClickHouseDbParameter { ParameterName = "Key1Value", Value = key1Value, DbType = DbType.Int64 },
                new ClickHouseDbParameter { ParameterName = "Key2Value", Value = key2Value, DbType = DbType.Int64 }
            };

            var count = ExecuteScalar<long>(selectSql, selectParams);

            if (count == 0)
            {
                string insertSql = $"INSERT INTO {tableName} (`{key1Name}`, `{key2Name}`, `Created`) VALUES (@Key1Value, @Key2Value, @Created);";
                var insertParams = new List<ClickHouseDbParameter>
                {
                    new ClickHouseDbParameter { ParameterName = "Key1Value", Value = key1Value, DbType = DbType.Int64 },
                    new ClickHouseDbParameter { ParameterName = "Key2Value", Value = key2Value, DbType = DbType.Int64 },
                    new ClickHouseDbParameter { ParameterName = "Created", Value = created, DbType = DbType.DateTime }
                };
                try
                {
                    ExecuteNonQuery(insertSql, insertParams);
                }
                catch (Exception ex)
                {
                    Loggers.GetIntance().Db.Error($"ClickHouse Error in Create{entityTypeForLog}DetailIfNotExists (Race Condition?) ({key1Name}: {key1Value}, {key2Name}: {key2Value}): {ex.Message}", ex);
                    ErrorsSender.Send(ex, $"Create{entityTypeForLog}DetailIfNotExists ({key1Name}: {key1Value}, {key2Name}: {key2Value})");
                    // Optionally re-check or throw, depending on desired behavior for race conditions
                    // For now, re-throwing:
                    throw;
                }
            }
        }

        public void CreateJarDetailIfNotExists(string kind, long jarVersion, long depositId, DateTime created)
        {
            string jarDetailTableName = GetDynamicTableName(BaseJarDetailTableName, kind);
            CreateDetailIfNotExistsGeneric(jarDetailTableName, "JarVersion", jarVersion, "DepositId", depositId, created, kind, "Jar");
        }

        public void CreateTankDetailIfNotExists(string kind, long tankId, long depositId, DateTime created)
        {
            string tankDetailTableName = GetDynamicTableName(BaseTankDetailTableName, kind);
            CreateDetailIfNotExistsGeneric(tankDetailTableName, "TankId", tankId, "DepositId", depositId, created, kind, "Tank");
        }

        private class TankDetailPoco
        {
            public long TankId { get; set; }
            public long DepositId { get; set; } 
            public DateTime Created { get; set; }
        }

        public void CreateTankDetails(string kind, long tankId, IEnumerable<int> depositIds, DateTime created)
        {
            if (depositIds == null || !depositIds.Any())
            {
                return;
            }

            string tankDetailTableName = GetDynamicTableName(BaseTankDetailTableName, kind);

            var pocos = depositIds.Select(depositId => new TankDetailPoco
            {
                TankId = tankId,
                DepositId = depositId, // depositIds is IEnumerable<int>, POCO property is long
                Created = created
            }).ToList();

            if (!pocos.Any()) return;

            try
            {
                using (var connection = new ClickHouseConnection(_connectionString))
                {
                    connection.Open();

                    // These are the names of the properties from TankDetailPoco that EnumerableDataReader will expose as fields.
                    // These names will be used as the "column names" from the IDataReader perspective.
                    // ClickHouseBulkCopy will then try to map these to the DestinationTableName's columns.
                    // For this to work seamlessly, TankDetailPoco property names should match ClickHouse table column names.
                    var pocoPropertyNames = new List<string> { "TankId", "DepositId", "Created" };

                    using (var dataReader = new EnumerableDataReader<TankDetailPoco>(pocos, pocoPropertyNames))
                    {
                        using (var bulkCopy = new ClickHouseBulkCopy(connection)
                        {
                            DestinationTableName = tankDetailTableName,
                            BatchSize = 10000 // Optional
                            // If pocoPropertyNames are different from actual ClickHouse column names,
                            // explicit ColumnMappings would be needed here:
                            // e.g., bulkCopy.ColumnMappings.Add(new ClickHouseBulkCopyColumnMapping("TankId", "Actual_CH_TankId_Column_Name"));
                        })
                        {
                            // WriteToServerAsync expects an IDataReader. EnumerableDataReader is one.
                            bulkCopy.WriteToServerAsync(dataReader).GetAwaiter().GetResult();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Loggers.GetIntance().Db.Error($"ClickHouse Error in batch inserting TankDetails using EnumerableDataReader (TankId: {tankId}): {ex.Message}. POCO count: {pocos.Count}", ex);
                ErrorsSender.Send(ex, $"{nameof(CreateTankDetails)} batch insert EnumerableDataReader (TankId: {tankId})");
                throw;
            }
        }

        public void CreateTankerDetailIfNotExists(string kind, long tankerId, long depositId, DateTime created)
        {
            string tankerDetailTableName = GetDynamicTableName(BaseTankerDetailTableName, kind);
            CreateDetailIfNotExistsGeneric(tankerDetailTableName, "TankerId", tankerId, "DepositId", depositId, created, kind, "Tanker");
        }

        public void CreateWithdrawal(string kind, Withdrawal w)
        {
            string withdrawalTableName = GetDynamicTableName(BaseWithdrawalTableName, kind);
            string insertSql = $@"
                INSERT INTO {withdrawalTableName}
                (`Id`, `DocumentNumber`, `Amount`, `Date`, `Store`, `AccountNumber`, `DomainId`, `Address`, `Created`)
                VALUES
                (@Id, @DocumentNumber, @Amount, @Date, @Store, @AccountNumber, @DomainId, @Address, @Created);
            ";

            var parameters = new List<ClickHouseDbParameter>
            {
                new ClickHouseDbParameter { ParameterName = "Id", Value = w.Id, DbType = DbType.Int64 },
                new ClickHouseDbParameter { ParameterName = "DocumentNumber", Value = w.DocumentNumber, DbType = DbType.String },
                new ClickHouseDbParameter { ParameterName = "Amount", Value = w.Amount, DbType = DbType.Decimal },
                new ClickHouseDbParameter { ParameterName = "Date", Value = w.Date, DbType = DbType.DateTime },
                new ClickHouseDbParameter { ParameterName = "Store", Value = w.StoreId, DbType = DbType.Byte },
                new ClickHouseDbParameter { ParameterName = "AccountNumber", Value = w.AccountNumber, DbType = DbType.String },
                new ClickHouseDbParameter { ParameterName = "DomainId", Value = w.DomainId, DbType = DbType.Int32 },
                new ClickHouseDbParameter { ParameterName = "Address", Value = w.Address, DbType = DbType.String },
                new ClickHouseDbParameter { ParameterName = "Created", Value = w.Created, DbType = DbType.DateTime }
            };
            ExecuteNonQuery(insertSql, parameters);
        }

        public void CreateBottle(string kind, Bottle b)
        {
            string bottleTableName = GetDynamicTableName(BaseBottleTableName, kind);
            string insertSql = $"INSERT INTO {bottleTableName} (`Id`, `Description`, `Created`) VALUES (@Id, @Description, @Created);";
            var parameters = new List<ClickHouseDbParameter>
            {
                new ClickHouseDbParameter { ParameterName = "Id", Value = b.Id, DbType = DbType.Int64 },
                new ClickHouseDbParameter { ParameterName = "Description", Value = (object)b.Description ?? DBNull.Value, DbType = DbType.String },
                new ClickHouseDbParameter { ParameterName = "Created", Value = b.Created, DbType = DbType.DateTime }
            };
            ExecuteNonQuery(insertSql, parameters);
        }

        public void CreateDispenser(string kind, Dispenser d)
        {
            string dispenserTableName = GetDynamicTableName(BaseDispenserTableName, kind);
            string insertSql = $"INSERT INTO {dispenserTableName} (`Id`, `Description`, `Created`) VALUES (@Id, @Description, @Created);";
            var parameters = new List<ClickHouseDbParameter>
            {
                new ClickHouseDbParameter { ParameterName = "Id", Value = d.Id, DbType = DbType.Int64 },
                new ClickHouseDbParameter { ParameterName = "Description", Value = (object)d.Description ?? DBNull.Value, DbType = DbType.String },
                new ClickHouseDbParameter { ParameterName = "Created", Value = d.Created, DbType = DbType.DateTime }
            };
            ExecuteNonQuery(insertSql, parameters);
        }

        public void CreateBottleDetailIfNotExists(string kind, long withdrawalId, long bottleId, DateTime created)
        {
            string bottleDetailTableName = GetDynamicTableName(BaseBottleDetailTableName, kind);
            CreateDetailIfNotExistsGeneric(bottleDetailTableName, "WithdrawalId", withdrawalId, "BottleId", bottleId, created, kind, "Bottle");
        }

        public void CreateDispenserDetailIfNotExists(string kind, long withdrawalId, long dispenserId, DateTime created)
        {
            string dispenserDetailTableName = GetDynamicTableName(BaseDispenserDetailTableName, kind);
            CreateDetailIfNotExistsGeneric(dispenserDetailTableName, "WithdrawalId", withdrawalId, "DispenserId", dispenserId, created, kind, "Dispenser");
        }

        // READ methods
        public List<Deposit> DepositsInNewestJar(string kind, DateTime startDate, DateTime endDate, string accountNumber = null)
        {
            var deposits = new List<Deposit>();

            string jarTableName = GetDynamicTableName(BaseJarTableName, kind);
            string depositTableName = GetDynamicTableName(BaseDepositTableName, kind);
            string jarDetailTableName = GetDynamicTableName(BaseJarDetailTableName, kind);

            string getNewestJarVersionSql = $"SELECT Version FROM {jarTableName} ORDER BY Version DESC, Created DESC LIMIT 1;";
            long? newestJarVersion = null;
            try
            {
                newestJarVersion = ExecuteScalar<long?>(getNewestJarVersionSql);
            }
            catch (Exception ex)
            {
                Loggers.GetIntance().Db.Error($"ClickHouse Error getting newest Jar version: {getNewestJarVersionSql}", ex);
                ErrorsSender.Send(ex, $"{nameof(DepositsInNewestJar)} - getNewestJarVersion");
                throw;
            }

            if (!newestJarVersion.HasValue)
            {
                Loggers.GetIntance().Db.Debug("No Jars found, cannot retrieve deposits for the newest jar by version.");
                return deposits;
            }

            var selectDepositsSqlBuilder = new StringBuilder($@"
                SELECT
                    d.Id, d.DocumentNumber, d.Amount, d.Date, d.Store, d.AccountNumber, d.DomainId, d.Address, d.Created
                FROM
                    {depositTableName} d
                INNER JOIN
                    {jarDetailTableName} jd ON d.Id = jd.DepositId
                WHERE
                    jd.JarVersion = @NewestJarVersion
                    AND d.Date >= @StartDate AND d.Date <= @EndDate
            ");

            var queryParams = new List<ClickHouseDbParameter>
            {
                new ClickHouseDbParameter { ParameterName = "NewestJarVersion", Value = newestJarVersion.Value, DbType = DbType.Int64 },
                new ClickHouseDbParameter { ParameterName = "StartDate", Value = startDate, DbType = DbType.DateTime },
                new ClickHouseDbParameter { ParameterName = "EndDate", Value = endDate, DbType = DbType.DateTime }
            };

            if (!string.IsNullOrEmpty(accountNumber))
            {
                selectDepositsSqlBuilder.Append(" AND d.AccountNumber = @AccountNumber");
                queryParams.Add(new ClickHouseDbParameter { ParameterName = "AccountNumber", Value = accountNumber, DbType = DbType.String });
            }
            selectDepositsSqlBuilder.Append(";");
            string selectDepositsSql = selectDepositsSqlBuilder.ToString();

            try
            {
                using (var connection = new ClickHouseConnection(_connectionString))
                {
                    connection.Open(); // Sync open
                    using (var command = connection.CreateCommand())
                    {
                        command.CommandText = selectDepositsSql;
                        foreach (var p in queryParams) command.Parameters.Add(p);

                        using (var reader = command.ExecuteReader()) // Sync execute
                        {
                            while (reader.Read()) // Sync read
                            {
                                deposits.Add(MapReaderToDeposit(reader));
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                string accNumContext = string.IsNullOrEmpty(accountNumber) ? "null" : accountNumber;
                string errorContext = $"newestJarVersion: {newestJarVersion.Value}";
                Loggers.GetIntance().Db.Error($"ClickHouse Error in {nameof(DepositsInNewestJar)} ({errorContext}, startDate: {startDate}, endDate: {endDate}, accountNumber: {accNumContext}): {ex.Message}", ex);
                ErrorsSender.Send(ex, $"{nameof(DepositsInNewestJar)}", $"newestJarVersion {newestJarVersion}, startDate {startDate}, endDate {endDate}, accountNumber {accNumContext}");
                throw;
            }
            return deposits;
        }

        private Deposit MapReaderToDeposit(IDataReader reader)
        {
            return new Deposit
            {
                Id = reader.GetInt64(reader.GetOrdinal("Id")),
                DocumentNumber = reader.GetString(reader.GetOrdinal("DocumentNumber")),
                Amount = reader.GetDecimal(reader.GetOrdinal("Amount")),
                Date = reader.GetDateTime(reader.GetOrdinal("Date")),
                StoreId = reader.GetByte(reader.GetOrdinal("Store")),
                AccountNumber = reader.GetString(reader.GetOrdinal("AccountNumber")),
                DomainId = reader.GetInt32(reader.GetOrdinal("DomainId")),
                Address = reader.GetString(reader.GetOrdinal("Address")),
                Created = reader.GetDateTime(reader.GetOrdinal("Created"))
            };
        }

        private Withdrawal MapReaderToWithdrawal(IDataReader reader)
        {
            return new Withdrawal
            {
                Id = reader.GetInt64(reader.GetOrdinal("WithdrawalId")),
                DocumentNumber = reader.GetString(reader.GetOrdinal("DocumentNumber")),
                Amount = reader.GetDecimal(reader.GetOrdinal("Amount")),
                Date = reader.GetDateTime(reader.GetOrdinal("Date")),
                StoreId = reader.GetByte(reader.GetOrdinal("Store")),
                AccountNumber = reader.GetString(reader.GetOrdinal("AccountNumber")),
                DomainId = reader.GetInt32(reader.GetOrdinal("DomainId")),
                Address = reader.GetString(reader.GetOrdinal("Address")),
                Created = reader.GetDateTime(reader.GetOrdinal("WithdrawalCreated"))
            };
        }

        public TankWithDeposits TankAndAllItsDeposits(string kind, long tankId)
        {
            var result = new TankWithDeposits();
            bool tankInfoSet = false;

            string tankTableName = GetDynamicTableName(BaseTankTableName, kind);
            string tankDetailTableName = GetDynamicTableName(BaseTankDetailTableName, kind);
            string depositTableName = GetDynamicTableName(BaseDepositTableName, kind);

            string selectSql = $@"
                SELECT
                    t.Id AS TankId, t.Description AS TankDescription, t.Created AS TankCreated,
                    d.Id, d.DocumentNumber, d.Amount, d.Date, d.Store, 
                    d.AccountNumber, d.DomainId, d.Address, d.Created AS DepositCreated
                FROM
                    {tankTableName} t
                LEFT JOIN
                    {tankDetailTableName} td ON t.Id = td.TankId
                LEFT JOIN
                    {depositTableName} d ON td.DepositId = d.Id
                WHERE
                    t.Id = @TankId;
            ";
            var queryParams = new List<ClickHouseDbParameter> { new ClickHouseDbParameter { ParameterName = "TankId", Value = tankId, DbType = DbType.Int64 } };

            try
            {
                using (var connection = new ClickHouseConnection(_connectionString))
                {
                    connection.Open();
                    using (var command = connection.CreateCommand())
                    {
                        command.CommandText = selectSql;
                        foreach (var p in queryParams) command.Parameters.Add(p);

                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                if (!tankInfoSet)
                                {
                                    result.TankInfo = new Tank
                                    {
                                        Id = reader.GetInt64(reader.GetOrdinal("TankId")),
                                        Description = reader.IsDBNull(reader.GetOrdinal("TankDescription")) ? null : reader.GetString(reader.GetOrdinal("TankDescription")),
                                        Created = reader.GetDateTime(reader.GetOrdinal("TankCreated"))
                                    };
                                    tankInfoSet = true;
                                }
                                if (!reader.IsDBNull(reader.GetOrdinal("Id")))
                                {
                                    result.Deposits.Add(new Deposit // Manually map here or use MapReaderToDeposit if column names match
                                    {
                                        Id = reader.GetInt64(reader.GetOrdinal("Id")),
                                        DocumentNumber = reader.GetString(reader.GetOrdinal("DocumentNumber")),
                                        Amount = reader.GetDecimal(reader.GetOrdinal("Amount")),
                                        Date = reader.GetDateTime(reader.GetOrdinal("Date")),
                                        StoreId = reader.GetByte(reader.GetOrdinal("Store")),
                                        AccountNumber = reader.GetString(reader.GetOrdinal("AccountNumber")),
                                        DomainId = reader.GetInt32(reader.GetOrdinal("DomainId")),
                                        Address = reader.GetString(reader.GetOrdinal("Address")),
                                        Created = reader.GetDateTime(reader.GetOrdinal("DepositCreated"))
                                    });
                                }
                            }
                        }
                    }
                }
                if (result.TankInfo == null) return null;
                return result;
            }
            catch (Exception ex)
            {
                Loggers.GetIntance().Db.Error($"ClickHouse Error in {nameof(TankAndAllItsDeposits)} (TankId: {tankId}): {ex.Message}", ex);
                ErrorsSender.Send(ex, $"{nameof(TankAndAllItsDeposits)}", $"TankId {tankId}");
                throw;
            }
        }

        public TankerWithDeposits TankerAndAllItsDeposits(string kind, long tankerId)
        {
            var result = new TankerWithDeposits();
            bool tankerInfoSet = false;

            string tankerTableName = GetDynamicTableName(BaseTankerTableName, kind);
            string tankerDetailTableName = GetDynamicTableName(BaseTankerDetailTableName, kind);
            string depositTableName = GetDynamicTableName(BaseDepositTableName, kind);

            string selectSql = $@"
                SELECT
                    t.Id AS TankerId, t.Description AS TankerDescription, t.Created AS TankerCreated,
                    d.Id, d.DocumentNumber, d.Amount, d.Date, d.Store, 
                    d.AccountNumber, d.DomainId, d.Address, d.Created AS DepositCreated
                FROM
                    {tankerTableName} t
                LEFT JOIN
                    {tankerDetailTableName} td ON t.Id = td.TankerId
                LEFT JOIN
                    {depositTableName} d ON td.DepositId = d.Id
                WHERE
                    t.Id = @TankerId;
            ";
            var queryParams = new List<ClickHouseDbParameter> { new ClickHouseDbParameter { ParameterName = "TankerId", Value = tankerId, DbType = DbType.Int64 } };

            try
            {
                using (var connection = new ClickHouseConnection(_connectionString))
                {
                    connection.Open();
                    using (var command = connection.CreateCommand())
                    {
                        command.CommandText = selectSql;
                        foreach (var p in queryParams) command.Parameters.Add(p);

                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                if (!tankerInfoSet)
                                {
                                    result.TankerInfo = new Tanker
                                    {
                                        Id = reader.GetInt64(reader.GetOrdinal("TankerId")),
                                        Description = reader.IsDBNull(reader.GetOrdinal("TankerDescription")) ? null : reader.GetString(reader.GetOrdinal("TankerDescription")),
                                        Created = reader.GetDateTime(reader.GetOrdinal("TankerCreated"))
                                    };
                                    tankerInfoSet = true;
                                }
                                if (!reader.IsDBNull(reader.GetOrdinal("Id")))
                                {
                                    result.Deposits.Add(new Deposit // Manual map or helper
                                    {
                                        Id = reader.GetInt64(reader.GetOrdinal("Id")),
                                        DocumentNumber = reader.GetString(reader.GetOrdinal("DocumentNumber")),
                                        Amount = reader.GetDecimal(reader.GetOrdinal("Amount")),
                                        Date = reader.GetDateTime(reader.GetOrdinal("Date")),
                                        StoreId = reader.GetByte(reader.GetOrdinal("Store")),
                                        AccountNumber = reader.GetString(reader.GetOrdinal("AccountNumber")),
                                        DomainId = reader.GetInt32(reader.GetOrdinal("DomainId")),
                                        Address = reader.GetString(reader.GetOrdinal("Address")),
                                        Created = reader.GetDateTime(reader.GetOrdinal("DepositCreated"))
                                    });
                                }
                            }
                        }
                    }
                }
                if (result.TankerInfo == null) return null;
                return result;
            }
            catch (Exception ex)
            {
                Loggers.GetIntance().Db.Error($"ClickHouse Error in {nameof(TankerAndAllItsDeposits)} (TankerId: {tankerId}): {ex.Message}", ex);
                ErrorsSender.Send(ex, $"{nameof(TankerAndAllItsDeposits)}", $"TankerId {tankerId}");
                throw;
            }
        }

        public DispenserWithWithdrawals DispenserAndAllItsWithdrawals(string kind, long dispenserId)
        {
            var result = new DispenserWithWithdrawals();
            bool dispenserInfoSet = false;

            string dispenserTableName = GetDynamicTableName(BaseDispenserTableName, kind);
            string dispenserDetailTableName = GetDynamicTableName(BaseDispenserDetailTableName, kind);
            string withdrawalTableName = GetDynamicTableName(BaseWithdrawalTableName, kind);

            string selectSql = $@"
                SELECT
                    disp.Id AS DispenserId, disp.Description AS DispenserDescription, disp.Created AS DispenserCreated,
                    w.Id AS WithdrawalId, w.DocumentNumber, w.Amount, w.Date, w.Store,
                    w.AccountNumber, w.DomainId, w.Address, w.Created AS WithdrawalCreated
                FROM
                    {dispenserTableName} disp
                LEFT JOIN
                    {dispenserDetailTableName} dd ON disp.Id = dd.DispenserId
                LEFT JOIN
                    {withdrawalTableName} w ON dd.WithdrawalId = w.Id
                WHERE
                    disp.Id = @DispenserId;
            ";
            var queryParams = new List<ClickHouseDbParameter> { new ClickHouseDbParameter { ParameterName = "DispenserId", Value = dispenserId, DbType = DbType.Int64 } };

            try
            {
                using (var connection = new ClickHouseConnection(_connectionString))
                {
                    connection.Open();
                    using (var command = connection.CreateCommand())
                    {
                        command.CommandText = selectSql;
                        foreach (var p in queryParams) command.Parameters.Add(p);

                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                if (!dispenserInfoSet)
                                {
                                    result.DispenserInfo = new Dispenser
                                    {
                                        Id = reader.GetInt64(reader.GetOrdinal("DispenserId")),
                                        Description = reader.IsDBNull(reader.GetOrdinal("DispenserDescription")) ? null : reader.GetString(reader.GetOrdinal("DispenserDescription")),
                                        Created = reader.GetDateTime(reader.GetOrdinal("DispenserCreated"))
                                    };
                                    dispenserInfoSet = true;
                                }
                                if (!reader.IsDBNull(reader.GetOrdinal("WithdrawalId")))
                                {
                                    result.Withdrawals.Add(MapReaderToWithdrawal(reader));
                                }
                            }
                        }
                    }
                }
                if (result.DispenserInfo == null || result.DispenserInfo.Id == 0 && !dispenserInfoSet) return null;
                return result;
            }
            catch (Exception ex)
            {
                Loggers.GetIntance().Db.Error($"ClickHouse Error in {nameof(DispenserAndAllItsWithdrawals)} (DispenserId: {dispenserId}): {ex.Message}", ex);
                ErrorsSender.Send(ex, $"{nameof(DispenserAndAllItsWithdrawals)}", $"DispenserId {dispenserId}");
                throw;
            }
        }
    }
}