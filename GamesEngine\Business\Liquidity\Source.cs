﻿using GamesEngine.Business.Liquidity.Containers;
using GamesEngine.Business.Liquidity.Transactions;
using GamesEngine.Finance;
using GamesEngine.RealTime;
using GamesEngine.RealTime.Events;
using GamesEngine.Settings;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using static GamesEngine.Business.Liquidity.Containers.Tank;

namespace GamesEngine.Business.Liquidity
{
    public class Source : Objeto
    {
        public string Kind { get; private set; } // Tipo de líquido/moneda
        public decimal Amount { get; private set; } // Suma de UTXOs disponibles
        public Jar Jar { get; private set; } // Jar activo único
        private readonly List<Xpub> _xpubs = new List<Xpub>();
        
        private readonly Dictionary<int, Tanker> _tankers = new Dictionary<int, Tanker>();
        private readonly Dictionary<int, Tank> _tanks = new Dictionary<int, Tank>();

        public Source(Liquid liquid)
        {
            if (liquid == null) throw new ArgumentNullException(nameof(liquid));

            Kind = liquid.Kind;
            int vesion = NextJarVersion();
            Jar = new Jar(this, liquid.Kind, vesion);
            CurrentVesion = vesion;
            Amount = 0;
        }

        internal Liquid Liquid { get; private set; }

        internal IEnumerable<Tank> Tanks => _tanks.Values;

        internal IEnumerable<Tanker> Tankers => _tankers.Values;

        public bool ExsitXpub(string xpub)
        {
            if (xpub == null) throw new ArgumentNullException(nameof(xpub));
            return _xpubs.Any(s => s.Value == xpub);
        }

        public void AddXpub(Xpub xpub)
        {
            if (xpub == null) throw new ArgumentNullException(nameof(xpub));

            _xpubs.Add(xpub);
        }

        public IEnumerable<Xpub> Xpubs => _xpubs.AsReadOnly();

        public PendingDeposit RequestDeposit(bool itIsThePresent, DateTime now, int depositId)
        {
            if (now == DateTime.MinValue) throw new ArgumentNullException(nameof(now), "Date cannot be empty.");
            if(depositId <= 0) throw new ArgumentException("Deposit ID must be greater than zero.", nameof(depositId));

            var address = GenerateNewAddress();
            var result = Jar.RequestDeposit(itIsThePresent, now, depositId, address);
            depositConsecutive = result.Id;
            return result;
        }

        private string GenerateNewAddress()
        {
            if (!_xpubs.Any())
                throw new InvalidOperationException("No XPUBs available to derive addresses.");

            // Ejemplo sencillo: usar el primer XPUB disponible para generar una dirección
            return _xpubs.First().GenerateAddress();
        }

        public void ConfirmDeposit(bool itIsThePresent, DateTime createdAt, PendingDeposit pendingDeposit, string txid, decimal amount, int domainId)
        {
            if (pendingDeposit == null) throw new ArgumentNullException(nameof(pendingDeposit));
            if(string.IsNullOrWhiteSpace(txid)) throw new ArgumentNullException(nameof(txid));
            if (amount <= 0) throw new ArgumentOutOfRangeException(nameof(amount), "Amount must be greater than zero.");
            if (createdAt == DateTime.MinValue) throw new ArgumentOutOfRangeException(nameof(createdAt), "Date cannot be empty.");
            if (domainId <= 0) throw new ArgumentOutOfRangeException(nameof(domainId), "Domain ID must be greater than zero.");

            Jar.ConfirmDeposit(itIsThePresent, createdAt, pendingDeposit, txid, amount, domainId);
            RecalculateAmount();
        }

        private int tankConsecutive = 0;
        internal int NextTankId()
        {
            return tankConsecutive + 1;
        }

        private int tankerConsecutive = 0;
        internal int NextTankerId()
        {
            return tankerConsecutive + 1;
        }


        public int CurrentVesion { get; private set; }
        internal int NextJarVersion()
        {
            return CurrentVesion + 1;
        }

        private int depositConsecutive = 0;
        internal int NextDepositId()
        {
            return depositConsecutive + 1;
        }

        private void RecalculateAmount()
        {
            Amount = Jar.AvailableAmount();// + _tanks.Sum(t => t.Amount);
        }

        internal void DelegateJar(bool itIsThePresent, DateTime now, int vesion, LegacyJar previousLegacyJar, List<Deposit> delegatedDeposits)
        {
            if (vesion <= 0) throw new ArgumentOutOfRangeException(nameof(vesion), "Version must be greater than zero.");
            if (previousLegacyJar == null) throw new ArgumentNullException(nameof(previousLegacyJar));
            if (delegatedDeposits == null) throw new ArgumentException("Delegated deposits cannot be null.", nameof(delegatedDeposits));

            if (vesion <= CurrentVesion) throw new InvalidOperationException($"Version {vesion} is not greater than the current version {CurrentVesion}.");

            var newJar = new Jar(this, Jar.Kind, vesion, previousLegacyJar, delegatedDeposits);
            Jar = newJar;
            CurrentVesion = vesion;

            RecalculateAmount();

            if (Integration.UseKafka)
            {
                using (KafkaMessagesBuffer buffer = new KafkaMessagesBuffer(itIsThePresent, Integration.Kafka.TopicForContainerEvents))
                {
                    string description = $"Jar for {Kind} V{CurrentVesion}";
                    CreatedJarMessage jarMessage = new CreatedJarMessage(CurrentVesion, Jar.Kind, description, now);
                    buffer.Send(jarMessage);
                }
            }
        }

        internal bool ExistTank(int tankId)
        {
            if (tankId <= 0) throw new ArgumentOutOfRangeException(nameof(tankId), "Tank ID must be greater than zero.");

            return _tanks.ContainsKey(tankId);
        }

        internal void AddOrUpdateTank(Tank result)
        {
            if (result == null) throw new ArgumentNullException(nameof(result));

            if (_tanks.TryGetValue(result.Id, out Tank foundTank))
            {
                _tanks[result.Id] = result;
            }
            else
            {
                _tanks.Add(result.Id, result);
                tankConsecutive = result.Id;
            }
        }

        internal Tank FindTank(int tankId)
        {
            if (tankId <= 0) throw new ArgumentNullException(nameof(tankId));

            if (_tanks.TryGetValue(tankId, out Tank foundTank))
            {
                return foundTank;
            }
            throw new GameEngineException($"Tank with id: {tankId} was not found in source kind {Kind}");
        }


        internal TankerPending CreateTanker(bool itIsThePresent, DateTime now, int tankerId, string name, IEnumerable<int> tankIds)
        {
            if (now == DateTime.MinValue) throw new ArgumentNullException(nameof(now), "Date cannot be empty.");
            if (tankerId <= 0) throw new ArgumentOutOfRangeException(nameof(tankerId), "Tanker ID must be greater than zero.");
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));
            if (tankIds == null || !tankIds.Any()) throw new ArgumentException("Tank IDs cannot be null or empty.", nameof(tankIds));

            if (tankIds.Any(tankId => !_tanks.ContainsKey(tankId))) throw new GameEngineException($"Some tanks with ids: {string.Join(", ", tankIds)} were not found in source kind {Kind}");

            List<TankReady> tankReadys = new List<TankReady>();

            foreach (var tankId in tankIds)
            {
                if (_tanks.TryGetValue(tankId, out Tank foundTank))
                {
                    if (foundTank is TankReady tankReady)
                    {
                        tankReadys.Add(tankReady);
                    }
                    else
                    {
                        throw new GameEngineException($"Tank with id: {tankId} is not ready.");
                    }
                }
            }

            var tanker = new TankerPending(tankerId, name, Kind, this, tankReadys);
            _tankers.Add(tankerId, tanker);

            if (Integration.UseKafka)
            {

                using (KafkaMessagesBuffer buffer = new KafkaMessagesBuffer(itIsThePresent, $"{Integration.Kafka.TopicForContainerEvents}"))
                {
                    CreatedTankerMessage createdTankMessage = new CreatedTankerMessage(
                        tankerId: tanker.Id,
                        kind: Kind,
                        description: $"Created Tanker {tanker.Name} - Jar version",
                        version:1,
                        createdAt: now
                    );
                    buffer.Send(createdTankMessage);
                }
            }

            if (itIsThePresent)
            {
                CreatedTankerEvent createdTankEvent = new CreatedTankerEvent(now, tankerId, tanker.Name);
                PlatformMonitor.GetInstance().WhenNewEvent(createdTankEvent);
            }
            return tanker;
        }

        internal void AddOrUpdateTanker(Tanker result)
        {
            if (result == null) throw new ArgumentNullException(nameof(result));

            if (_tankers.TryGetValue(result.Id, out Tanker foundTank))
            {
                _tankers[result.Id] = result;
            }
            else { 
                
                _tankers.Add(result.Id, result);
                tankerConsecutive = result.Id;
            }
        }

        internal bool ExistTanker(int tankerId)
        {
            if (tankerId <= 0) throw new ArgumentOutOfRangeException(nameof(tankerId), "Tanker ID must be greater than zero.");

            return _tankers.ContainsKey(tankerId);
        }

        internal Tanker FindTanker(int tankerId)
        {
            if (tankerId <= 0) throw new ArgumentNullException(nameof(tankerId));

            if (_tankers.TryGetValue(tankerId, out Tanker foundTanker))
            {
                return foundTanker;
            }
            throw new GameEngineException($"Tanker with id: {tankerId} was not found in source kind {Kind}");
        }
    }

}
