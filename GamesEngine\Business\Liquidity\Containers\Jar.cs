﻿using GamesEngine.Business.Liquidity.Transactions;
using GamesEngine.Finance;
using GamesEngine.RealTime;
using GamesEngine.RealTime.Events;
using GamesEngine.Settings;
using System;
using System.Collections.Generic;
using System.Linq;
using static GamesEngine.Business.Liquidity.Containers.Tank;

namespace GamesEngine.Business.Liquidity.Containers
{
    public class Jar : Container
    {
        private readonly DepositWrapper _deposits = new DepositWrapper();
        public LegacyJar PreviousLegacyJar { get; private set; }


        public Jar(Source source, string kind, int vesion) : base(kind)
        {
            if (source == null) throw new ArgumentNullException(nameof(source));
            Source = source;
            Version = vesion;
        }

        public Jar(Source source, string kind, int vesion, LegacyJar legacyJar, IEnumerable<Deposit> deposits) : base(kind) {

            if (source == null) throw new ArgumentNullException(nameof(source));
            if (string.IsNullOrWhiteSpace(kind)) throw new ArgumentException("Kind cannot be null or empty.", nameof(kind));
            if (vesion <= 0) throw new ArgumentException("Version cannot be less than or equal to 0.", nameof(vesion));
            if(deposits == null) throw new ArgumentException("Deposits cannot be null or empty.", nameof(deposits));

            Source = source;
            Version = vesion;
            PreviousLegacyJar = legacyJar;
            if (deposits.Count() != 0) this._deposits.Add(deposits);
        }

        public Source Source { get; private set; }

        public int Version { get; private set; }
        public string Address => $"JAR_{Kind}_V{Version}";

        public PendingDeposit RequestDeposit(bool itIsThePresent, DateTime now, int depositId, string address)
        {
            if (Version != Source.CurrentVesion) throw new GameEngineException($"This jar with version {Version} is not the latest version with {Source.CurrentVesion}. Please ensure you are using the most up-to-date jar version.");
            if(depositId <= 0) throw new GameEngineException("The deposit id is invalid or empty.");

            if (string.IsNullOrWhiteSpace(address))
                throw new GameEngineException("The address is invalid or empty.");

            if (_deposits.ExistAddress(address))
                throw new GameEngineException($"The address: {address} is already associated with an existing deposit.");

            if (now == DateTime.MinValue) throw new GameEngineException("The date is invalid or empty.");
        
            var result = _deposits.Add(depositId, address);
           

            if (itIsThePresent)
            {
                CreatedDepositEvent createdDepositEvent = new CreatedDepositEvent(now, result.Id, result.Address, result.Amount);
                PlatformMonitor.GetInstance().WhenNewEvent(createdDepositEvent);
            }

            //Rubicon Todo Preguntar a alvaro si la primera intencion de deposito va en 0
            return result;
        }

        internal ConfirmedDeposit ConfirmDeposit(bool itIsThePresent, DateTime createdAt, PendingDeposit pendingDeposit, string txid, decimal amount, int domainId)
        {
            if (Version != Source.CurrentVesion) throw new GameEngineException($"This jar with version {Version} is not the latest version with {Source.CurrentVesion}. Please ensure you are using the most up-to-date jar version.");
            if (domainId <= 0) throw new GameEngineException("The domain id is invalid or empty.");

            if (pendingDeposit == null) throw new GameEngineException("Pending deposit is null.");
            if (string.IsNullOrWhiteSpace(txid)) throw new GameEngineException("The transaction id is invalid");
            if(createdAt == DateTime.MinValue) throw new GameEngineException("The createdAt date is invalid or empty.");

            if (!_deposits.ExistId(pendingDeposit.Id)) throw new InvalidOperationException("Deposit Id does not exist.");

            if (_deposits.DepositById(pendingDeposit.Id) is not PendingDeposit) throw new GameEngineException($"The deposit is no longer a pending deposit.");

            var result = _deposits.Add(pendingDeposit, txid, amount, createdAt);

            UpdateAmount(amount);

            if (Integration.UseKafka)
            {
                using (KafkaMessagesBuffer buffer = new KafkaMessagesBuffer(itIsThePresent, Integration.Kafka.TopicForContainerEvents))
                {
                    ConfirmDepositMessage deposit = new ConfirmDepositMessage(
                        result.Id,
                        result.Address,
                        result.Amount,
                        result.CreatedAt,
                        txid,
                        Kind,
                        Version,
                        Address,
                        domainId
                    );
                    buffer.Send(deposit);
                }
            }

            if (itIsThePresent)
            {
                ConfirmedDepositEvent confirmedDepositEvent = new ConfirmedDepositEvent(createdAt, result.Id, result.Amount);
                PlatformMonitor.GetInstance().WhenNewEvent(confirmedDepositEvent);
            }

            return result;
        }

        public decimal MinimumAmount { get; private set; }

        public Tank FundTank(decimal amountForTank)
        {
            throw new NotImplementedException();
            if (amountForTank < MinimumAmount) throw new GameEngineException($"The request amount {amountForTank} for the tank is lower than the minimum amount {MinimumAmount}");
            //Rubicon: Todo aqui se deben extraer los depositos de cercanos al monto para crear y devolver un Tank
        }

        public decimal AvailableAmount()
        {
            decimal confirmedAmount = 0;
            IEnumerable<Transactions.ConfirmedDeposit> confirmedDeposits = _deposits.ConfirmedDeposits();
            if (confirmedDeposits == null) throw new GameEngineException("No deposits selected.");
            foreach (Transactions.ConfirmedDeposit confirmedDeposit in confirmedDeposits)
            {
                confirmedAmount += confirmedDeposit.Amount;
            }
            return confirmedAmount;
        }

        internal Deposit FindDepositById(int id)
        {
            if (id <= 0) throw new GameEngineException("The id is invalid or empty.");
            return _deposits[id];
        }


        internal IEnumerable<PendingDeposit> PendingDeposits()
        {
            return _deposits.PendingDeposits();
        }
        
        internal IEnumerable<Transactions.ConfirmedDeposit> ConfirmedDeposits()
        {
            return _deposits.ConfirmedDeposits();
        }


        public decimal EstimateNextTankAmount()
        {
            throw new NotImplementedException();
            //Rubicon: Todo, revisar hasta cuando se considera un deposito como reciente. retronar
            
            //var confirmed = _deposits.Where(d => d.Status == DepositStatus.Confirmed).ToList();
            //return confirmed.Any() ? confirmed.Average(d => d.Amount) : 0;
        }


        public TankReady CreateTank(bool itIsThePresent, DateTime now, int id, string name, int vesion, IEnumerable<int> selectedDeposits)
        {
            if (id <= 0) throw new GameEngineException("The tank id is invalid or empty.");
            if (selectedDeposits == null || !selectedDeposits.Any()) throw new GameEngineException("No deposits selected.");
            if (selectedDeposits.Any(deposit => deposit <= 0)) throw new GameEngineException("One or more deposits are null.");

            if (string.IsNullOrWhiteSpace(name)) throw new GameEngineException("The tank name cannot be null or empty.");
            if (now == DateTime.MinValue) throw new GameEngineException("The date is invalid or empty.");

            var confirmedDepositList = ConfirmedDeposits();
            List<ConfirmedDeposit> selectedDepositsList = new List<ConfirmedDeposit>();

            foreach (int depositId in selectedDeposits)
            {
                ConfirmedDeposit confirmedDeposit = confirmedDepositList.FirstOrDefault(x => x.Id == depositId);
                if (confirmedDeposit == null) throw new GameEngineException($"The deposit with id {depositId} does not exist.");
                selectedDepositsList.Add(confirmedDeposit);
            }
            if(selectedDepositsList.Count == 0) throw new GameEngineException("No deposits where found in selected.");

            return CreateTank(itIsThePresent, now, id, name, vesion, selectedDepositsList);
        }

        public TankReady CreateTank(bool itIsThePresent, DateTime now, int id, string name, int vesion, IEnumerable<ConfirmedDeposit> selectedDeposits)
        {
            if (selectedDeposits == null || !selectedDeposits.Any()) throw new GameEngineException("No deposits selected.");

            if (selectedDeposits.Any(deposit => deposit == null)) throw new GameEngineException("One or more deposits are null.");

            if (id <= 0) throw new GameEngineException("The tank id is invalid or empty.");
            if (string.IsNullOrWhiteSpace(name)) throw new GameEngineException("The tank name cannot be null or empty.");
            if (now == DateTime.MinValue) throw new GameEngineException("The date is invalid or empty.");

            List<ProposedDeposit> proposedDeposits = new List<ProposedDeposit>();

            PreviousLegacyJar = new LegacyJar(this);

            foreach (ConfirmedDeposit confirmedDeposit in selectedDeposits)
            {
                var proposedDeposit = new ProposedDeposit(confirmedDeposit);
                proposedDeposits.Add(proposedDeposit);
            }

            TankReady result = new TankReady(id, name,now, this, Kind, proposedDeposits);

            List<Deposit> delegatedDeposits = new List<Deposit>();
            foreach (Deposit deposit in Deposits)
            {
                bool delegateDeposit = !proposedDeposits.Any(x => x.Id == deposit.Id);
                if (delegateDeposit)
                {
                    delegatedDeposits.Add(deposit);
                }
            }

            Jar newDelegatedJar = new Jar(Source, Kind, vesion, PreviousLegacyJar, delegatedDeposits);
            Source.DelegateJar(itIsThePresent, now, vesion, PreviousLegacyJar, delegatedDeposits);

            if (Integration.UseKafka)
            {
               
                using (KafkaMessagesBuffer buffer = new KafkaMessagesBuffer(itIsThePresent, $"{Integration.Kafka.TopicForContainerEvents}"))
                {
                    CreatedTankMessage createdTankMessage = new CreatedTankMessage(
                        tankId: result.Id,
                        kind: Kind,
                        description: $"Created Tank {result.Name} - Jar version ${result.Version}",
                        jarVersion: result.Version,
                        createdAt: now,
                        depositIds: proposedDeposits.Select(x => x.Id).ToList()
                    );
                    buffer.Send(createdTankMessage);
                }
            }

            if (itIsThePresent)
            {
                CreatedTankEvent CreatedTankEvent = new CreatedTankEvent(now, result.Id, result.Name);
                PlatformMonitor.GetInstance().WhenNewEvent(CreatedTankEvent);
            }
            Source.AddOrUpdateTank(result);
            return result;
        }

        public TankReady CreateTank(bool itIsThePresent, DateTime now, int id, string name, int vesion)
        {
            if (id <= 0) throw new GameEngineException("The tank id is invalid or empty.");
            if (Source.ExistTank(id)) throw new GameEngineException("The tank id is already associated with an existing tank.");
            if (string.IsNullOrWhiteSpace(name)) throw new GameEngineException("The tank name cannot be null or empty.");
            if(now == DateTime.MinValue) throw new GameEngineException("The date is invalid or empty.");


            IEnumerable<ConfirmedDeposit> cofirmedDeposits = _deposits.ConfirmedDeposits();
            if (cofirmedDeposits.Count() == 0) throw new GameEngineException("Cannot create a tank without confirmed deposits.");

            List<ProposedDeposit> proposedDeposits = new List<ProposedDeposit>();
            if (cofirmedDeposits == null) throw new GameEngineException("No deposits selected.");

            PreviousLegacyJar = new LegacyJar(this);

            foreach (ConfirmedDeposit confirmedDeposit in cofirmedDeposits)
            {
                var proposedDeposit = new ProposedDeposit(confirmedDeposit);
                proposedDeposits.Add(proposedDeposit);
            }

            //el Tank sabe cual es su source.
            //Esto tambien sive para ver que sean del mismo afiliado y tambien mismo liquido por medio del source cuando se haga merge
            TankReady result = new TankReady(id, name, now, this, Kind, proposedDeposits);

            List<Deposit> delegatedDeposits = new List<Deposit>();
            foreach (Deposit deposit in Deposits)
            {
                bool delegateDeposit = !proposedDeposits.Any(x => x.Id == deposit.Id);
                if (delegateDeposit)
                {
                    delegatedDeposits.Add(deposit);
                }
            }
            Source.DelegateJar(itIsThePresent, now, vesion, PreviousLegacyJar, delegatedDeposits);


            if (Integration.UseKafka)
            {
                
                using (KafkaMessagesBuffer buffer = new KafkaMessagesBuffer(itIsThePresent, $"{Integration.Kafka.TopicForContainerEvents}"))
                {
                    CreatedTankMessage createdTankMessage = new CreatedTankMessage(
                        tankId: result.Id,
                        kind: Kind,
                        description: $"Created Tank {result.Name} - Jar version ${result.Version}",
                        jarVersion: result.Version,
                        createdAt: now,
                        depositIds: proposedDeposits.Select(x => x.Id).ToList()
                    );
                    buffer.Send(createdTankMessage);
                }
            }

            if (itIsThePresent)
            {
                CreatedTankEvent CreatedTankEvent = new CreatedTankEvent(now, result.Id, result.Name);
                PlatformMonitor.GetInstance().WhenNewEvent(CreatedTankEvent);
            }

            Source.AddOrUpdateTank(result);
            return result;
        }  
        
        public Tank OptimizedDeposit()
        {
            //Rubicon: Todo, Calcula es los mejores depósitos candidatos para ser movidos  a un Tank. Los Depósitos cumplen en monto
            throw new NotImplementedException();
        }

        public bool ExistDeposit(int depositId)
        {
            if (depositId <= 0) throw new GameEngineException("The deposit id is invalid or empty.");
            return _deposits.ExistId(depositId);
        }

        internal IEnumerable<Deposit> Deposits => _deposits.Values;

        private class DepositWrapper
        {
            private Dictionary<int, Deposit> _deposits;

            internal Deposit this[int id]
            {
                get => DepositById(id);
            }

            internal DepositWrapper()
            {
                _deposits = new Dictionary<int, Deposit>();
            }

            internal IEnumerable<Deposit> Values => _deposits.Values;

            internal void Add(IEnumerable<Deposit> deposits)
            {
                if(deposits == null || deposits.Count() == 0)
                    throw new ArgumentException("Deposits cannot be null or empty.", nameof(deposits));

                foreach (var deposit in deposits)
                {
                    if (deposit == null) throw new GameEngineException("Deposit is null.");
                    if (_deposits.ContainsKey(deposit.Id)) throw new GameEngineException("The deposit is already associated with an existing deposit.");
                    _deposits.Add(deposit.Id, deposit);
                }

            }

            internal bool ExistAddress(string address)
            {
                if (string.IsNullOrEmpty(address))
                    throw new GameEngineException("The address is invalid or empty.");
                return _deposits.Values.Any((x) => x.Address == address);
            }

            internal bool ExistId(int id)
            {
                return _deposits.Keys.Any(x => x == id);
            }

            internal PendingDeposit Add(int depositId, string address)
            {
                if(depositId <= 0) throw new GameEngineException("The deposit id is invalid or empty.");
                if (_deposits.Values.Any((x) => x.Address == address)) throw new GameEngineException("The address is already associated with an existing deposit.");
                
                var pendingDeposit = new PendingDeposit(0, address, depositId);//

                _deposits.Add(depositId, pendingDeposit);
                return pendingDeposit;
            }

            internal Transactions.ConfirmedDeposit Add(PendingDeposit pendingDeposit, string transactionId, decimal amount, DateTime createdAt)
            {
                if (!_deposits.Keys.Any(x => x == pendingDeposit.Id)) throw new GameEngineException("The pending deposit does not existing in this Jar.");

                var confirmedDeposit = new Transactions.ConfirmedDeposit(pendingDeposit, transactionId, amount, createdAt);
                
                if (_deposits.ContainsKey(pendingDeposit.Id))
                {
                    _deposits[pendingDeposit.Id] = confirmedDeposit;
                }
                else
                {
                    _deposits.Add(pendingDeposit.Id, pendingDeposit);
                }

                return confirmedDeposit;
            }

            internal Deposit DepositById(int id)
            {
                if (id <= 0) throw new GameEngineException("The id is invalid or empty.");

                if (!_deposits.TryGetValue(id, out var deposit)) throw new GameEngineException("The deposit is not found.");
                if (deposit == null) throw new GameEngineException("The deposit is not found.");

                return deposit;
            }

            private List<PendingDeposit> pendingResults = new List<PendingDeposit>();
            internal IEnumerable<PendingDeposit> PendingDeposits()
            {
                pendingResults.Clear();
                foreach (var deposit in _deposits.Values)
                {
                    if(deposit is PendingDeposit pendingDeposit)
                    {
                        pendingResults.Add(pendingDeposit);
                    }
                }
                return pendingResults;
            }

            private List<ConfirmedDeposit> confirmedResults = new List<ConfirmedDeposit>();
            internal IEnumerable<ConfirmedDeposit> ConfirmedDeposits()
            {
                confirmedResults.Clear();
                foreach (var deposit in _deposits.Values)
                {
                    if (deposit is ConfirmedDeposit confirmedDeposit)
                    {
                        confirmedResults.Add(confirmedDeposit);
                    }
                }
                return confirmedResults;
            }

        }
    }


}
