using ExchangeAPI.ExchangeEntities;
using GamesEngine.Bets;
using GamesEngine.Domains;
using GamesEngine.Games.Lotto;
using GamesEngine.PurchaseOrders;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using static GamesEngine.Bets.PresetBetAmounts;

namespace GamesEngine.Exchange
{
    abstract class RiskProfiles: Objeto
    {
        internal const string DefaultRiskProfileName = "Preset Risk Profile";
        protected Dictionary<Domain, RiskProfile> Profiles { get; } = new Dictionary<Domain, RiskProfile>();
        internal RiskProfile DefaultRiskProfile { get; private protected set; }
        internal IEnumerable<RiskProfile> GetAll => Profiles.Values.Distinct().ToList();

        internal RiskProfile GetRiskProfile(Domain domain)
        {
            if (domain == null) throw new ArgumentNullException(nameof(domain));
            if (!Profiles.ContainsKey(domain)) return DefaultRiskProfile;
            return Profiles[domain];
        }

        internal RiskProfile GetRiskProfile(string name)
        {
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));

            if (DefaultRiskProfile.Name.Equals(name, StringComparison.OrdinalIgnoreCase)) return DefaultRiskProfile;

            var result = Profiles.Values.FirstOrDefault(riskProfile => riskProfile.Name.Equals(name, StringComparison.OrdinalIgnoreCase));
            if (result == null) throw new GameEngineException($"Risk profile {name} not found");
            return result;
        }

        abstract internal RiskProfile CreateRiskProfile(Domain domain, string name);
        abstract internal RiskProfile CreateRiskProfile(RiskProfile riskProfileOriginal, Domain domainToClone, string name);

        internal bool ExistsProfileName(string name)
        {
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));

            return Profiles.Values.Any(riskProfile => riskProfile.Name.Equals(name, StringComparison.OrdinalIgnoreCase));
        }

        internal bool HasProfile(Domain domain)
        {
            if (domain == null) throw new ArgumentNullException(nameof(domain));

            return Profiles.ContainsKey(domain);
        }

        internal void AssignDomain(Domain domain, RiskProfile riskProfileTarget)
        {
            if (domain == null) throw new ArgumentNullException(nameof(domain));
            if (riskProfileTarget == null) throw new ArgumentNullException(nameof(riskProfileTarget));
            if (riskProfileTarget == DefaultRiskProfile) throw new GameEngineException("Cannot move domain to default risk profile");
            if (riskProfileTarget.ContainsDomain(domain)) throw new GameEngineException($"Domain {domain.Url} already exists in risk profile {riskProfileTarget.Name}");

            if (Profiles.ContainsKey(domain))
            {
                var riskProfileOrigin = Profiles[domain];
                riskProfileOrigin.RemoveDomain(domain);
            }
            riskProfileTarget.AddDomain(domain);
            Profiles[domain] = riskProfileTarget;
        }

    }

    abstract class RiskProfile : Objeto
    {
        protected List<BetRanges> betRangesPerGameType = new List<BetRanges>();
        internal IEnumerable<BetRanges> BetRangesPerGameType 
        {
            get
            {
                return betRangesPerGameType;
            }
        }

        protected List<PresetBetAmounts> presetBetAmountsPerGameType = new List<PresetBetAmounts>();
        internal IEnumerable<PresetBetAmounts> PresetBetAmountsPerGameType 
        {
            get
            {
                return presetBetAmountsPerGameType;
            }
        }

        internal Prizes Prizes { get; private protected set; }
        internal Risks Risks { get; private protected set; }
        internal int DaysForwardToSell { get; private set; }

        List<Domain> domains = new List<Domain>();
        internal IEnumerable<Domain> Domains
        {
            get
            {
                return domains;
            }
        }

        string name;
        internal string Name 
        { 
            get
            {
                return name;
            }
            set 
            {
                if (string.IsNullOrWhiteSpace(value)) throw new ArgumentNullException(nameof(value));
                if (value.Equals(RiskProfiles.DefaultRiskProfileName, StringComparison.OrdinalIgnoreCase)) throw new GameEngineException("Cannot change name to default risk profile");
                if (riskProfiles.ExistsProfileName(value)) throw new GameEngineException($"Risk profile {value} already exists");

                name = value;
            }
        }
        RiskProfiles riskProfiles;
        internal RiskProfile(RiskProfiles riskProfiles, Domain domain, string name)
        {
            if (riskProfiles == null) throw new ArgumentNullException(nameof(riskProfiles));
            if (domain == null) throw new ArgumentNullException(nameof(domain));
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));
            if (riskProfiles.ExistsProfileName(name)) throw new GameEngineException($"Risk profile {name} already exists");

            this.riskProfiles = riskProfiles;
            domains.Add(domain);
            this.name = name;
        }
        
        protected RiskProfile(IEnumerable<Domain> domains, IEnumerable<BetRanges> betRangesPerGameType, IEnumerable<PresetBetAmounts> presetBetAmountsPerGameType, Prizes prizesTable, Risks risks, int daysForwardToSell, string name)
        {
            this.domains.AddRange(domains);
            this.betRangesPerGameType.AddRange(betRangesPerGameType);
            this.presetBetAmountsPerGameType.AddRange(presetBetAmountsPerGameType);
            Prizes = prizesTable;
            Risks = risks;
            DaysForwardToSell = daysForwardToSell;
            this.name = name;
        }

        internal void CloneFrom(RiskProfile riskProfile, Domain domain)
        {
            if (riskProfile == null) throw new ArgumentNullException(nameof(riskProfile));
            if (domain == null) throw new ArgumentNullException(nameof(domain));

            betRangesPerGameType.Clear();
            foreach (var betRanges in riskProfile.BetRangesPerGameType)
            {
                var newBetRanges = new BetRanges();
                for (int index = betRanges.Count - 1; index >= 0; index--)
                {
                    var betRange = betRanges.GetAll.ElementAt(index);
                    if (Domains.Contains(betRange.Domain))
                    {
                        newBetRanges.Add(betRange);
                        betRanges.Remove(betRange);
                    }
                }
                betRangesPerGameType.Add(newBetRanges);
            }

            presetBetAmountsPerGameType.Clear();
            foreach (var presetBetAmounts in riskProfile.PresetBetAmountsPerGameType)
            {
                var newPresetBetAmounts = new PresetBetAmounts();
                foreach (var presetBetAmount in presetBetAmounts.GetAll)
                {
                    newPresetBetAmounts.Add(presetBetAmount.Amount, presetBetAmount.WhoEnteredLastChange, presetBetAmount.DateLastChange);
                    if (!presetBetAmount.Enabled) newPresetBetAmounts.Disable(presetBetAmount.Amount, presetBetAmount.WhoEnteredLastChange, presetBetAmount.DateLastChange);
                }
                presetBetAmountsPerGameType.Add(newPresetBetAmounts);
            }

            Prizes.CloneFrom(riskProfile.Prizes);
            Risks.CloneFrom(riskProfile.Risks, domain);
            DaysForwardToSell = riskProfile.DaysForwardToSell;
        }

        internal void UpdateDaysForwardToSell(int daysForwardToSell, string employeeName, DateTime now)
        {
            if (daysForwardToSell <= 0) throw new GameEngineException($"{nameof(daysForwardToSell)} must be greater than 0");
            if (string.IsNullOrWhiteSpace(employeeName)) throw new ArgumentNullException(nameof(employeeName));
            if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");

            DaysForwardToSell = daysForwardToSell;
        }

        internal void RemoveDomain(Domain domain)
        {
            if (domain == null) throw new ArgumentNullException(nameof(domain));

            domains.Remove(domain);
        }

        internal void AddDomain(Domain domain)
        {
            if (domain == null) throw new ArgumentNullException(nameof(domain));

            domains.Add(domain);
        }

        internal bool ContainsDomain(Domain domain)
        {
            if (domain == null) throw new ArgumentNullException(nameof(domain));

            return Domains.Contains(domain);
        }
    }

    internal class RiskProfilesLotteries : RiskProfiles
    {
        PicksLotteryGame Lotteries { get; }
        internal RiskProfilesLotteries(PicksLotteryGame picksLotteryGame)
        {
            if (picksLotteryGame == null) throw new ArgumentNullException(nameof(picksLotteryGame));

            Lotteries = picksLotteryGame;
            DefaultRiskProfile = new RiskProfileLotteries(this,
                picksLotteryGame.Company.Sales.AllDomains,
                new BetRanges[] { picksLotteryGame.BetRangesByPick(2), picksLotteryGame.BetRangesByPick(3), picksLotteryGame.BetRangesByPick(4), picksLotteryGame.BetRangesByPick(5) },
                new PresetBetAmounts[] { picksLotteryGame.PresetBetAmountsByPick(2), picksLotteryGame.PresetBetAmountsByPick(3), picksLotteryGame.PresetBetAmountsByPick(4), picksLotteryGame.PresetBetAmountsByPick(5) },
                picksLotteryGame.Prizes,
                picksLotteryGame.Risks,
                Store.MAX_DAYS_FROM_TODAY_TO_SELL_A_TICKET,
                DefaultRiskProfileName
                );
        }

        override internal RiskProfile CreateRiskProfile(Domain domain, string name)
        {
            if (domain == null) throw new ArgumentNullException(nameof(domain));
            if (Profiles.ContainsKey(domain)) throw new GameEngineException($"Domain {domain.Url} already exists in risk profiles");
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));
            if (ExistsProfileName(name)) throw new GameEngineException($"Risk profile {name} already exists");

            var riskProfile = new RiskProfileLotteries(this, domain, name);
            riskProfile.CloneFrom(DefaultRiskProfile, domain);
            Profiles.Add(domain, riskProfile);
            return riskProfile;
        }

        override internal RiskProfile CreateRiskProfile(RiskProfile riskProfileOriginal, Domain domainOfClone, string name)
        {
            if (riskProfileOriginal == null) throw new ArgumentNullException(nameof(riskProfileOriginal));
            if (domainOfClone == null) throw new ArgumentNullException(nameof(domainOfClone));
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));
            if (ExistsProfileName(name)) throw new GameEngineException($"Risk profile {name} already exists");
            if (riskProfileOriginal == DefaultRiskProfile) throw new GameEngineException("Cannot clone default risk profile");
            if (!Profiles.ContainsKey(domainOfClone)) throw new GameEngineException($"Domain {domainOfClone.Url} must exist in risk profiles registered");
            if (!riskProfileOriginal.ContainsDomain(domainOfClone)) throw new GameEngineException($"Domain {domainOfClone.Url} must exist in risk profile {riskProfileOriginal.Name}");

            var riskProfileOfClone = new RiskProfileLotteries(this, domainOfClone, name);
            riskProfileOfClone.CloneFrom(riskProfileOriginal, domainOfClone);
            riskProfileOriginal.RemoveDomain(domainOfClone);
            
            Profiles[domainOfClone] = riskProfileOfClone;
            return riskProfileOfClone;
        }

        internal class RiskProfileLotteries : RiskProfile
        {
            RiskProfilesLotteries riskProfilesLotteries;
            internal RiskProfileLotteries(RiskProfilesLotteries riskProfilesLotteries, Domain domain, string name) : base(riskProfilesLotteries, domain, name)
            {
                if (riskProfilesLotteries == null) throw new ArgumentNullException(nameof(riskProfilesLotteries));

                this.riskProfilesLotteries = riskProfilesLotteries;
                Risks = new Risks();
                Prizes = new PrizesPicks();
            }
            internal RiskProfileLotteries(RiskProfilesLotteries riskProfilesLotteries, IEnumerable<Domain> domains, IEnumerable<BetRanges> betRangesPerGameType, IEnumerable<PresetBetAmounts> presetBetAmountsPerGameType, Prizes prizesTable, Risks risks, int daysForwardToSell, string name) : 
                base(domains, betRangesPerGameType, presetBetAmountsPerGameType, prizesTable, risks, daysForwardToSell, name)
            {
                this.riskProfilesLotteries = riskProfilesLotteries;
            }

            internal IEnumerable<BetRange> AllBetRangesByPick(int pick)
            {
                switch (pick)
                {
                    case 2:
                        return betRangesPerGameType[0].GetAll;
                    case 3:
                        return betRangesPerGameType[1].GetAll;
                    case 4:
                        return betRangesPerGameType[2].GetAll;
                    case 5:
                        return betRangesPerGameType[3].GetAll;
                    default:
                        throw new GameEngineException($"There is no {nameof(BetRanges)} implementation for {nameof(pick)} {pick}");
                }
            }

            internal void AddBetRanges(int pick, DateTime now, Domain domain, decimal maxAmount, decimal minAmount, string employeeName)
            {
                if (pick < 2 || pick > 5) throw new GameEngineException($"There is no {nameof(pick)} {pick}");
                if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");
                if (domain == null) throw new ArgumentNullException(nameof(domain));
                if (riskProfilesLotteries.DefaultRiskProfile != this && !Domains.Contains(domain)) throw new GameEngineException($"Domain {domain.Url} must exist in risk profile {Name}");
                if (maxAmount <= 0) throw new GameEngineException($"{nameof(maxAmount)} must be greater than 0");
                if (minAmount <= 0) throw new GameEngineException($"{nameof(minAmount)} must be greater than 0");
                if (minAmount > maxAmount) throw new GameEngineException($"{nameof(maxAmount)} must be greater than {nameof(minAmount)}");
                if (string.IsNullOrWhiteSpace(employeeName)) throw new ArgumentNullException(nameof(employeeName));

                var betRanges = betRangesPerGameType[pick - 2];
                betRanges.Add(now, domain, maxAmount, minAmount, employeeName);
            }

            internal void ClearBetRanges(int pick)
            {
                if (pick < 2 || pick > 5) throw new GameEngineException($"There is no {nameof(pick)} {pick}");

                var betRanges = betRangesPerGameType[pick - 2];
                betRanges.Clear();
            }

            internal IEnumerable<PresetBetAmount> AllPresetBetAmountsByPick(int pick)
            {
                switch (pick)
                {
                    case 2:
                        return presetBetAmountsPerGameType[0].GetAll;
                    case 3:
                        return presetBetAmountsPerGameType[1].GetAll;
                    case 4:
                        return presetBetAmountsPerGameType[2].GetAll;
                    case 5:
                        return presetBetAmountsPerGameType[3].GetAll;
                    default:
                        throw new GameEngineException($"There is no {nameof(BetRanges)} implementation for {nameof(pick)} {pick}");
                }
            }

            internal int CountPresetBetAmountsByPick(int pick)
            {
                switch (pick)
                {
                    case 2:
                        return presetBetAmountsPerGameType[0].Count;
                    case 3:
                        return presetBetAmountsPerGameType[1].Count;
                    case 4:
                        return presetBetAmountsPerGameType[2].Count;
                    case 5:
                        return presetBetAmountsPerGameType[3].Count;
                    default:
                        throw new GameEngineException($"There is no {nameof(BetRanges)} implementation for {nameof(pick)} {pick}");
                }
            }

            internal void AddPresetBetAmounts(int pick, decimal amount, string employeeName, DateTime now)
            {
                if (pick < 2 || pick > 5) throw new GameEngineException($"There is no implementation for {nameof(pick)} {pick}");
                if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");
                if (amount <= 0) throw new GameEngineException($"{nameof(amount)} must be greater than 0");
                if (string.IsNullOrWhiteSpace(employeeName)) throw new ArgumentNullException(nameof(employeeName));

                var presetBetAmounts = presetBetAmountsPerGameType[pick - 2];
                presetBetAmounts.Add(amount, employeeName, now);
            }

            internal void UpdatePresetBetAmounts(int pick, decimal currentAmount, decimal newAmount, string employeeName, DateTime now)
            {
                if (pick < 2 || pick > 5) throw new GameEngineException($"There is no implementation for {nameof(pick)} {pick}");
                if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");
                if (currentAmount <= 0) throw new GameEngineException($"{nameof(currentAmount)} must be greater than 0");
                if (newAmount <= 0) throw new GameEngineException($"{nameof(newAmount)} must be greater than 0");
                if (string.IsNullOrWhiteSpace(employeeName)) throw new ArgumentNullException(nameof(employeeName));

                var presetBetAmounts = presetBetAmountsPerGameType[pick - 2];
                presetBetAmounts.Update(currentAmount, newAmount, employeeName, now);
            }

            internal void EnablePresetBetAmounts(int pick, decimal amount, string employeeName, DateTime now)
            {
                if (pick < 2 || pick > 5) throw new GameEngineException($"There is no implementation for {nameof(pick)} {pick}");
                if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");
                if (amount <= 0) throw new GameEngineException($"{nameof(amount)} must be greater than 0");
                if (string.IsNullOrWhiteSpace(employeeName)) throw new ArgumentNullException(nameof(employeeName));

                var presetBetAmounts = presetBetAmountsPerGameType[pick - 2];
                presetBetAmounts.Enable(amount, employeeName, now);
            }

            internal void DisablePresetBetAmounts(int pick, decimal amount, string employeeName, DateTime now)
            {
                if (pick < 2 || pick > 5) throw new GameEngineException($"There is no implementation for {nameof(pick)} {pick}");
                if (now == default(DateTime)) throw new GameEngineException("Date cannot have default value");
                if (amount <= 0) throw new GameEngineException($"{nameof(amount)} must be greater than 0");
                if (string.IsNullOrWhiteSpace(employeeName)) throw new ArgumentNullException(nameof(employeeName));

                var presetBetAmounts = presetBetAmountsPerGameType[pick - 2];
                presetBetAmounts.Disable(amount, employeeName, now);
            }   
        }
    }

}
