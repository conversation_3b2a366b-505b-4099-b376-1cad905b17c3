﻿using Puppeteer.EventSourcing.Libraries;
using System;

namespace GamesEngine.Business.Liquidity.Containers
{
    public abstract class Container:Objeto
    {
        public decimal Amount { get; protected set; } 
        public string Kind { get; protected set; }

        protected Container(string kind)
        {
            if (string.IsNullOrWhiteSpace(kind)) throw new ArgumentException("Kind cannot be null or empty.", nameof(kind));

            Kind = kind;
            Amount = 0;
        }

        protected void UpdateAmount(decimal amountDelta)
        {
            Amount += amountDelta;
            NotifyChange();
        }

        protected void NotifyChange()
        {
            //Rubicon: TODO emitir una notificación a través de SignalR o el mecanismo de eventos configurado.

        }
    }


}
