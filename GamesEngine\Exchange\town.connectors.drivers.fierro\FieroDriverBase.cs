﻿using GamesEngine.Marketing;
using GamesEngine.Settings;
using log4net;
using System;

namespace town.connectors.drivers.fiero
{
    public abstract class FieroTenantDriver : TenantDriver
    {
		private const float VERSION = 1.0F;
		protected RestAPISpawnerActor actor;
		public FieroTenantDriver(Tenant_Actions tenantAction)
			: base(tenantAction, "Fiero", PaymentMethod.ThirdParty, new string [] { "USD", "FP", "BTC", "KRW", "KRWFP", SpinKick.SPR_CURRENCY_CODE }, VERSION)
		{
		}

        public FieroTenantDriver(Tenant_Actions tenantAction, TransactionType transactionType, string currencyCode)
            : base(tenantAction, transactionType, "Fiero", PaymentMethod.ThirdParty, new string[] { currencyCode }, VERSION)
        {
        }

        public override string Description => "Fiero driver";
		public override string Fabricator => "Ncubo";
		public override DateTime ReleaseDate => new DateTime(2019,01,01);

	}
	public abstract class FierroProcessorDriver : ProcessorDriver
	{
		public FierroProcessorDriver(TransactionType transactionType, float version) 
			: base("Fiero", PaymentMethod.ThirdParty, new string[] { "USD", "FP", "BTC", "KRW", "KRWFP", SpinKick.SPR_CURRENCY_CODE }, transactionType, version)
		{
			
		}

        public FierroProcessorDriver(TransactionType transactionType, float version, string currencyIsoCode)
            : base("Fiero", PaymentMethod.ThirdParty, currencyIsoCode, transactionType, version)
        {

        }

        public override string Description => "Fiero driver";
		public override string Fabricator => "Ncubo";
		public override DateTime ReleaseDate => new DateTime(2019, 01, 01);

	}
}
