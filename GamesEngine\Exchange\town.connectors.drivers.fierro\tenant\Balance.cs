﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using town.connectors.drivers.artemis;

namespace town.connectors.drivers.fiero
{
	internal abstract class Balance : FieroTenantDriver
	{


        public Balance(string currencyCode) : base(Tenant_Actions.Balance, TransactionType.RetrieveBalance, currencyCode)
		{
		}

        public override async Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet)
        {
            var atAddress = recordSet.Mappings["atAddress"].As<string>();

            if (CurrencyIsoCodes.Length > 1) throw new NotImplementedException("Drives must have only one currency code");
            string currencyCode = CurrencyIsoCodes.FirstOrDefault();


            var result = await AvailableBalanceAsync(atAddress, currencyCode);
            if (!result.Success) throw new Exception($"Error in Cashier: {result.Message}");

            if (typeof(T) == typeof(decimal))
            {
                return (T)Convert.ChangeType(result.Available, typeof(T));
            }
            else if (typeof(T) == typeof(PlayerBalanceBody))
            {
                PlayerBalanceBody playerBalanceBody = ExtendResponse(result.Available);
                return (T)Convert.ChangeType(playerBalanceBody, typeof(T));
            }
            else
            {
                throw new NotImplementedException($"Type {typeof(T)} not implemented");
            }
        }

        private PlayerBalanceBody ExtendResponse(decimal amount)
        {
            return new PlayerBalanceBody()
            {
                idPlayer = 0,
                player = string.Empty,
                curentBalance = 0,
                availBalance = 0,
                amountAtRisk=0,
                realAvailBalance = 0,
                freePlayAmount = 0,
                creditLimit = 0,
                thisWeek = 0,
                lastWeek = 0,
                bonusPoints = 0,
                idAgent = 0,
                agent = string.Empty,
                betTypeLimit = new List<string>()
            };
        }

        public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet)
        {
            throw new NotImplementedException();
        }

        public override void Prepare(DateTime now)
        {
            CustomSettings.AddVariableParameter("atAddress");
            //CustomSettings.Prepare();

        }

        protected abstract Task<BalanceResponse> AvailableBalanceAsync(string atAddress, string currencyCode);

        protected class BalanceResponse
        {
            public decimal Available { get; set; }
            public bool Success { get; set; }
            public string Message { get; set; }
        }

       
    }
}
