﻿using GamesEngine;
using GamesEngine.Business;
using GamesEngine.Finance;
using GamesEngine.Settings;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Threading.Tasks;
using town.connectors.drivers;
using town.connectors.drivers.hades;
using static ExchangeAPI.Controllers.APIController;
using static GamesEngine.Business.WholePaymentProcessor;

namespace ExchangeAPI.Controllers
{
    public class WalletController: AuthorizeController
    {
		[HttpPost("api/wallet/drafts/deposit")]
		[Authorize(Roles = "c5,player")]
		public async Task<IActionResult> AddWalletDepositDraftAsync([FromBody] WalletDepositCreationBody body)
		{
			if (body == null) return BadRequest("Body is required");
			if (body.Amount <= 0) return BadRequest($"{nameof(body.Amount)} must be greater than 0");
			if (body.EntityId <= 0) return BadRequest($"{nameof(body.EntityId)} must be greater than 0");
			if (string.IsNullOrWhiteSpace(body.ToCurrencyCode)) return BadRequest($"{nameof(body.ToCurrencyCode)} is required");
			if (string.IsNullOrWhiteSpace(body.ToIdentifier)) return BadRequest($"{nameof(body.ToIdentifier)} is required");
			if (!body.PaymentMethod.HasValue) return BadRequest($"{nameof(body.PaymentMethod)} is required");

			string path = "CR/Cartago"; //TODO: batch is fixed and it should be dynamic
            string employeeName = "FW";
            string domain = HttpContext.Request.Host.Host;
			string depositor = (body.Depositor == null) ? "" : body.Depositor;
			string voucher = (body.Voucher == null) ? "" : body.Voucher;
			string voucherUrl = (body.VoucherUrl == null) ? "" : body.VoucherUrl;

			var accountCommand = string.IsNullOrWhiteSpace(body.AccountNumber) ?
					$"account = customer.FindAccountByCurrency('{body.ToCurrencyCode}');" :
					$"account = customer.FindAccount('{body.AccountNumber}');";

			var result = await ExchangeManagerAPI.ExchangeManager.PerformChkThenCmdAsync(HttpContext, $@"
				existsCustomer = company.ExistsCustomerByIdentifier('{body.ToIdentifier}');
                Check(existsCustomer) Error 'Customer {body.ToIdentifier} does not exist';
				if (existsCustomer) 
				{{
					customer = company.CustomerByIdentifier('{body.ToIdentifier}');
					Check(customer.IsApproved()) Error 'Customer {body.ToIdentifier} is not approved';
				}}
				domain = company.Sales.DomainFrom('{domain}');
				isTransactionAllowed = domain.AllowedTransactions.ItsEnabled({TransactionType.Deposit});
				Check(isTransactionAllowed) Error 'Transaction {TransactionType.Deposit} is not enabled';
				existsDomainInMarketplace = company.Sales.CurrentStore.ExistsDomain(domain.Id);
				Check(existsDomainInMarketplace) Error 'Domain {domain} does not belong to marketplace';
			",
			$@"
			{{
				domain = company.Sales.DomainFrom('{domain}');
				customer = company.CustomerByIdentifier('{body.ToIdentifier}');
				{accountCommand}
				Eval('transactionNumber =' + marketplace.NewTransationNumber() + ';');

				drivers = company.System.DriverManagers.PaymentProcessorsAndActionsByDomains.SearchForX(domain.AgentId, domain.Url);
				processor = drivers.SearchByX({TransactionType.Deposit}, '{body.ToCurrencyCode}', {body.PaymentMethod.Value}, {body.EntityId});

				processorAccountId = guardian.Accounts().SearchByProcessor(processor.ProcessorKey).Id;
				transaction = marketplace.From(transactionNumber, account, '{path}', '{employeeName}', domain).Deposit(Now, itIsThePresent, Currency('{body.ToCurrencyCode}',{body.Amount}), '{employeeName}', '{depositor}', '{voucher}', '{voucherUrl}', '{body.Description}', processor, processorAccountId);
				print transactionNumber authorizationId;
				print transaction.BatchTransactions.TransactionsNumber batchNumber;
				print Now now;
			}}
			");

			if (!(result is OkObjectResult))
			{
				return result;
			}

			var o = (OkObjectResult)result;
			var json = o.Value.ToString();
			var idResponse = JsonConvert.DeserializeObject<WalletTransactionResponse>(json);

			return Ok(idResponse);
		}

		[HttpPost("api/wallet/drafts/withdrawal")]
		[Authorize(Roles = "c9,player")]
		public async Task<IActionResult> AddWalletWithdrawalDraftAsync([FromBody] WalletWithdrawalCreationBody body)
		{
			if (body == null) return BadRequest("Body is required");
			if (body.Amount <= 0) return BadRequest($"{nameof(body.Amount)} must be greater than 0");
			if (body.MinerFee < 0) return BadRequest($"{nameof(body.MinerFee)} must be greater or equal than 0");
			if (body.EntityId <= 0) return BadRequest($"{nameof(body.EntityId)} must be greater than 0");
			if (string.IsNullOrWhiteSpace(body.FromCurrencyCode)) return BadRequest($"{nameof(body.FromCurrencyCode)} is required");
			if (string.IsNullOrWhiteSpace(body.FromIdentifier)) return BadRequest($"{nameof(body.FromIdentifier)} is required");
			if (!body.PaymentMethod.HasValue) return BadRequest($"{nameof(body.PaymentMethod)} is required");

			string path = "CR/Cartago"; //TODO: batch is fixed and it should be dynamic
            string employeeName = "FW";
            string domain = HttpContext.Request.Host.Host;

			string accountCommand = string.IsNullOrWhiteSpace(body.accountNumber) ?
				$"account = customer.FindAccountByCurrency('{body.FromCurrencyCode}');" :
				$"account = customer.FindAccount('{body.accountNumber}');";

			var result = await ExchangeManagerAPI.ExchangeManager.PerformChkThenCmdAsync(HttpContext, $@"
				existsCustomer = company.ExistsCustomerByIdentifier('{body.FromIdentifier}');
                Check(existsCustomer) Error 'Customer {body.FromIdentifier} does not exist';
				if (existsCustomer) 
				{{
					customer = company.CustomerByIdentifier('{body.FromIdentifier}');
					Check(customer.IsApproved()) Error 'Customer {body.FromIdentifier} is not approved';
				}}
				domain = company.Sales.DomainFrom('{domain}');
				isTransactionAllowed = domain.AllowedTransactions.ItsEnabled({TransactionType.Withdrawal});
				Check(isTransactionAllowed) Error 'Transaction {TransactionType.Withdrawal} is not enabled';
				existsDomainInMarketplace = company.Sales.CurrentStore.ExistsDomain(domain.Id);
				Check(existsDomainInMarketplace) Error 'Domain {domain} does not belong to marketplace';
			",
			$@"
			{{
				domain = company.Sales.DomainFrom('{domain}');
				customer = company.CustomerByIdentifier('{body.FromIdentifier}');
				{accountCommand}
				Eval('transactionNumber =' + marketplace.NewTransationNumber() + ';');

				drivers = company.System.DriverManagers.PaymentProcessorsAndActionsByDomains.SearchForX(domain.AgentId, domain.Url);
				processor = drivers.SearchByX({TransactionType.Withdrawal}, '{body.FromCurrencyCode}', {body.PaymentMethod.Value}, {body.EntityId});
				
				processorAccount = guardian.Accounts().SearchByProcessor(processor.ProcessorKey);
				transaction = marketplace.From(transactionNumber, account, '{path}', '{employeeName}', domain);
				print transaction.Id transactionId;
				print account.Identificator account;
				print customer.AccountNumber customerNumber;
				print processorAccount.Id processorAccountId;
				print company.Sales.CurrentStore.Id storeId;
				print domain.AgentId agentId;
				print domain.Url domainUrl;
				print domain.Id domainId;
			}}
			");
			if (!(result is OkObjectResult))
			{
				return result;
			}
			var o = (OkObjectResult)result;
			var json = o.Value.ToString();
			var tempTransactionResponse = JsonConvert.DeserializeObject<TempTransactionResponse>(json);


            var useless = DateTime.Now.AddDays(DaysToBecomeUseless);
            var authorizationNumberFromLockBalance = await PaymentChannels.LockBalanceAsync(
				tempTransactionResponse.CustomerNumber,
				body.Amount,
				body.FromCurrencyCode,
				body.Description,
				tempTransactionResponse.TransactionId.ToString(),
				tempTransactionResponse.Account,
				employeeName,
				tempTransactionResponse.StoreId,
                useless,
                tempTransactionResponse.AgentId,
                tempTransactionResponse.DomainUrl,
                tempTransactionResponse.DomainId,
                body.PaymentMethod.Value,
                body.EntityId
            );

			if (authorizationNumberFromLockBalance == ASITenantDriver.FAKE_TICKET_NUMBER)
			{
				await ExchangeManagerAPI.ExchangeManager.PerformCmdAsync(HttpContext, $@"
				{{
					transaction = marketplace.SearchTemporaryDefinition({tempTransactionResponse.TransactionId}, '{path}', '{employeeName}').Remove();
				}}
				");
			}

			var feeScript = body.MinerFee == 0 ? $"NoFee('{body.FromCurrencyCode}')" : $"MinerFee(Currency('{body.FromCurrencyCode}',{body.MinerFee}))";
			string realAccount = body.RealAccount;
			var errorMessage = "Sorry, your withdrawal cannot be completed at this moment. Please try again. If the problem persists, please contact customer service.";
			result = await ExchangeManagerAPI.ExchangeManager.PerformChkThenCmdAsync(HttpContext, $@"
					Check({authorizationNumberFromLockBalance} != {ASITenantDriver.FAKE_TICKET_NUMBER}) Error '{errorMessage}';
				", $@"
				{{
					domain = company.Sales.DomainFrom('{domain}');
					drivers = company.System.DriverManagers.PaymentProcessorsAndActionsByDomains.SearchForX(domain.AgentId, domain.Url);
					processor = drivers.SearchByX({TransactionType.Withdrawal}, '{body.FromCurrencyCode}', {body.PaymentMethod.Value}, {body.EntityId});

					processorAccountId = guardian.Accounts().SearchByProcessor(processor.ProcessorKey).Id;
					transaction = marketplace.SearchTemporaryDefinition({tempTransactionResponse.TransactionId}, '{path}', '{employeeName}').Withdraw(Now, itIsThePresent, Currency('{body.FromCurrencyCode}',{body.Amount}), {authorizationNumberFromLockBalance}, '{employeeName}', '{realAccount}', '{body.Description}', {feeScript}, processor, processorAccountId);
					
					print transactionNumber authorizationId;
					print transaction.BatchTransactions.TransactionsNumber batchNumber;
					print Now now;
				}}");
			if (!(result is OkObjectResult))
			{
				return result;
			}
			o = (OkObjectResult)result;
			json = o.Value.ToString();
			var idResponse = JsonConvert.DeserializeObject<WalletTransactionResponse>(json);

			return Ok(idResponse);
		}

        [HttpPost("api/wallet/drafts/transfer")]
        [Authorize(Roles = "c13,player")]
        public async Task<IActionResult> AddTransferDraftAsync([FromBody] WalletTransferCreationBody body)
        {
            if (body == null) return BadRequest("Body is required");
            if (body.Amount <= 0) return BadRequest($"{nameof(body.Amount)} must be greater than 0");
            if (body.EntityId <= 0) return BadRequest($"{nameof(body.EntityId)} must be greater than 0");
            //if (!body.PaymentMethod.HasValue) return BadRequest($"{nameof(body.PaymentMethod)} is required");
            if (string.IsNullOrWhiteSpace(body.FromCurrencyCode)) return BadRequest($"{nameof(body.FromCurrencyCode)} is required");
            if (string.IsNullOrWhiteSpace(body.ToCurrencyCode)) return BadRequest($"{nameof(body.ToCurrencyCode)} is required");
            if (string.IsNullOrWhiteSpace(body.FromIdentifier)) return BadRequest($"{nameof(body.FromIdentifier)} is required");
            if (string.IsNullOrWhiteSpace(body.ToIdentifier)) return BadRequest($"{nameof(body.ToIdentifier)} is required");
            if (string.IsNullOrWhiteSpace(body.RealAccount)) body.RealAccount = "";

            string path = "CR/Cartago"; //TODO: batch is fixed and it should be dynamic
            string employeeName = "FW";
            string batchPath = path + '/' + employeeName;
            string domain = HttpContext.Request.Host.Host;


            string accountCommand = string.IsNullOrWhiteSpace(body.FromAccountNumber) ?
                $"account = customer.FindAccountByCurrency('{body.FromCurrencyCode}');" :
                $"account = customer.FindAccount('{body.FromAccountNumber}');";

            var result = await ExchangeManagerAPI.ExchangeManager.PerformChkThenCmdAsync(HttpContext, $@"
					existPaymentProcessor = company.System.DriverManagers.ExistPaymentProcessor({TransactionType.Transfer}, '{body.FromCurrencyCode}', {body.EntityId}, {body.PaymentMethod.Value});
					Check(existPaymentProcessor) Error 'Payment processor does not exist';

					existsCustomer = company.ExistsCustomerByIdentifier('{body.FromIdentifier}');
					Check(existsCustomer) Error 'Customer {body.FromIdentifier} does not exist';
					if (existsCustomer) 
					{{
						customer = company.CustomerByIdentifier('{body.FromIdentifier}');
						Check(customer.IsApproved()) Error 'Customer {body.FromIdentifier} is not approved';
					}}

					domain = company.Sales.DomainFrom('{domain}');
					isTransactionAllowed = domain.AllowedTransactions.ItsEnabled({TransactionType.Transfer});
					Check(isTransactionAllowed) Error 'Transaction {TransactionType.Transfer} is not enabled';
					existsDomainInMarketplace = company.Sales.CurrentStore.ExistsDomain(domain.Id);
					Check(existsDomainInMarketplace) Error 'Domain {domain} does not belong to marketplace';

					existsBatch = marketplace.Batches.ExistAgentBatch('{batchPath}');
					Check(existsBatch) Error 'Agent batch {batchPath} does not exist.';
					if (existsBatch)
					{{
						agentBatch = marketplace.SearchAgentBatch('{batchPath}');
						Check(agentBatch.BatchTransactions.HasAvailable(Currency('{body.FromCurrencyCode}',{body.Amount}))) Error 'Agent batch {batchPath} does not have enough funds for {body.FromCurrencyCode}{body.Amount}.';
					}}

					existsAgent = marketplace.ExistsAgent('{path}');
					Check(existsAgent) Error 'Agent {path} does not exist.';
					if (existsAgent)
					{{
						agent = marketplace.SearchAgent('{path}');
						user = agent.SearchUser('{employeeName}');
						Check(user.IsInRange(Currency('{body.FromCurrencyCode}',{body.Amount}))) Error 'Agent {path} does not have enough funds for {body.FromCurrencyCode}{body.Amount}.';
					}}
				", $@"
				{{
					domain = company.Sales.DomainFrom('{domain}');
					customer = company.CustomerByIdentifier('{body.FromIdentifier}');
					{accountCommand}
					Eval('transactionNumber =' + marketplace.NewTransationNumber() + ';');
					
					drivers = company.System.DriverManagers.PaymentProcessorsAndActionsByDomains.SearchForX(domain.AgentId, domain.Url);
					processor = drivers.SearchByX({TransactionType.Transfer}, '{body.FromCurrencyCode}', {body.PaymentMethod.Value}, {body.EntityId});

					transaction = marketplace.From(transactionNumber, account, '{path}', '{employeeName}', domain);
					print transaction.Id transactionId;
					print account.Identificator account;
					processorAccount = guardian.Accounts().SearchByProcessor(processor.ProcessorKey);
					print processorAccount.Id processorAccountId;
					print customer.AccountNumber customerNumber;
					print company.Sales.CurrentStore.Id storeId;
					print domain.AgentId agentId;
					print domain.Url domainUrl;
					print domain.Id domainId;
					print transaction.Processor.PaymentMethodAsString paymentMethodType;
				}}
			");
            if (!(result is OkObjectResult))
            {
                return result;
            }
            var o = (OkObjectResult)result;
            var json = o.Value.ToString();
            TempTransactionResponse tempTransactionResponse = JsonConvert.DeserializeObject<TempTransactionResponse>(json);

            var useless = DateTime.Now.AddDays(DaysToBecomeUseless);
            var authorizationNumberFromLockBalance = await PaymentChannels.LockBalanceAsync(
                tempTransactionResponse.CustomerNumber,
                body.Amount,
                body.FromCurrencyCode,
                body.Description,
                tempTransactionResponse.TransactionId.ToString(),
                tempTransactionResponse.Account,
                employeeName,
                tempTransactionResponse.StoreId,
				useless,
                tempTransactionResponse.AgentId,
                tempTransactionResponse.DomainUrl,
                tempTransactionResponse.DomainId,
                tempTransactionResponse.PaymentMethodType.Value,
                body.EntityId
            );

            if (authorizationNumberFromLockBalance == ASITenantDriver.FAKE_TICKET_NUMBER)
            {
                await ExchangeManagerAPI.ExchangeManager.PerformCmdAsync(HttpContext, $@"
					{{
						transaction = marketplace.SearchTemporaryDefinition({tempTransactionResponse.TransactionId}, '{path}', '{employeeName}').Remove();
					}}
					");
            }

            accountCommand = string.IsNullOrWhiteSpace(body.ToAccountNumber) ?
                $"toAccount = targetCustomer.FindAccountByCurrency('{body.ToCurrencyCode}');" :
                $"toAccount = targetCustomer.FindAccount('{body.ToAccountNumber}');";

            var errorMessage = "Sorry, your transfer cannot be completed at this moment. Please try again. If the problem persists, please contact customer service.";
            result = await ExchangeManagerAPI.ExchangeManager.PerformChkThenCmdAsync(HttpContext, $@"
					Check({authorizationNumberFromLockBalance} != {ASITenantDriver.FAKE_TICKET_NUMBER}) Error '{errorMessage}';
				", $@"
				{{	
					targetCustomer = company.CustomerByIdentifier('{body.ToIdentifier}');
					{accountCommand}
					
					domain = company.Sales.DomainFrom('{domain}');

					drivers = company.System.DriverManagers.PaymentProcessorsAndActionsByDomains.SearchForX(domain.AgentId, domain.Url);
					processor = drivers.SearchByX({TransactionType.Transfer}, '{body.FromCurrencyCode}', {body.PaymentMethod.Value}, {body.EntityId});

					processorAccountId = guardian.Accounts().SearchBy(processor).Id;
					transaction = marketplace.SearchTemporaryDefinition({tempTransactionResponse.TransactionId}, '{path}', '{employeeName}').TransferTo(Now, itIsThePresent, Currency('{body.FromCurrencyCode}',{body.Amount}), {authorizationNumberFromLockBalance}, toAccount, '{employeeName}', '{body.RealAccount}', '{body.Description}', processor, processorAccountId);
					print transactionNumber authorizationId;
					print transaction.BatchTransactions.TransactionsNumber batchNumber;
					print Now now;
				}}
			");
            if (!(result is OkObjectResult))
            {
                return result;
            }
            o = (OkObjectResult)result;
            json = o.Value.ToString();
            DraftTransactionResponse idResponse = JsonConvert.DeserializeObject<DraftTransactionResponse>(json);

            return Ok(idResponse);
        }

        [DataContract(Name = "WalletDepositCreationBody")]
		public class WalletDepositCreationBody
		{
			[DataMember(Name = "toCurrencyCode")]
			public string ToCurrencyCode { get; set; }
			[DataMember(Name = "amount")]
			public decimal Amount { get; set; }
			[DataMember(Name = "toIdentifier")]
			public string ToIdentifier { get; set; }
			[DataMember(Name = "accountNumber")]
			public string AccountNumber { get; set; }
			[DataMember(Name = "description")]
			public string Description { get; set; }
			[DataMember(Name = "voucher")]
			public string Voucher { get; set; }
			[DataMember(Name = "voucherurl")]
			public string VoucherUrl { get; set; }
			[DataMember(Name = "depositor")]
			public string Depositor { get; set; }

			[DataMember(Name = "sendersName")]
			public string SendersName { get; set; }
			[DataMember(Name = "country")]
			public string Country { get; set; }
			[DataMember(Name = "state")]
			public string State { get; set; }
			[DataMember(Name = "city")]
			public string City { get; set; }
			[DataMember(Name = "controlNum")]
			public string ControlNum { get; set; }
			[DataMember(Name = "providerId")]
			public int ProviderId { get; set; }
			[DataMember(Name = "paymentMethod")]
			public PaymentMethod? PaymentMethod { get; set; }
			[DataMember(Name = "entityId")]
			public int EntityId { get; set; }
		}

		[DataContract(Name = "IdWalletDepositResponse")]
		public class WalletTransactionResponse
		{
			[DataMember(Name = "authorizationId")]
			public long AuthorizationId { get; set; }
			[DataMember(Name = "batchNumber")]
			public int BatchNumber { get; set; }
			[DataMember(Name = "now")]
			public string Now { get; set; }
		}

		[DataContract(Name = "WalletWithdrawalCreationBody")]
		public class WalletWithdrawalCreationBody
		{
			[DataMember(Name = "accountNumber")]
			public string accountNumber { get; set; }

			[DataMember(Name = "fromCurrencyCode")]
			public string FromCurrencyCode { get; set; }
			[DataMember(Name = "amount")]
			public decimal Amount { get; set; }
			[DataMember(Name = "fromIdentifier")]
			public string FromIdentifier { get; set; }
			[DataMember(Name = "description")]
			public string Description { get; set; }
			[DataMember(Name = "realAccount")]
			public string RealAccount { get; set; }
			[DataMember(Name = "minerFee")]
			public decimal MinerFee { get; set; }
			[DataMember(Name = "paymentMethod")]
			public PaymentMethod? PaymentMethod { get; set; }
			[DataMember(Name = "entityId")]
			public int EntityId { get; set; }
		}

        [DataContract(Name = "WalletTransferCreationBody")]
        public class WalletTransferCreationBody
        {

            [DataMember(Name = "toIdentifier")]
            public string ToIdentifier { get; set; }

            [DataMember(Name = "fromIdentifier")]
            public string FromIdentifier { get; set; }

            [DataMember(Name = "fromCurrencyCode")]
            public string FromCurrencyCode { get; set; }
            [DataMember(Name = "toCurrencyCode")]
            public string ToCurrencyCode { get; set; }
            [DataMember(Name = "amount")]
            public decimal Amount { get; set; }
            [DataMember(Name = "description")]
            public string Description { get; set; }
            [DataMember(Name = "realAccount")]
            public string RealAccount { get; set; }

            [DataMember(Name = "fromAccountNumber")]
            public string FromAccountNumber { get; set; }

            [DataMember(Name = "toAccountNumber")]
            public string ToAccountNumber { get; set; }
            [DataMember(Name = "paymentMethod")]
            public PaymentMethod? PaymentMethod { get; set; }
			[DataMember(Name = "entityId")]
			public int EntityId { get; set; }
        }
    }
}
