﻿using GamesEngine.Business.Liquidity.Transactions;
using System;
using System.Collections.Generic;
using System.Linq;

namespace GamesEngine.Business.Liquidity.Containers
{
    public abstract class Dispenser : Container
    {
        public int Id { get; private set; }

        public string Name { get; private set; }

        public string Address { get; private set; }

        public bool Enabled { get; private set; } = true;

        public DateTime StartDate { get; private set; }

        public Liquid Liquid { get; private set; }

        public Dispenser(int id, string name, Liquid liquid, string kind, string address, decimal amount, DateTime startDate) : base(kind)
        {
            if (id <= 0) throw new ArgumentException("Id must be greater than zero.", nameof(id));
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentException("Name cannot be null or empty.", nameof(name));
            if (liquid == null) throw new ArgumentNullException(nameof(liquid));
            if (string.IsNullOrWhiteSpace(address)) throw new ArgumentException("Address cannot be null or empty.", nameof(address));
            if (amount <= 0) throw new ArgumentException("Amount must be greater than zero.", nameof(amount));
            if (startDate == DateTime.MinValue) throw new ArgumentException("StartDate cannot be empty.", nameof(startDate));

            Id = id;
            Name = name;
            Liquid = liquid;
            Address = address;
            Amount = amount;
            StartDate = startDate;
        }

        public string Type => this.GetType().Name;

    }
    
    public class DispenserReady : Dispenser
    {
        public decimal AvailableAmount => Amount - _withdrawals.Sum(w => w.Amount);

        private List<Withdrawal> _withdrawals = new List<Withdrawal>();

        public DispenserReady(int id, string name, Liquid liquid, string kind, string address, decimal amount, DateTime startDate) : base(id, name, liquid, kind, address, amount, startDate)
        {
        }

        public DispenserDiscarded Discard(DateTime discardDate)
        {
            if (discardDate == DateTime.MinValue) throw new ArgumentException("Discard date cannot be empty.", nameof(discardDate));
            return new DispenserDiscarded(this, discardDate);
        }

        public decimal EstimateWithdrawalFee(decimal amount, TimeSpan allowedTime)
        {
            throw new NotImplementedException("This method should be implemented in the derived class.");
        }

        public Withdrawal Dispense(int withdrawalId, string addressToDispense, decimal amount, DateTime dispenseDate)
        {
            if (withdrawalId <= 0) throw new ArgumentException("Withdrawal ID must be greater than zero.", nameof(withdrawalId));
            if (string.IsNullOrWhiteSpace(addressToDispense)) throw new ArgumentException("Address to dispense cannot be null or empty.", nameof(addressToDispense));
            if (amount <= 0) throw new ArgumentException("Amount must be greater than zero.", nameof(amount));
            if (dispenseDate == DateTime.MinValue) throw new ArgumentException("Dispense date cannot be empty.", nameof(dispenseDate));
            if (amount > Amount) throw new ArgumentException("Insufficient amount in dispenser.", nameof(amount));

            if (dispenseDate < StartDate) throw new ArgumentException("Dispense date cannot be earlier than the start date.", nameof(dispenseDate));

            decimal currentAvailableAmount = Amount - _withdrawals.Sum(w => w.Amount);
            decimal remainingAmount = currentAvailableAmount - amount;
            if (remainingAmount == 0)
            {
                DispenserDiscarded discardThisDispenser = new DispenserDiscarded(this, dispenseDate);
                Liquid.AddOrUpdateDispenser(discardThisDispenser);
            }
            else if (remainingAmount < 0)
            {
                throw new ArgumentException("Insufficient amount in dispenser.", nameof(amount));
            }

            var result = new Withdrawal(withdrawalId, amount, dispenseDate);
            _withdrawals.Add(result);
            return result;
        }
    }

    public class DispenserDiscarded : Dispenser
    {
        private DispenserReady dispenserReady;

        public DateTime DiscardDate { get; private set; }

        public DispenserDiscarded(DispenserReady dispenserReady, DateTime discardDate) : base(dispenserReady.Id, dispenserReady.Name, dispenserReady.Liquid, dispenserReady.Kind, dispenserReady.Address, dispenserReady.Amount, dispenserReady.StartDate)
        {
            if (dispenserReady == null) throw new ArgumentNullException(nameof(dispenserReady));
            if (discardDate == DateTime.MinValue) throw new ArgumentException("Discard date cannot be empty.", nameof(discardDate));
            this.dispenserReady = dispenserReady;
            DiscardDate = discardDate;
        }
    }
}
